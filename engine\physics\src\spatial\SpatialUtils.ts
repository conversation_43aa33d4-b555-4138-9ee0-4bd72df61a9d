/**
 * DL-Engine 空间工具函数
 * 提供各种空间计算和几何工具函数
 */

import { PhysicsVector3, PhysicsQuaternion } from '../types/PhysicsTypes'

/**
 * 向量工具类
 */
export class VectorUtils {
  /**
   * 创建零向量
   */
  static zero(): PhysicsVector3 {
    return { x: 0, y: 0, z: 0 }
  }
  
  /**
   * 创建单位向量
   */
  static one(): PhysicsVector3 {
    return { x: 1, y: 1, z: 1 }
  }
  
  /**
   * 向量加法
   */
  static add(a: PhysicsVector3, b: PhysicsVector3): PhysicsVector3 {
    return {
      x: a.x + b.x,
      y: a.y + b.y,
      z: a.z + b.z
    }
  }
  
  /**
   * 向量减法
   */
  static subtract(a: PhysicsVector3, b: PhysicsVector3): PhysicsVector3 {
    return {
      x: a.x - b.x,
      y: a.y - b.y,
      z: a.z - b.z
    }
  }
  
  /**
   * 向量标量乘法
   */
  static multiply(v: PhysicsVector3, scalar: number): PhysicsVector3 {
    return {
      x: v.x * scalar,
      y: v.y * scalar,
      z: v.z * scalar
    }
  }
  
  /**
   * 向量除法
   */
  static divide(v: PhysicsVector3, scalar: number): PhysicsVector3 {
    if (Math.abs(scalar) < 1e-6) {
      throw new Error('Division by zero or near-zero value')
    }
    return {
      x: v.x / scalar,
      y: v.y / scalar,
      z: v.z / scalar
    }
  }
  
  /**
   * 向量点积
   */
  static dot(a: PhysicsVector3, b: PhysicsVector3): number {
    return a.x * b.x + a.y * b.y + a.z * b.z
  }
  
  /**
   * 向量叉积
   */
  static cross(a: PhysicsVector3, b: PhysicsVector3): PhysicsVector3 {
    return {
      x: a.y * b.z - a.z * b.y,
      y: a.z * b.x - a.x * b.z,
      z: a.x * b.y - a.y * b.x
    }
  }
  
  /**
   * 向量长度
   */
  static length(v: PhysicsVector3): number {
    return Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z)
  }
  
  /**
   * 向量长度的平方
   */
  static lengthSquared(v: PhysicsVector3): number {
    return v.x * v.x + v.y * v.y + v.z * v.z
  }
  
  /**
   * 向量归一化
   */
  static normalize(v: PhysicsVector3): PhysicsVector3 {
    const length = VectorUtils.length(v)
    if (length < 1e-6) {
      return { x: 0, y: 0, z: 0 }
    }
    return VectorUtils.divide(v, length)
  }
  
  /**
   * 向量距离
   */
  static distance(a: PhysicsVector3, b: PhysicsVector3): number {
    return VectorUtils.length(VectorUtils.subtract(a, b))
  }
  
  /**
   * 向量距离的平方
   */
  static distanceSquared(a: PhysicsVector3, b: PhysicsVector3): number {
    return VectorUtils.lengthSquared(VectorUtils.subtract(a, b))
  }
  
  /**
   * 向量线性插值
   */
  static lerp(a: PhysicsVector3, b: PhysicsVector3, t: number): PhysicsVector3 {
    return {
      x: a.x + (b.x - a.x) * t,
      y: a.y + (b.y - a.y) * t,
      z: a.z + (b.z - a.z) * t
    }
  }
  
  /**
   * 向量球面线性插值
   */
  static slerp(a: PhysicsVector3, b: PhysicsVector3, t: number): PhysicsVector3 {
    const dot = VectorUtils.dot(VectorUtils.normalize(a), VectorUtils.normalize(b))
    const theta = Math.acos(Math.max(-1, Math.min(1, dot)))
    
    if (Math.abs(theta) < 1e-6) {
      return VectorUtils.lerp(a, b, t)
    }
    
    const sinTheta = Math.sin(theta)
    const factor1 = Math.sin((1 - t) * theta) / sinTheta
    const factor2 = Math.sin(t * theta) / sinTheta
    
    return VectorUtils.add(
      VectorUtils.multiply(a, factor1),
      VectorUtils.multiply(b, factor2)
    )
  }
  
  /**
   * 向量反射
   */
  static reflect(incident: PhysicsVector3, normal: PhysicsVector3): PhysicsVector3 {
    const dot = VectorUtils.dot(incident, normal)
    return VectorUtils.subtract(incident, VectorUtils.multiply(normal, 2 * dot))
  }
  
  /**
   * 向量投影
   */
  static project(a: PhysicsVector3, b: PhysicsVector3): PhysicsVector3 {
    const dot = VectorUtils.dot(a, b)
    const lengthSq = VectorUtils.lengthSquared(b)
    if (lengthSq < 1e-6) {
      return VectorUtils.zero()
    }
    return VectorUtils.multiply(b, dot / lengthSq)
  }
  
  /**
   * 检查向量是否相等
   */
  static equals(a: PhysicsVector3, b: PhysicsVector3, epsilon: number = 1e-6): boolean {
    return Math.abs(a.x - b.x) < epsilon &&
           Math.abs(a.y - b.y) < epsilon &&
           Math.abs(a.z - b.z) < epsilon
  }
}

/**
 * 四元数工具类
 */
export class QuaternionUtils {
  /**
   * 创建单位四元数
   */
  static identity(): PhysicsQuaternion {
    return { x: 0, y: 0, z: 0, w: 1 }
  }
  
  /**
   * 从欧拉角创建四元数
   */
  static fromEuler(x: number, y: number, z: number): PhysicsQuaternion {
    const cx = Math.cos(x / 2)
    const cy = Math.cos(y / 2)
    const cz = Math.cos(z / 2)
    const sx = Math.sin(x / 2)
    const sy = Math.sin(y / 2)
    const sz = Math.sin(z / 2)
    
    return {
      x: sx * cy * cz - cx * sy * sz,
      y: cx * sy * cz + sx * cy * sz,
      z: cx * cy * sz - sx * sy * cz,
      w: cx * cy * cz + sx * sy * sz
    }
  }
  
  /**
   * 四元数乘法
   */
  static multiply(a: PhysicsQuaternion, b: PhysicsQuaternion): PhysicsQuaternion {
    return {
      x: a.w * b.x + a.x * b.w + a.y * b.z - a.z * b.y,
      y: a.w * b.y - a.x * b.z + a.y * b.w + a.z * b.x,
      z: a.w * b.z + a.x * b.y - a.y * b.x + a.z * b.w,
      w: a.w * b.w - a.x * b.x - a.y * b.y - a.z * b.z
    }
  }
  
  /**
   * 四元数归一化
   */
  static normalize(q: PhysicsQuaternion): PhysicsQuaternion {
    const length = Math.sqrt(q.x * q.x + q.y * q.y + q.z * q.z + q.w * q.w)
    if (length < 1e-6) {
      return QuaternionUtils.identity()
    }
    return {
      x: q.x / length,
      y: q.y / length,
      z: q.z / length,
      w: q.w / length
    }
  }
  
  /**
   * 四元数共轭
   */
  static conjugate(q: PhysicsQuaternion): PhysicsQuaternion {
    return {
      x: -q.x,
      y: -q.y,
      z: -q.z,
      w: q.w
    }
  }
  
  /**
   * 四元数球面线性插值
   */
  static slerp(a: PhysicsQuaternion, b: PhysicsQuaternion, t: number): PhysicsQuaternion {
    let dot = a.x * b.x + a.y * b.y + a.z * b.z + a.w * b.w
    
    // 如果点积为负，取反其中一个四元数以选择较短的路径
    if (dot < 0) {
      b = { x: -b.x, y: -b.y, z: -b.z, w: -b.w }
      dot = -dot
    }
    
    // 如果四元数非常接近，使用线性插值
    if (dot > 0.9995) {
      return QuaternionUtils.normalize({
        x: a.x + t * (b.x - a.x),
        y: a.y + t * (b.y - a.y),
        z: a.z + t * (b.z - a.z),
        w: a.w + t * (b.w - a.w)
      })
    }
    
    const theta = Math.acos(Math.abs(dot))
    const sinTheta = Math.sin(theta)
    const factor1 = Math.sin((1 - t) * theta) / sinTheta
    const factor2 = Math.sin(t * theta) / sinTheta
    
    return {
      x: a.x * factor1 + b.x * factor2,
      y: a.y * factor1 + b.y * factor2,
      z: a.z * factor1 + b.z * factor2,
      w: a.w * factor1 + b.w * factor2
    }
  }
}

/**
 * 几何工具类
 */
export class GeometryUtils {
  /**
   * 计算点到线段的最短距离
   */
  static pointToLineDistance(point: PhysicsVector3, lineStart: PhysicsVector3, lineEnd: PhysicsVector3): number {
    const line = VectorUtils.subtract(lineEnd, lineStart)
    const pointToStart = VectorUtils.subtract(point, lineStart)
    
    const lineLength = VectorUtils.length(line)
    if (lineLength < 1e-6) {
      return VectorUtils.distance(point, lineStart)
    }
    
    const t = Math.max(0, Math.min(1, VectorUtils.dot(pointToStart, line) / (lineLength * lineLength)))
    const projection = VectorUtils.add(lineStart, VectorUtils.multiply(line, t))
    
    return VectorUtils.distance(point, projection)
  }
  
  /**
   * 计算点到平面的距离
   */
  static pointToPlaneDistance(point: PhysicsVector3, planePoint: PhysicsVector3, planeNormal: PhysicsVector3): number {
    const pointToPlane = VectorUtils.subtract(point, planePoint)
    return Math.abs(VectorUtils.dot(pointToPlane, VectorUtils.normalize(planeNormal)))
  }
  
  /**
   * 检查点是否在三角形内
   */
  static pointInTriangle(point: PhysicsVector3, a: PhysicsVector3, b: PhysicsVector3, c: PhysicsVector3): boolean {
    const v0 = VectorUtils.subtract(c, a)
    const v1 = VectorUtils.subtract(b, a)
    const v2 = VectorUtils.subtract(point, a)
    
    const dot00 = VectorUtils.dot(v0, v0)
    const dot01 = VectorUtils.dot(v0, v1)
    const dot02 = VectorUtils.dot(v0, v2)
    const dot11 = VectorUtils.dot(v1, v1)
    const dot12 = VectorUtils.dot(v1, v2)
    
    const invDenom = 1 / (dot00 * dot11 - dot01 * dot01)
    const u = (dot11 * dot02 - dot01 * dot12) * invDenom
    const v = (dot00 * dot12 - dot01 * dot02) * invDenom
    
    return (u >= 0) && (v >= 0) && (u + v <= 1)
  }
  
  /**
   * 计算两个AABB的重叠体积
   */
  static aabbOverlapVolume(
    min1: PhysicsVector3, max1: PhysicsVector3,
    min2: PhysicsVector3, max2: PhysicsVector3
  ): number {
    const overlapMin = {
      x: Math.max(min1.x, min2.x),
      y: Math.max(min1.y, min2.y),
      z: Math.max(min1.z, min2.z)
    }
    
    const overlapMax = {
      x: Math.min(max1.x, max2.x),
      y: Math.min(max1.y, max2.y),
      z: Math.min(max1.z, max2.z)
    }
    
    if (overlapMin.x >= overlapMax.x || overlapMin.y >= overlapMax.y || overlapMin.z >= overlapMax.z) {
      return 0
    }
    
    return (overlapMax.x - overlapMin.x) * (overlapMax.y - overlapMin.y) * (overlapMax.z - overlapMin.z)
  }
  
  /**
   * 计算球体体积
   */
  static sphereVolume(radius: number): number {
    return (4 / 3) * Math.PI * radius * radius * radius
  }
  
  /**
   * 计算立方体体积
   */
  static boxVolume(size: PhysicsVector3): number {
    return size.x * size.y * size.z
  }
}

/**
 * 空间哈希工具
 */
export class SpatialHashUtils {
  /**
   * 计算3D位置的哈希值
   */
  static hash3D(x: number, y: number, z: number, cellSize: number): string {
    const cellX = Math.floor(x / cellSize)
    const cellY = Math.floor(y / cellSize)
    const cellZ = Math.floor(z / cellSize)
    return `${cellX},${cellY},${cellZ}`
  }
  
  /**
   * 获取位置周围的哈希键
   */
  static getNeighborHashes(position: PhysicsVector3, cellSize: number, radius: number): string[] {
    const hashes: string[] = []
    const cellRadius = Math.ceil(radius / cellSize)
    
    const centerX = Math.floor(position.x / cellSize)
    const centerY = Math.floor(position.y / cellSize)
    const centerZ = Math.floor(position.z / cellSize)
    
    for (let x = centerX - cellRadius; x <= centerX + cellRadius; x++) {
      for (let y = centerY - cellRadius; y <= centerY + cellRadius; y++) {
        for (let z = centerZ - cellRadius; z <= centerZ + cellRadius; z++) {
          hashes.push(`${x},${y},${z}`)
        }
      }
    }
    
    return hashes
  }
}

/**
 * 常用常量
 */
export const SpatialConstants = {
  /** 浮点数比较精度 */
  EPSILON: 1e-6,
  
  /** 重力加速度 */
  GRAVITY: 9.81,
  
  /** 弧度转角度 */
  RAD_TO_DEG: 180 / Math.PI,
  
  /** 角度转弧度 */
  DEG_TO_RAD: Math.PI / 180,
  
  /** 常用方向向量 */
  DIRECTIONS: {
    UP: { x: 0, y: 1, z: 0 },
    DOWN: { x: 0, y: -1, z: 0 },
    LEFT: { x: -1, y: 0, z: 0 },
    RIGHT: { x: 1, y: 0, z: 0 },
    FORWARD: { x: 0, y: 0, z: -1 },
    BACKWARD: { x: 0, y: 0, z: 1 }
  }
}
