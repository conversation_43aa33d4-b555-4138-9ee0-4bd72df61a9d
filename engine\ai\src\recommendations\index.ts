/**
 * DL-Engine 推荐系统模块导出
 * 提供完整的智能推荐功能
 */

// 核心推荐引擎
export * from './RecommendationEngine'

// 内容推荐器
export * from './ContentRecommender'

// 协同过滤算法
export * from './CollaborativeFiltering'

// 推荐服务
export * from './RecommendationService'

// 推荐系统工厂类
export class RecommendationSystemFactory {
  /**
   * 创建默认推荐引擎
   */
  static createDefaultEngine() {
    return new (require('./RecommendationEngine').RecommendationEngine)()
  }

  /**
   * 创建内容推荐器
   */
  static createContentRecommender() {
    return new (require('./ContentRecommender').ContentRecommender)()
  }

  /**
   * 创建协同过滤推荐器
   */
  static createCollaborativeFiltering() {
    return new (require('./CollaborativeFiltering').CollaborativeFiltering)()
  }

  /**
   * 创建推荐服务
   */
  static createRecommendationService(config?: any) {
    return new (require('./RecommendationService').RecommendationService)(config)
  }
}
