/**
 * DL-Engine 资产状态管理
 * 管理资产的全局状态
 */

import { defineState, getMutableState, getState } from '@dl-engine/engine-state'
import { Asset, AssetType, AssetStatus, AssetStats, AssetEvent } from './AssetTypes'

/**
 * 资产状态类型
 */
export interface AssetStateType {
  /** 所有资产的映射 */
  assets: Map<string, Asset>
  
  /** 加载队列 */
  loadingQueue: string[]
  
  /** 当前正在加载的资产 */
  currentlyLoading: Set<string>
  
  /** 缓存的资产ID */
  cachedAssets: Set<string>
  
  /** 资产统计信息 */
  stats: AssetStats
  
  /** 最大并发加载数 */
  maxConcurrentLoads: number
  
  /** 缓存大小限制（字节） */
  maxCacheSize: number
  
  /** 是否启用自动缓存清理 */
  autoCleanup: boolean
  
  /** 缓存清理间隔（毫秒） */
  cleanupInterval: number
  
  /** 事件历史 */
  eventHistory: AssetEvent[]
  
  /** 最大事件历史数量 */
  maxEventHistory: number
}

/**
 * 默认资产统计信息
 */
const defaultStats: AssetStats = {
  totalAssets: 0,
  assetsByType: {
    [AssetType.MODEL]: 0,
    [AssetType.TEXTURE]: 0,
    [AssetType.MATERIAL]: 0,
    [AssetType.GEOMETRY]: 0,
    [AssetType.AUDIO]: 0,
    [AssetType.VIDEO]: 0,
    [AssetType.IMAGE]: 0,
    [AssetType.ANIMATION]: 0,
    [AssetType.SCENE]: 0,
    [AssetType.FONT]: 0,
    [AssetType.SHADER]: 0,
    [AssetType.SCRIPT]: 0,
    [AssetType.DATA]: 0
  },
  assetsByStatus: {
    [AssetStatus.UNLOADED]: 0,
    [AssetStatus.LOADING]: 0,
    [AssetStatus.LOADED]: 0,
    [AssetStatus.ERROR]: 0,
    [AssetStatus.CACHED]: 0
  },
  totalSize: 0,
  cacheSize: 0,
  cacheHitRate: 0,
  averageLoadTime: 0,
  memoryUsage: {
    textures: 0,
    geometries: 0,
    materials: 0,
    audio: 0,
    total: 0
  }
}

/**
 * 资产状态定义
 */
export const AssetState = defineState({
  name: 'DLEngine.Asset',
  initial: () => ({
    assets: new Map<string, Asset>(),
    loadingQueue: [],
    currentlyLoading: new Set<string>(),
    cachedAssets: new Set<string>(),
    stats: { ...defaultStats },
    maxConcurrentLoads: 4,
    maxCacheSize: 512 * 1024 * 1024, // 512MB
    autoCleanup: true,
    cleanupInterval: 60000, // 1分钟
    eventHistory: [],
    maxEventHistory: 1000
  } as AssetStateType)
})

/**
 * 资产状态工具函数
 */
export const AssetStateUtils = {
  /**
   * 添加资产
   */
  addAsset: (asset: Asset) => {
    const state = getMutableState(AssetState)
    const assets = new Map(state.assets.value)
    assets.set(asset.id, asset)
    state.assets.set(assets)
    
    // 更新统计信息
    AssetStateUtils.updateStats()
  },

  /**
   * 移除资产
   */
  removeAsset: (assetId: string) => {
    const state = getMutableState(AssetState)
    const assets = new Map(state.assets.value)
    assets.delete(assetId)
    state.assets.set(assets)
    
    // 从缓存中移除
    const cachedAssets = new Set(state.cachedAssets.value)
    cachedAssets.delete(assetId)
    state.cachedAssets.set(cachedAssets)
    
    // 从加载队列中移除
    const loadingQueue = state.loadingQueue.value.filter(id => id !== assetId)
    state.loadingQueue.set(loadingQueue)
    
    // 从当前加载中移除
    const currentlyLoading = new Set(state.currentlyLoading.value)
    currentlyLoading.delete(assetId)
    state.currentlyLoading.set(currentlyLoading)
    
    // 更新统计信息
    AssetStateUtils.updateStats()
  },

  /**
   * 更新资产
   */
  updateAsset: (assetId: string, updates: Partial<Asset>) => {
    const state = getMutableState(AssetState)
    const assets = new Map(state.assets.value)
    const asset = assets.get(assetId)
    
    if (asset) {
      const updatedAsset = { ...asset, ...updates }
      assets.set(assetId, updatedAsset)
      state.assets.set(assets)
      
      // 更新统计信息
      AssetStateUtils.updateStats()
    }
  },

  /**
   * 获取资产
   */
  getAsset: (assetId: string): Asset | undefined => {
    const state = getState(AssetState)
    return state.assets.get(assetId)
  },

  /**
   * 获取所有资产
   */
  getAllAssets: (): Asset[] => {
    const state = getState(AssetState)
    return Array.from(state.assets.values())
  },

  /**
   * 添加到加载队列
   */
  addToLoadingQueue: (assetId: string) => {
    const state = getMutableState(AssetState)
    const queue = [...state.loadingQueue.value]
    if (!queue.includes(assetId)) {
      queue.push(assetId)
      state.loadingQueue.set(queue)
    }
  },

  /**
   * 从加载队列移除
   */
  removeFromLoadingQueue: (assetId: string) => {
    const state = getMutableState(AssetState)
    const queue = state.loadingQueue.value.filter(id => id !== assetId)
    state.loadingQueue.set(queue)
  },

  /**
   * 标记为正在加载
   */
  markAsLoading: (assetId: string) => {
    const state = getMutableState(AssetState)
    const loading = new Set(state.currentlyLoading.value)
    loading.add(assetId)
    state.currentlyLoading.set(loading)
  },

  /**
   * 标记加载完成
   */
  markAsLoaded: (assetId: string) => {
    const state = getMutableState(AssetState)
    const loading = new Set(state.currentlyLoading.value)
    loading.delete(assetId)
    state.currentlyLoading.set(loading)
  },

  /**
   * 添加到缓存
   */
  addToCache: (assetId: string) => {
    const state = getMutableState(AssetState)
    const cached = new Set(state.cachedAssets.value)
    cached.add(assetId)
    state.cachedAssets.set(cached)
  },

  /**
   * 从缓存移除
   */
  removeFromCache: (assetId: string) => {
    const state = getMutableState(AssetState)
    const cached = new Set(state.cachedAssets.value)
    cached.delete(assetId)
    state.cachedAssets.set(cached)
  },

  /**
   * 添加事件
   */
  addEvent: (event: AssetEvent) => {
    const state = getMutableState(AssetState)
    const history = [...state.eventHistory.value, event]
    
    // 限制历史记录数量
    if (history.length > state.maxEventHistory.value) {
      history.splice(0, history.length - state.maxEventHistory.value)
    }
    
    state.eventHistory.set(history)
  },

  /**
   * 更新统计信息
   */
  updateStats: () => {
    const state = getMutableState(AssetState)
    const assets = Array.from(state.assets.value.values())
    
    const stats: AssetStats = {
      totalAssets: assets.length,
      assetsByType: { ...defaultStats.assetsByType },
      assetsByStatus: { ...defaultStats.assetsByStatus },
      totalSize: 0,
      cacheSize: 0,
      cacheHitRate: 0,
      averageLoadTime: 0,
      memoryUsage: {
        textures: 0,
        geometries: 0,
        materials: 0,
        audio: 0,
        total: 0
      }
    }
    
    // 计算统计信息
    for (const asset of assets) {
      stats.assetsByType[asset.type]++
      stats.assetsByStatus[asset.status]++
      stats.totalSize += asset.size
      
      if (state.cachedAssets.value.has(asset.id)) {
        stats.cacheSize += asset.size
      }
      
      // 计算内存使用
      switch (asset.type) {
        case AssetType.TEXTURE:
        case AssetType.IMAGE:
          stats.memoryUsage.textures += asset.size
          break
        case AssetType.GEOMETRY:
          stats.memoryUsage.geometries += asset.size
          break
        case AssetType.MATERIAL:
          stats.memoryUsage.materials += asset.size
          break
        case AssetType.AUDIO:
          stats.memoryUsage.audio += asset.size
          break
      }
    }
    
    stats.memoryUsage.total = 
      stats.memoryUsage.textures + 
      stats.memoryUsage.geometries + 
      stats.memoryUsage.materials + 
      stats.memoryUsage.audio
    
    // 计算缓存命中率
    if (stats.totalAssets > 0) {
      stats.cacheHitRate = state.cachedAssets.value.size / stats.totalAssets
    }
    
    state.stats.set(stats)
  },

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache: () => {
    const state = getMutableState(AssetState)
    const now = Date.now()
    const assetsToRemove: string[] = []
    
    for (const [assetId, asset] of state.assets.value) {
      if (state.cachedAssets.value.has(assetId)) {
        const timeSinceAccess = now - asset.lastAccessedAt.getTime()
        if (timeSinceAccess > asset.cacheTTL) {
          assetsToRemove.push(assetId)
        }
      }
    }
    
    // 移除过期资产
    for (const assetId of assetsToRemove) {
      AssetStateUtils.removeFromCache(assetId)
    }
    
    // 检查缓存大小限制
    AssetStateUtils.enforceMaxCacheSize()
  },

  /**
   * 强制执行最大缓存大小
   */
  enforceMaxCacheSize: () => {
    const state = getMutableState(AssetState)
    const maxSize = state.maxCacheSize.value
    let currentSize = 0
    
    // 计算当前缓存大小
    for (const assetId of state.cachedAssets.value) {
      const asset = state.assets.value.get(assetId)
      if (asset) {
        currentSize += asset.size
      }
    }
    
    if (currentSize > maxSize) {
      // 按最后访问时间排序，移除最旧的资产
      const cachedAssets = Array.from(state.cachedAssets.value)
        .map(id => state.assets.value.get(id))
        .filter(asset => asset !== undefined)
        .sort((a, b) => a!.lastAccessedAt.getTime() - b!.lastAccessedAt.getTime())
      
      for (const asset of cachedAssets) {
        if (currentSize <= maxSize) break
        
        AssetStateUtils.removeFromCache(asset!.id)
        currentSize -= asset!.size
      }
    }
  }
}
