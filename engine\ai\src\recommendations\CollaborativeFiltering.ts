/**
 * DL-Engine 协同过滤推荐算法实现
 * 基于用户行为相似性进行推荐
 */

import { Entity } from '@etherealengine/ecs'

/**
 * 用户行为数据
 */
export interface UserBehavior {
  /** 用户ID */
  userId: Entity
  
  /** 项目ID */
  itemId: string
  
  /** 行为类型 */
  actionType: 'view' | 'like' | 'dislike' | 'complete' | 'bookmark' | 'share' | 'rate'
  
  /** 行为值（如评分、观看时长等） */
  value: number
  
  /** 行为时间 */
  timestamp: Date
  
  /** 上下文信息 */
  context?: {
    /** 设备类型 */
    deviceType?: string
    
    /** 会话ID */
    sessionId?: string
    
    /** 来源页面 */
    referrer?: string
    
    /** 额外元数据 */
    metadata?: Record<string, any>
  }
}

/**
 * 用户相似度
 */
export interface UserSimilarity {
  /** 用户1 ID */
  userId1: Entity
  
  /** 用户2 ID */
  userId2: Entity
  
  /** 相似度分数 (0-1) */
  similarity: number
  
  /** 共同项目数量 */
  commonItems: number
  
  /** 计算方法 */
  method: 'cosine' | 'pearson' | 'jaccard' | 'euclidean'
  
  /** 计算时间 */
  calculatedAt: Date
}

/**
 * 项目相似度
 */
export interface ItemSimilarity {
  /** 项目1 ID */
  itemId1: string
  
  /** 项目2 ID */
  itemId2: string
  
  /** 相似度分数 (0-1) */
  similarity: number
  
  /** 共同用户数量 */
  commonUsers: number
  
  /** 计算方法 */
  method: 'cosine' | 'pearson' | 'jaccard' | 'euclidean'
  
  /** 计算时间 */
  calculatedAt: Date
}

/**
 * 协同过滤配置
 */
export interface CollaborativeFilteringConfig {
  /** 相似度计算方法 */
  similarityMethod: 'cosine' | 'pearson' | 'jaccard' | 'euclidean'
  
  /** 最小共同项目数 */
  minCommonItems: number
  
  /** 最大邻居数量 */
  maxNeighbors: number
  
  /** 相似度阈值 */
  similarityThreshold: number
  
  /** 是否使用基于用户的协同过滤 */
  useUserBased: boolean
  
  /** 是否使用基于项目的协同过滤 */
  useItemBased: boolean
  
  /** 时间衰减因子 */
  timeDecayFactor: number
  
  /** 缓存过期时间（小时） */
  cacheExpirationHours: number
}

/**
 * 协同过滤推荐器
 */
export class CollaborativeFiltering {
  private config: CollaborativeFilteringConfig
  private userBehaviors = new Map<string, UserBehavior[]>()
  private itemBehaviors = new Map<string, UserBehavior[]>()
  private userSimilarities = new Map<string, UserSimilarity[]>()
  private itemSimilarities = new Map<string, ItemSimilarity[]>()
  private userItemMatrix = new Map<string, Map<string, number>>()
  
  constructor(config: Partial<CollaborativeFilteringConfig> = {}) {
    this.config = {
      similarityMethod: 'cosine',
      minCommonItems: 3,
      maxNeighbors: 50,
      similarityThreshold: 0.1,
      useUserBased: true,
      useItemBased: true,
      timeDecayFactor: 0.95,
      cacheExpirationHours: 24,
      ...config
    }
  }
  
  /**
   * 添加用户行为数据
   */
  addUserBehavior(behavior: UserBehavior): void {
    const userKey = behavior.userId.toString()
    const itemKey = behavior.itemId
    
    // 添加到用户行为记录
    if (!this.userBehaviors.has(userKey)) {
      this.userBehaviors.set(userKey, [])
    }
    this.userBehaviors.get(userKey)!.push(behavior)
    
    // 添加到项目行为记录
    if (!this.itemBehaviors.has(itemKey)) {
      this.itemBehaviors.set(itemKey, [])
    }
    this.itemBehaviors.get(itemKey)!.push(behavior)
    
    // 更新用户-项目矩阵
    this.updateUserItemMatrix(behavior)
    
    // 清除相关缓存
    this.clearSimilarityCache(userKey, itemKey)
  }
  
  /**
   * 批量添加用户行为数据
   */
  addUserBehaviors(behaviors: UserBehavior[]): void {
    for (const behavior of behaviors) {
      this.addUserBehavior(behavior)
    }
  }
  
  /**
   * 基于用户的协同过滤推荐
   */
  async getUserBasedRecommendations(
    userId: Entity,
    limit: number = 10
  ): Promise<Array<{ itemId: string; score: number; reason: string }>> {
    const userKey = userId.toString()
    
    // 获取用户相似度
    const similarities = await this.getUserSimilarities(userId)
    
    // 获取推荐候选
    const candidates = new Map<string, { score: number; contributors: string[] }>()
    
    for (const similarity of similarities.slice(0, this.config.maxNeighbors)) {
      const neighborId = similarity.userId2.toString()
      const neighborBehaviors = this.userBehaviors.get(neighborId) || []
      
      for (const behavior of neighborBehaviors) {
        // 跳过用户已经交互过的项目
        if (this.hasUserInteracted(userId, behavior.itemId)) {
          continue
        }
        
        const weightedScore = behavior.value * similarity.similarity
        const timeDecay = this.calculateTimeDecay(behavior.timestamp)
        const finalScore = weightedScore * timeDecay
        
        if (!candidates.has(behavior.itemId)) {
          candidates.set(behavior.itemId, { score: 0, contributors: [] })
        }
        
        const candidate = candidates.get(behavior.itemId)!
        candidate.score += finalScore
        candidate.contributors.push(neighborId)
      }
    }
    
    // 排序并返回结果
    const recommendations = Array.from(candidates.entries())
      .map(([itemId, data]) => ({
        itemId,
        score: data.score / data.contributors.length, // 平均分数
        reason: `基于 ${data.contributors.length} 个相似用户的偏好`
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
    
    return recommendations
  }
  
  /**
   * 基于项目的协同过滤推荐
   */
  async getItemBasedRecommendations(
    userId: Entity,
    limit: number = 10
  ): Promise<Array<{ itemId: string; score: number; reason: string }>> {
    const userKey = userId.toString()
    const userBehaviors = this.userBehaviors.get(userKey) || []
    
    if (userBehaviors.length === 0) {
      return []
    }
    
    const candidates = new Map<string, { score: number; contributors: string[] }>()
    
    // 基于用户历史行为的项目找相似项目
    for (const behavior of userBehaviors) {
      const similarities = await this.getItemSimilarities(behavior.itemId)
      
      for (const similarity of similarities.slice(0, this.config.maxNeighbors)) {
        const candidateItemId = similarity.itemId2
        
        // 跳过用户已经交互过的项目
        if (this.hasUserInteracted(userId, candidateItemId)) {
          continue
        }
        
        const weightedScore = behavior.value * similarity.similarity
        const timeDecay = this.calculateTimeDecay(behavior.timestamp)
        const finalScore = weightedScore * timeDecay
        
        if (!candidates.has(candidateItemId)) {
          candidates.set(candidateItemId, { score: 0, contributors: [] })
        }
        
        const candidate = candidates.get(candidateItemId)!
        candidate.score += finalScore
        candidate.contributors.push(behavior.itemId)
      }
    }
    
    // 排序并返回结果
    const recommendations = Array.from(candidates.entries())
      .map(([itemId, data]) => ({
        itemId,
        score: data.score / data.contributors.length,
        reason: `基于您喜欢的 ${data.contributors.length} 个相似内容`
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
    
    return recommendations
  }
  
  /**
   * 混合推荐（结合用户和项目协同过滤）
   */
  async getHybridRecommendations(
    userId: Entity,
    limit: number = 10,
    userWeight: number = 0.6,
    itemWeight: number = 0.4
  ): Promise<Array<{ itemId: string; score: number; reason: string }>> {
    const userBasedRecs = this.config.useUserBased 
      ? await this.getUserBasedRecommendations(userId, limit * 2)
      : []
    
    const itemBasedRecs = this.config.useItemBased
      ? await this.getItemBasedRecommendations(userId, limit * 2)
      : []
    
    // 合并推荐结果
    const combined = new Map<string, { score: number; reasons: string[] }>()
    
    // 添加基于用户的推荐
    for (const rec of userBasedRecs) {
      combined.set(rec.itemId, {
        score: rec.score * userWeight,
        reasons: [rec.reason]
      })
    }
    
    // 添加基于项目的推荐
    for (const rec of itemBasedRecs) {
      if (combined.has(rec.itemId)) {
        const existing = combined.get(rec.itemId)!
        existing.score += rec.score * itemWeight
        existing.reasons.push(rec.reason)
      } else {
        combined.set(rec.itemId, {
          score: rec.score * itemWeight,
          reasons: [rec.reason]
        })
      }
    }
    
    // 排序并返回结果
    return Array.from(combined.entries())
      .map(([itemId, data]) => ({
        itemId,
        score: data.score,
        reason: data.reasons.join('；')
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
  }
  
  /**
   * 获取用户相似度
   */
  private async getUserSimilarities(userId: Entity): Promise<UserSimilarity[]> {
    const userKey = userId.toString()
    
    // 检查缓存
    if (this.userSimilarities.has(userKey)) {
      const cached = this.userSimilarities.get(userKey)!
      if (this.isCacheValid(cached[0]?.calculatedAt)) {
        return cached
      }
    }
    
    // 计算相似度
    const similarities: UserSimilarity[] = []
    const userMatrix = this.userItemMatrix.get(userKey)
    
    if (!userMatrix) return similarities
    
    for (const [otherUserKey, otherMatrix] of this.userItemMatrix) {
      if (otherUserKey === userKey) continue
      
      const similarity = this.calculateUserSimilarity(userMatrix, otherMatrix, userKey, otherUserKey)
      if (similarity && similarity.similarity >= this.config.similarityThreshold) {
        similarities.push(similarity)
      }
    }
    
    // 排序并缓存
    similarities.sort((a, b) => b.similarity - a.similarity)
    this.userSimilarities.set(userKey, similarities)
    
    return similarities
  }
  
  /**
   * 获取项目相似度
   */
  private async getItemSimilarities(itemId: string): Promise<ItemSimilarity[]> {
    // 检查缓存
    if (this.itemSimilarities.has(itemId)) {
      const cached = this.itemSimilarities.get(itemId)!
      if (this.isCacheValid(cached[0]?.calculatedAt)) {
        return cached
      }
    }
    
    // 计算相似度
    const similarities: ItemSimilarity[] = []
    const itemUsers = this.getItemUserVector(itemId)
    
    for (const [otherItemId] of this.itemBehaviors) {
      if (otherItemId === itemId) continue
      
      const otherItemUsers = this.getItemUserVector(otherItemId)
      const similarity = this.calculateItemSimilarity(itemUsers, otherItemUsers, itemId, otherItemId)
      
      if (similarity && similarity.similarity >= this.config.similarityThreshold) {
        similarities.push(similarity)
      }
    }
    
    // 排序并缓存
    similarities.sort((a, b) => b.similarity - a.similarity)
    this.itemSimilarities.set(itemId, similarities)
    
    return similarities
  }
  
  /**
   * 计算用户相似度
   */
  private calculateUserSimilarity(
    userMatrix1: Map<string, number>,
    userMatrix2: Map<string, number>,
    userId1: string,
    userId2: string
  ): UserSimilarity | null {
    const commonItems = Array.from(userMatrix1.keys()).filter(item => userMatrix2.has(item))
    
    if (commonItems.length < this.config.minCommonItems) {
      return null
    }
    
    const vector1 = commonItems.map(item => userMatrix1.get(item)!)
    const vector2 = commonItems.map(item => userMatrix2.get(item)!)
    
    let similarity: number
    
    switch (this.config.similarityMethod) {
      case 'cosine':
        similarity = this.cosineSimilarity(vector1, vector2)
        break
      case 'pearson':
        similarity = this.pearsonCorrelation(vector1, vector2)
        break
      case 'jaccard':
        similarity = this.jaccardSimilarity(vector1, vector2)
        break
      case 'euclidean':
        similarity = this.euclideanSimilarity(vector1, vector2)
        break
      default:
        similarity = this.cosineSimilarity(vector1, vector2)
    }
    
    return {
      userId1: userId1 as any,
      userId2: userId2 as any,
      similarity,
      commonItems: commonItems.length,
      method: this.config.similarityMethod,
      calculatedAt: new Date()
    }
  }
  
  /**
   * 计算项目相似度
   */
  private calculateItemSimilarity(
    itemUsers1: Map<string, number>,
    itemUsers2: Map<string, number>,
    itemId1: string,
    itemId2: string
  ): ItemSimilarity | null {
    const commonUsers = Array.from(itemUsers1.keys()).filter(user => itemUsers2.has(user))
    
    if (commonUsers.length < this.config.minCommonItems) {
      return null
    }
    
    const vector1 = commonUsers.map(user => itemUsers1.get(user)!)
    const vector2 = commonUsers.map(user => itemUsers2.get(user)!)
    
    let similarity: number
    
    switch (this.config.similarityMethod) {
      case 'cosine':
        similarity = this.cosineSimilarity(vector1, vector2)
        break
      case 'pearson':
        similarity = this.pearsonCorrelation(vector1, vector2)
        break
      case 'jaccard':
        similarity = this.jaccardSimilarity(vector1, vector2)
        break
      case 'euclidean':
        similarity = this.euclideanSimilarity(vector1, vector2)
        break
      default:
        similarity = this.cosineSimilarity(vector1, vector2)
    }
    
    return {
      itemId1,
      itemId2,
      similarity,
      commonUsers: commonUsers.length,
      method: this.config.similarityMethod,
      calculatedAt: new Date()
    }
  }
  
  /**
   * 余弦相似度
   */
  private cosineSimilarity(vector1: number[], vector2: number[]): number {
    const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0)
    const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0))
    const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0))
    
    if (magnitude1 === 0 || magnitude2 === 0) return 0
    
    return dotProduct / (magnitude1 * magnitude2)
  }
  
  /**
   * 皮尔逊相关系数
   */
  private pearsonCorrelation(vector1: number[], vector2: number[]): number {
    const n = vector1.length
    if (n === 0) return 0
    
    const mean1 = vector1.reduce((sum, val) => sum + val, 0) / n
    const mean2 = vector2.reduce((sum, val) => sum + val, 0) / n
    
    let numerator = 0
    let denominator1 = 0
    let denominator2 = 0
    
    for (let i = 0; i < n; i++) {
      const diff1 = vector1[i] - mean1
      const diff2 = vector2[i] - mean2
      
      numerator += diff1 * diff2
      denominator1 += diff1 * diff1
      denominator2 += diff2 * diff2
    }
    
    const denominator = Math.sqrt(denominator1 * denominator2)
    if (denominator === 0) return 0
    
    return numerator / denominator
  }
  
  /**
   * 杰卡德相似度
   */
  private jaccardSimilarity(vector1: number[], vector2: number[]): number {
    const set1 = new Set(vector1.map((val, i) => val > 0 ? i : -1).filter(i => i >= 0))
    const set2 = new Set(vector2.map((val, i) => val > 0 ? i : -1).filter(i => i >= 0))
    
    const intersection = new Set([...set1].filter(x => set2.has(x)))
    const union = new Set([...set1, ...set2])
    
    return union.size === 0 ? 0 : intersection.size / union.size
  }
  
  /**
   * 欧几里得相似度
   */
  private euclideanSimilarity(vector1: number[], vector2: number[]): number {
    const distance = Math.sqrt(
      vector1.reduce((sum, val, i) => sum + Math.pow(val - vector2[i], 2), 0)
    )
    
    return 1 / (1 + distance)
  }
  
  // 辅助方法
  private updateUserItemMatrix(behavior: UserBehavior): void {
    const userKey = behavior.userId.toString()
    
    if (!this.userItemMatrix.has(userKey)) {
      this.userItemMatrix.set(userKey, new Map())
    }
    
    const userMatrix = this.userItemMatrix.get(userKey)!
    const currentValue = userMatrix.get(behavior.itemId) || 0
    
    // 根据行为类型计算权重
    let weight = 1
    switch (behavior.actionType) {
      case 'view': weight = 1; break
      case 'like': weight = 3; break
      case 'dislike': weight = -2; break
      case 'complete': weight = 5; break
      case 'bookmark': weight = 4; break
      case 'share': weight = 3; break
      case 'rate': weight = behavior.value; break
    }
    
    userMatrix.set(behavior.itemId, currentValue + weight)
  }
  
  private getItemUserVector(itemId: string): Map<string, number> {
    const itemUsers = new Map<string, number>()
    
    for (const [userKey, userMatrix] of this.userItemMatrix) {
      const rating = userMatrix.get(itemId)
      if (rating !== undefined) {
        itemUsers.set(userKey, rating)
      }
    }
    
    return itemUsers
  }
  
  private hasUserInteracted(userId: Entity, itemId: string): boolean {
    const userKey = userId.toString()
    const userMatrix = this.userItemMatrix.get(userKey)
    return userMatrix ? userMatrix.has(itemId) : false
  }
  
  private calculateTimeDecay(timestamp: Date): number {
    const now = Date.now()
    const timeDiff = now - timestamp.getTime()
    const daysDiff = timeDiff / (1000 * 60 * 60 * 24)
    
    return Math.pow(this.config.timeDecayFactor, daysDiff)
  }
  
  private isCacheValid(calculatedAt?: Date): boolean {
    if (!calculatedAt) return false
    
    const now = Date.now()
    const cacheAge = now - calculatedAt.getTime()
    const maxAge = this.config.cacheExpirationHours * 60 * 60 * 1000
    
    return cacheAge < maxAge
  }
  
  private clearSimilarityCache(userKey: string, itemKey: string): void {
    this.userSimilarities.delete(userKey)
    this.itemSimilarities.delete(itemKey)
  }
  
  /**
   * 获取推荐统计信息
   */
  getStatistics(): {
    totalUsers: number
    totalItems: number
    totalBehaviors: number
    averageBehaviorsPerUser: number
    averageBehaviorsPerItem: number
    sparsity: number
  } {
    const totalUsers = this.userBehaviors.size
    const totalItems = this.itemBehaviors.size
    const totalBehaviors = Array.from(this.userBehaviors.values())
      .reduce((sum, behaviors) => sum + behaviors.length, 0)
    
    const averageBehaviorsPerUser = totalUsers > 0 ? totalBehaviors / totalUsers : 0
    const averageBehaviorsPerItem = totalItems > 0 ? totalBehaviors / totalItems : 0
    
    const possibleInteractions = totalUsers * totalItems
    const sparsity = possibleInteractions > 0 ? 1 - (totalBehaviors / possibleInteractions) : 0
    
    return {
      totalUsers,
      totalItems,
      totalBehaviors,
      averageBehaviorsPerUser,
      averageBehaviorsPerItem,
      sparsity
    }
  }
  
  /**
   * 清除所有数据
   */
  clearAllData(): void {
    this.userBehaviors.clear()
    this.itemBehaviors.clear()
    this.userSimilarities.clear()
    this.itemSimilarities.clear()
    this.userItemMatrix.clear()
  }
}
