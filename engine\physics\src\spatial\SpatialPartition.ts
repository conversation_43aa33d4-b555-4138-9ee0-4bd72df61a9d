/**
 * DL-Engine 空间分区系统
 * 提供高效的空间分区算法，用于优化碰撞检测和查询性能
 */

import { Entity } from '@dl-engine/engine-ecs'
import { PhysicsVector3 } from '../types/PhysicsTypes'
import { SpatialIndexItem } from './SpatialIndex'

/**
 * 空间分区节点
 */
export interface SpatialPartitionNode {
  /** 节点边界 */
  bounds: {
    min: PhysicsVector3
    max: PhysicsVector3
  }
  
  /** 节点中的项目 */
  items: SpatialIndexItem[]
  
  /** 子节点 */
  children?: SpatialPartitionNode[]
  
  /** 是否为叶子节点 */
  isLeaf: boolean
  
  /** 节点深度 */
  depth: number
}

/**
 * 空间分区配置
 */
export interface SpatialPartitionConfig {
  /** 最大深度 */
  maxDepth: number
  
  /** 每个节点最大项目数 */
  maxItemsPerNode: number
  
  /** 最小节点尺寸 */
  minNodeSize: PhysicsVector3
}

/**
 * 四叉树空间分区（2D）
 */
export class QuadTreePartition {
  private root: SpatialPartitionNode
  private config: SpatialPartitionConfig
  
  constructor(bounds: { min: PhysicsVector3; max: PhysicsVector3 }, config?: Partial<SpatialPartitionConfig>) {
    this.config = {
      maxDepth: 8,
      maxItemsPerNode: 10,
      minNodeSize: { x: 1, y: 1, z: 1 },
      ...config
    }
    
    this.root = {
      bounds,
      items: [],
      isLeaf: true,
      depth: 0
    }
  }
  
  /**
   * 插入项目
   */
  insert(item: SpatialIndexItem): void {
    this.insertIntoNode(this.root, item)
  }
  
  /**
   * 移除项目
   */
  remove(entity: Entity): boolean {
    return this.removeFromNode(this.root, entity)
  }
  
  /**
   * 查询范围内的项目
   */
  queryRange(min: PhysicsVector3, max: PhysicsVector3): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    this.queryRangeInNode(this.root, min, max, results)
    return results
  }
  
  /**
   * 清空分区
   */
  clear(): void {
    this.root = {
      bounds: this.root.bounds,
      items: [],
      isLeaf: true,
      depth: 0
    }
  }
  
  /**
   * 在节点中插入项目
   */
  private insertIntoNode(node: SpatialPartitionNode, item: SpatialIndexItem): void {
    // 检查项目是否在节点边界内
    if (!this.itemInBounds(item, node.bounds)) {
      return
    }
    
    if (node.isLeaf) {
      node.items.push(item)
      
      // 检查是否需要分割节点
      if (node.items.length > this.config.maxItemsPerNode && 
          node.depth < this.config.maxDepth &&
          this.canSplitNode(node)) {
        this.splitNode(node)
      }
    } else {
      // 递归插入到子节点
      if (node.children) {
        for (const child of node.children) {
          this.insertIntoNode(child, item)
        }
      }
    }
  }
  
  /**
   * 从节点中移除项目
   */
  private removeFromNode(node: SpatialPartitionNode, entity: Entity): boolean {
    if (node.isLeaf) {
      const index = node.items.findIndex(item => item.entity === entity)
      if (index !== -1) {
        node.items.splice(index, 1)
        return true
      }
    } else if (node.children) {
      for (const child of node.children) {
        if (this.removeFromNode(child, entity)) {
          return true
        }
      }
    }
    
    return false
  }
  
  /**
   * 在节点中查询范围
   */
  private queryRangeInNode(
    node: SpatialPartitionNode, 
    min: PhysicsVector3, 
    max: PhysicsVector3, 
    results: SpatialIndexItem[]
  ): void {
    // 检查查询范围是否与节点边界相交
    if (!this.boundsIntersect(node.bounds, { min, max })) {
      return
    }
    
    if (node.isLeaf) {
      for (const item of node.items) {
        if (this.itemInRange(item, min, max)) {
          results.push(item)
        }
      }
    } else if (node.children) {
      for (const child of node.children) {
        this.queryRangeInNode(child, min, max, results)
      }
    }
  }
  
  /**
   * 分割节点
   */
  private splitNode(node: SpatialPartitionNode): void {
    const bounds = node.bounds
    const centerX = (bounds.min.x + bounds.max.x) / 2
    const centerZ = (bounds.min.z + bounds.max.z) / 2
    
    // 创建四个子节点
    node.children = [
      // 左上
      {
        bounds: {
          min: { x: bounds.min.x, y: bounds.min.y, z: centerZ },
          max: { x: centerX, y: bounds.max.y, z: bounds.max.z }
        },
        items: [],
        isLeaf: true,
        depth: node.depth + 1
      },
      // 右上
      {
        bounds: {
          min: { x: centerX, y: bounds.min.y, z: centerZ },
          max: { x: bounds.max.x, y: bounds.max.y, z: bounds.max.z }
        },
        items: [],
        isLeaf: true,
        depth: node.depth + 1
      },
      // 左下
      {
        bounds: {
          min: { x: bounds.min.x, y: bounds.min.y, z: bounds.min.z },
          max: { x: centerX, y: bounds.max.y, z: centerZ }
        },
        items: [],
        isLeaf: true,
        depth: node.depth + 1
      },
      // 右下
      {
        bounds: {
          min: { x: centerX, y: bounds.min.y, z: bounds.min.z },
          max: { x: bounds.max.x, y: bounds.max.y, z: centerZ }
        },
        items: [],
        isLeaf: true,
        depth: node.depth + 1
      }
    ]
    
    // 重新分配项目到子节点
    const items = node.items
    node.items = []
    node.isLeaf = false
    
    for (const item of items) {
      for (const child of node.children) {
        this.insertIntoNode(child, item)
      }
    }
  }
  
  /**
   * 检查是否可以分割节点
   */
  private canSplitNode(node: SpatialPartitionNode): boolean {
    const bounds = node.bounds
    const width = bounds.max.x - bounds.min.x
    const height = bounds.max.z - bounds.min.z
    
    return width > this.config.minNodeSize.x * 2 && 
           height > this.config.minNodeSize.z * 2
  }
  
  /**
   * 检查项目是否在边界内
   */
  private itemInBounds(item: SpatialIndexItem, bounds: { min: PhysicsVector3; max: PhysicsVector3 }): boolean {
    return item.position.x >= bounds.min.x && item.position.x <= bounds.max.x &&
           item.position.z >= bounds.min.z && item.position.z <= bounds.max.z
  }
  
  /**
   * 检查项目是否在查询范围内
   */
  private itemInRange(item: SpatialIndexItem, min: PhysicsVector3, max: PhysicsVector3): boolean {
    return item.position.x >= min.x && item.position.x <= max.x &&
           item.position.y >= min.y && item.position.y <= max.y &&
           item.position.z >= min.z && item.position.z <= max.z
  }
  
  /**
   * 检查两个边界是否相交
   */
  private boundsIntersect(
    bounds1: { min: PhysicsVector3; max: PhysicsVector3 },
    bounds2: { min: PhysicsVector3; max: PhysicsVector3 }
  ): boolean {
    return bounds1.min.x <= bounds2.max.x && bounds1.max.x >= bounds2.min.x &&
           bounds1.min.y <= bounds2.max.y && bounds1.max.y >= bounds2.min.y &&
           bounds1.min.z <= bounds2.max.z && bounds1.max.z >= bounds2.min.z
  }
}

/**
 * 空间哈希分区
 */
export class SpatialHashPartition {
  private cellSize: number
  private cells: Map<string, SpatialIndexItem[]>
  
  constructor(cellSize: number = 10) {
    this.cellSize = cellSize
    this.cells = new Map()
  }
  
  /**
   * 获取哈希键
   */
  private getHashKey(position: PhysicsVector3): string {
    const x = Math.floor(position.x / this.cellSize)
    const y = Math.floor(position.y / this.cellSize)
    const z = Math.floor(position.z / this.cellSize)
    return `${x},${y},${z}`
  }
  
  /**
   * 插入项目
   */
  insert(item: SpatialIndexItem): void {
    const key = this.getHashKey(item.position)
    if (!this.cells.has(key)) {
      this.cells.set(key, [])
    }
    this.cells.get(key)!.push(item)
  }
  
  /**
   * 移除项目
   */
  remove(entity: Entity): boolean {
    for (const [key, items] of this.cells) {
      const index = items.findIndex(item => item.entity === entity)
      if (index !== -1) {
        items.splice(index, 1)
        if (items.length === 0) {
          this.cells.delete(key)
        }
        return true
      }
    }
    return false
  }
  
  /**
   * 查询位置附近的项目
   */
  queryNearby(position: PhysicsVector3, radius: number): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    const cellRadius = Math.ceil(radius / this.cellSize)
    
    const centerX = Math.floor(position.x / this.cellSize)
    const centerY = Math.floor(position.y / this.cellSize)
    const centerZ = Math.floor(position.z / this.cellSize)
    
    for (let x = centerX - cellRadius; x <= centerX + cellRadius; x++) {
      for (let y = centerY - cellRadius; y <= centerY + cellRadius; y++) {
        for (let z = centerZ - cellRadius; z <= centerZ + cellRadius; z++) {
          const key = `${x},${y},${z}`
          const items = this.cells.get(key)
          if (items) {
            for (const item of items) {
              const distance = this.distance(position, item.position)
              if (distance <= radius) {
                results.push(item)
              }
            }
          }
        }
      }
    }
    
    return results
  }
  
  /**
   * 清空分区
   */
  clear(): void {
    this.cells.clear()
  }
  
  /**
   * 计算距离
   */
  private distance(a: PhysicsVector3, b: PhysicsVector3): number {
    const dx = a.x - b.x
    const dy = a.y - b.y
    const dz = a.z - b.z
    return Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
}
