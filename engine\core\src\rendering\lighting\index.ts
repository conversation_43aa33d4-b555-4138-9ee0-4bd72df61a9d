/**
 * DL-Engine 光照系统
 * 提供各种光源和光照工具
 */

import * as THREE from 'three'

/**
 * 光源类型枚举
 */
export enum LightType {
  AMBIENT = 'ambient',
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  HEMISPHERE = 'hemisphere',
  RECT_AREA = 'rectArea'
}

/**
 * 光源配置接口
 */
export interface LightConfig {
  type: LightType
  color?: number | string
  intensity?: number
  position?: [number, number, number]
  target?: [number, number, number]
  castShadow?: boolean
  [key: string]: any
}

/**
 * 光源工厂
 */
export class LightFactory {
  /**
   * 创建光源
   */
  static create(config: LightConfig): THREE.Light {
    let light: THREE.Light

    switch (config.type) {
      case LightType.AMBIENT:
        light = new THREE.AmbientLight(config.color, config.intensity)
        break

      case LightType.DIRECTIONAL:
        light = new THREE.DirectionalLight(config.color, config.intensity)
        if (config.position) {
          light.position.set(...config.position)
        }
        if (config.target) {
          const directionalLight = light as THREE.DirectionalLight
          if (directionalLight.target) {
            directionalLight.target.position.set(...config.target)
          }
        }
        break

      case LightType.POINT:
        light = new THREE.PointLight(config.color, config.intensity, config.distance, config.decay)
        if (config.position) {
          light.position.set(...config.position)
        }
        break

      case LightType.SPOT:
        light = new THREE.SpotLight(
          config.color,
          config.intensity,
          config.distance,
          config.angle,
          config.penumbra,
          config.decay
        )
        if (config.position) {
          light.position.set(...config.position)
        }
        if (config.target) {
          const spotLight = light as THREE.SpotLight
          if (spotLight.target) {
            spotLight.target.position.set(...config.target)
          }
        }
        break

      case LightType.HEMISPHERE:
        light = new THREE.HemisphereLight(config.color, config.groundColor, config.intensity)
        if (config.position) {
          light.position.set(...config.position)
        }
        break

      case LightType.RECT_AREA:
        light = new THREE.RectAreaLight(config.color, config.intensity, config.width, config.height)
        if (config.position) {
          light.position.set(...config.position)
        }
        break

      default:
        light = new THREE.AmbientLight(config.color, config.intensity)
    }

    // 设置阴影
    if (config.castShadow && 'castShadow' in light) {
      light.castShadow = true
      
      // 配置阴影参数
      if (light.shadow) {
        light.shadow.mapSize.width = config.shadowMapSize || 1024
        light.shadow.mapSize.height = config.shadowMapSize || 1024
        if ('near' in light.shadow.camera) {
          (light.shadow.camera as any).near = config.shadowNear || 0.5
        }
        if ('far' in light.shadow.camera) {
          (light.shadow.camera as any).far = config.shadowFar || 500
        }
        
        if (light.shadow.camera instanceof THREE.PerspectiveCamera) {
          light.shadow.camera.fov = config.shadowFov || 50
        } else if (light.shadow.camera instanceof THREE.OrthographicCamera) {
          const size = config.shadowSize || 10
          light.shadow.camera.left = -size
          light.shadow.camera.right = size
          light.shadow.camera.top = size
          light.shadow.camera.bottom = -size
        }
      }
    }

    return light
  }
}

/**
 * 预定义光照设置
 */
export const PredefinedLighting = {
  /**
   * 基础三点光照
   */
  threePoint: () => [
    LightFactory.create({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1.0,
      position: [5, 5, 5],
      castShadow: true
    }),
    LightFactory.create({
      type: LightType.DIRECTIONAL,
      color: 0x4444ff,
      intensity: 0.3,
      position: [-5, 0, -5]
    }),
    LightFactory.create({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.2
    })
  ],

  /**
   * 室内光照
   */
  indoor: () => [
    LightFactory.create({
      type: LightType.AMBIENT,
      color: 0x404040,
      intensity: 0.4
    }),
    LightFactory.create({
      type: LightType.POINT,
      color: 0xffffff,
      intensity: 1.0,
      position: [0, 5, 0],
      distance: 20,
      decay: 2
    }),
    LightFactory.create({
      type: LightType.RECT_AREA,
      color: 0xffffff,
      intensity: 2.0,
      width: 4,
      height: 4,
      position: [0, 8, 0]
    })
  ],

  /**
   * 户外光照
   */
  outdoor: () => [
    LightFactory.create({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 1.0,
      position: [10, 10, 5],
      castShadow: true,
      shadowMapSize: 2048
    }),
    LightFactory.create({
      type: LightType.HEMISPHERE,
      color: 0x87CEEB,
      groundColor: 0x8B4513,
      intensity: 0.6
    })
  ],

  /**
   * 戏剧性光照
   */
  dramatic: () => [
    LightFactory.create({
      type: LightType.SPOT,
      color: 0xffffff,
      intensity: 2.0,
      position: [0, 10, 0],
      target: [0, 0, 0],
      angle: Math.PI / 6,
      penumbra: 0.3,
      castShadow: true
    }),
    LightFactory.create({
      type: LightType.AMBIENT,
      color: 0x111111,
      intensity: 0.1
    })
  ],

  /**
   * 教育场景光照
   */
  educational: () => [
    LightFactory.create({
      type: LightType.AMBIENT,
      color: 0xffffff,
      intensity: 0.6
    }),
    LightFactory.create({
      type: LightType.DIRECTIONAL,
      color: 0xffffff,
      intensity: 0.8,
      position: [5, 5, 5],
      castShadow: true
    }),
    LightFactory.create({
      type: LightType.POINT,
      color: 0x4488ff,
      intensity: 0.3,
      position: [-3, 3, -3]
    })
  ]
}

/**
 * 光照工具函数
 */
export const LightingUtils = {
  /**
   * 设置光源强度
   */
  setIntensity(light: THREE.Light, intensity: number): void {
    light.intensity = intensity
  },

  /**
   * 设置光源颜色
   */
  setColor(light: THREE.Light, color: number | string): void {
    light.color.set(color)
  },

  /**
   * 动画光源强度
   */
  animateIntensity(
    light: THREE.Light,
    targetIntensity: number,
    duration: number,
    onComplete?: () => void
  ): void {
    const startIntensity = light.intensity
    const startTime = performance.now()

    const animate = () => {
      const elapsed = performance.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      light.intensity = startIntensity + (targetIntensity - startIntensity) * progress

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else if (onComplete) {
        onComplete()
      }
    }

    animate()
  },

  /**
   * 创建光源辅助器
   */
  createHelper(light: THREE.Light): THREE.Object3D | null {
    if (light instanceof THREE.DirectionalLight) {
      return new THREE.DirectionalLightHelper(light, 1)
    } else if (light instanceof THREE.PointLight) {
      return new THREE.PointLightHelper(light, 1)
    } else if (light instanceof THREE.SpotLight) {
      return new THREE.SpotLightHelper(light)
    } else if (light instanceof THREE.HemisphereLight) {
      return new THREE.HemisphereLightHelper(light, 1)
    }
    return null
  },

  /**
   * 计算光照强度
   */
  calculateLightIntensity(distance: number, lightPower: number): number {
    return lightPower / (4 * Math.PI * distance * distance)
  },

  /**
   * 设置阴影质量
   */
  setShadowQuality(light: THREE.Light, quality: 'low' | 'medium' | 'high' | 'ultra'): void {
    if (!light.shadow) return

    const qualitySettings = {
      low: { mapSize: 512, bias: -0.0005 },
      medium: { mapSize: 1024, bias: -0.0003 },
      high: { mapSize: 2048, bias: -0.0001 },
      ultra: { mapSize: 4096, bias: -0.00005 }
    }

    const settings = qualitySettings[quality]
    light.shadow.mapSize.width = settings.mapSize
    light.shadow.mapSize.height = settings.mapSize
    light.shadow.bias = settings.bias
  },

  /**
   * 优化光源性能
   */
  optimizeLight(light: THREE.Light): void {
    // 对于点光源和聚光灯，设置合理的距离
    if (light instanceof THREE.PointLight || light instanceof THREE.SpotLight) {
      if (!light.distance) {
        light.distance = 100 // 设置默认距离
      }
      light.decay = 2 // 使用物理正确的衰减
    }

    // 优化阴影设置
    if (light.shadow) {
      light.shadow.autoUpdate = false // 手动控制阴影更新
      light.shadow.needsUpdate = true
    }
  }
}

/**
 * 光照管理器
 */
export class LightingManager {
  private lights = new Map<string, THREE.Light>()
  private scene: THREE.Scene | null = null

  constructor(scene?: THREE.Scene) {
    this.scene = scene || null
  }

  /**
   * 设置场景
   */
  setScene(scene: THREE.Scene): void {
    this.scene = scene
  }

  /**
   * 添加光源
   */
  addLight(name: string, config: LightConfig): THREE.Light {
    const light = LightFactory.create(config)
    this.lights.set(name, light)
    
    if (this.scene) {
      this.scene.add(light)
      if ('target' in light && (light as any).target) {
        this.scene.add((light as any).target)
      }
    }

    return light
  }

  /**
   * 移除光源
   */
  removeLight(name: string): boolean {
    const light = this.lights.get(name)
    if (light) {
      if (this.scene) {
        this.scene.remove(light)
        if ('target' in light && (light as any).target) {
          this.scene.remove((light as any).target)
        }
      }
      this.lights.delete(name)
      return true
    }
    return false
  }

  /**
   * 获取光源
   */
  getLight(name: string): THREE.Light | undefined {
    return this.lights.get(name)
  }

  /**
   * 设置预定义光照
   */
  setPredefinedLighting(preset: keyof typeof PredefinedLighting): void {
    this.clearLights()
    const lights = PredefinedLighting[preset]()
    
    lights.forEach((light, index) => {
      this.lights.set(`${preset}_${index}`, light)
      if (this.scene) {
        this.scene.add(light)
        if ('target' in light && (light as any).target) {
          this.scene.add((light as any).target)
        }
      }
    })
  }

  /**
   * 清除所有光源
   */
  clearLights(): void {
    this.lights.forEach(light => {
      if (this.scene) {
        this.scene.remove(light)
        if ('target' in light && (light as any).target) {
          this.scene.remove((light as any).target)
        }
      }
    })
    this.lights.clear()
  }

  /**
   * 获取所有光源
   */
  getAllLights(): THREE.Light[] {
    return Array.from(this.lights.values())
  }

  /**
   * 更新所有光源
   */
  update(): void {
    this.lights.forEach(light => {
      if (light.shadow) {
        light.shadow.needsUpdate = true
      }
    })
  }
}

/**
 * 全局光照管理器实例
 */
export const globalLightingManager = new LightingManager()
