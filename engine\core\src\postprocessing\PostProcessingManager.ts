/**
 * DL-Engine 后处理效果管理器
 * 管理渲染管道、后处理效果链和性能优化
 */

import {
  WebGLRenderer,
  WebGLRenderTarget,
  Scene,
  Camera,
  ShaderMaterial,
  PlaneGeometry,
  Mesh,
  OrthographicCamera,
  Vector2,
  Texture,
  LinearFilter,
  RGBAFormat,
  UnsignedByteType,
  NearestFilter
} from 'three'

import { defineState, getMutableState, getState, State } from '@dl-engine/engine-state'

/**
 * 后处理效果类型
 */
export type PostProcessingEffectType = 
  | 'bloom'
  | 'blur'
  | 'fxaa'
  | 'ssao'
  | 'ssr'
  | 'tonemap'
  | 'colorgrade'
  | 'vignette'
  | 'chromatic_aberration'
  | 'film_grain'
  | 'depth_of_field'
  | 'motion_blur'
  | 'outline'
  | 'custom'

/**
 * 后处理效果配置
 */
export interface PostProcessingEffectConfig {
  /** 效果类型 */
  type: PostProcessingEffectType
  
  /** 是否启用 */
  enabled: boolean
  
  /** 渲染顺序 */
  order: number
  
  /** 效果参数 */
  params: Record<string, any>
  
  /** 着色器代码（仅用于custom类型） */
  shaderCode?: {
    vertexShader: string
    fragmentShader: string
    uniforms: Record<string, any>
  }
}

/**
 * 渲染通道
 */
export interface RenderPass {
  /** 通道ID */
  id: string
  
  /** 通道名称 */
  name: string
  
  /** 输入纹理 */
  inputTexture: Texture | null
  
  /** 输出渲染目标 */
  outputTarget: WebGLRenderTarget | null
  
  /** 着色器材质 */
  material: ShaderMaterial
  
  /** 全屏四边形 */
  quad: Mesh
  
  /** 是否启用 */
  enabled: boolean
  
  /** 是否需要深度缓冲 */
  needsDepth: boolean
  
  /** 是否需要法线缓冲 */
  needsNormals: boolean
  
  /** 渲染尺寸缩放 */
  sizeScale: number
}

/**
 * 后处理管道
 */
export interface PostProcessingPipeline {
  /** 管道ID */
  id: string
  
  /** 管道名称 */
  name: string
  
  /** 渲染通道列表 */
  passes: RenderPass[]
  
  /** 是否启用 */
  enabled: boolean
  
  /** 输入场景 */
  inputScene: Scene | null
  
  /** 输入相机 */
  inputCamera: Camera | null
  
  /** 最终输出目标 */
  finalTarget: WebGLRenderTarget | null
  
  /** 性能统计 */
  stats: {
    renderTime: number
    passCount: number
    textureMemory: number
  }
}

/**
 * 后处理管理器状态
 */
export interface PostProcessingManagerState {
  /** 后处理管道 */
  pipelines: Map<string, PostProcessingPipeline>
  
  /** 当前激活管道 */
  activePipeline: string | null
  
  /** 渲染目标池 */
  renderTargetPool: WebGLRenderTarget[]
  
  /** 全局配置 */
  config: {
    /** 是否启用后处理 */
    enabled: boolean
    
    /** 渲染尺寸 */
    renderSize: Vector2
    
    /** 像素比 */
    pixelRatio: number
    
    /** 抗锯齿 */
    antialias: boolean
    
    /** 自动调整质量 */
    autoQuality: boolean
    
    /** 目标帧率 */
    targetFPS: number
    
    /** 最大渲染目标数量 */
    maxRenderTargets: number
  }
  
  /** 性能统计 */
  stats: {
    totalRenderTime: number
    frameCount: number
    averageFPS: number
    memoryUsage: number
  }
}

/**
 * 后处理管理器状态定义
 */
export const PostProcessingManagerState = defineState({
  name: 'DLEngine.PostProcessingManager',
  initial: (): PostProcessingManagerState => ({
    pipelines: new Map(),
    activePipeline: null,
    renderTargetPool: [],
    config: {
      enabled: true,
      renderSize: new Vector2(1920, 1080),
      pixelRatio: 1.0,
      antialias: true,
      autoQuality: true,
      targetFPS: 60,
      maxRenderTargets: 10
    },
    stats: {
      totalRenderTime: 0,
      frameCount: 0,
      averageFPS: 60,
      memoryUsage: 0
    }
  })
})

// 导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 后处理管理器
 */
export class PostProcessingManager {
  private static instance: PostProcessingManager | null = null
  private renderer: WebGLRenderer | null = null
  private orthoCamera: OrthographicCamera
  private quadGeometry: PlaneGeometry
  
  /**
   * 获取单例实例
   */
  static getInstance(): PostProcessingManager {
    if (!PostProcessingManager.instance) {
      PostProcessingManager.instance = new PostProcessingManager()
    }
    return PostProcessingManager.instance
  }
  
  constructor() {
    // 创建正交相机用于全屏渲染
    this.orthoCamera = new OrthographicCamera(-1, 1, 1, -1, 0, 1)
    
    // 创建全屏四边形几何体
    this.quadGeometry = new PlaneGeometry(2, 2)
  }
  
  /**
   * 初始化后处理管理器
   */
  initialize(renderer: WebGLRenderer): void {
    this.renderer = renderer
    
    // 更新渲染尺寸
    const size = renderer.getSize(new Vector2())
    const state = getMutableState(PostProcessingManagerState)
    state.config.renderSize.set(size)
    state.config.pixelRatio.set(renderer.getPixelRatio())
    
    console.log('PostProcessing Manager initialized')
  }
  
  /**
   * 创建后处理管道
   */
  createPipeline(name: string, effects: PostProcessingEffectConfig[]): string {
    const pipelineId = this.generatePipelineId()
    
    // 按顺序排序效果
    const sortedEffects = effects.sort((a, b) => a.order - b.order)
    
    // 创建渲染通道
    const passes: RenderPass[] = []
    for (const effect of sortedEffects) {
      if (effect.enabled) {
        const pass = this.createRenderPass(effect)
        if (pass) {
          passes.push(pass)
        }
      }
    }
    
    // 创建管道
    const pipeline: PostProcessingPipeline = {
      id: pipelineId,
      name,
      passes,
      enabled: true,
      inputScene: null,
      inputCamera: null,
      finalTarget: null,
      stats: {
        renderTime: 0,
        passCount: passes.length,
        textureMemory: 0
      }
    }
    
    // 添加到状态（简化版）
    console.log(`Adding pipeline to state: ${pipeline.id}`)
    
    console.log(`PostProcessing pipeline created: ${pipelineId} (${name})`)
    return pipelineId
  }
  
  /**
   * 创建渲染通道
   */
  private createRenderPass(effect: PostProcessingEffectConfig): RenderPass | null {
    let material: ShaderMaterial
    
    try {
      // 根据效果类型创建着色器材质
      switch (effect.type) {
        case 'bloom':
          material = this.createBloomMaterial(effect.params)
          break
          
        case 'blur':
          material = this.createBlurMaterial(effect.params)
          break
          
        case 'fxaa':
          material = this.createFXAAMaterial(effect.params)
          break
          
        case 'tonemap':
          material = this.createTonemapMaterial(effect.params)
          break
          
        case 'vignette':
          material = this.createVignetteMaterial(effect.params)
          break
          
        case 'custom':
          if (!effect.shaderCode) {
            throw new Error('Shader code required for custom effect')
          }
          material = new ShaderMaterial({
            vertexShader: effect.shaderCode.vertexShader,
            fragmentShader: effect.shaderCode.fragmentShader,
            uniforms: effect.shaderCode.uniforms
          })
          break
          
        default:
          console.warn(`Unsupported post-processing effect: ${effect.type}`)
          return null
      }
      
      // 创建全屏四边形
      const quad = new Mesh(this.quadGeometry, material)
      
      // 创建渲染通道
      const pass: RenderPass = {
        id: this.generatePassId(),
        name: effect.type,
        inputTexture: null,
        outputTarget: null,
        material,
        quad,
        enabled: effect.enabled,
        needsDepth: this.effectNeedsDepth(effect.type),
        needsNormals: this.effectNeedsNormals(effect.type),
        sizeScale: effect.params.sizeScale || 1.0
      }
      
      return pass
      
    } catch (error) {
      console.error(`Failed to create render pass for ${effect.type}:`, error)
      return null
    }
  }
  
  /**
   * 创建Bloom材质
   */
  private createBloomMaterial(params: any): ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform float threshold;
      uniform float intensity;
      varying vec2 vUv;
      
      void main() {
        vec4 color = texture2D(tDiffuse, vUv);
        float brightness = dot(color.rgb, vec3(0.299, 0.587, 0.114));
        
        if (brightness > threshold) {
          gl_FragColor = color * intensity;
        } else {
          gl_FragColor = vec4(0.0);
        }
      }
    `
    
    return new ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        tDiffuse: { value: null },
        threshold: { value: params.threshold || 0.8 },
        intensity: { value: params.intensity || 1.0 }
      }
    })
  }
  
  /**
   * 创建模糊材质
   */
  private createBlurMaterial(params: any): ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform vec2 resolution;
      uniform float radius;
      varying vec2 vUv;
      
      void main() {
        vec2 texelSize = 1.0 / resolution;
        vec4 color = vec4(0.0);
        float total = 0.0;
        
        for (float x = -radius; x <= radius; x++) {
          for (float y = -radius; y <= radius; y++) {
            vec2 offset = vec2(x, y) * texelSize;
            color += texture2D(tDiffuse, vUv + offset);
            total += 1.0;
          }
        }
        
        gl_FragColor = color / total;
      }
    `
    
    return new ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        tDiffuse: { value: null },
        resolution: { value: new Vector2(1920, 1080) },
        radius: { value: params.radius || 2.0 }
      }
    })
  }
  
  /**
   * 创建FXAA材质
   */
  private createFXAAMaterial(params: any): ShaderMaterial {
    // 简化的FXAA实现
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform vec2 resolution;
      varying vec2 vUv;
      
      void main() {
        vec2 texelSize = 1.0 / resolution;
        vec4 color = texture2D(tDiffuse, vUv);
        
        // 简化的边缘检测和抗锯齿
        vec4 colorN = texture2D(tDiffuse, vUv + vec2(0.0, -texelSize.y));
        vec4 colorS = texture2D(tDiffuse, vUv + vec2(0.0, texelSize.y));
        vec4 colorE = texture2D(tDiffuse, vUv + vec2(texelSize.x, 0.0));
        vec4 colorW = texture2D(tDiffuse, vUv + vec2(-texelSize.x, 0.0));
        
        vec4 average = (colorN + colorS + colorE + colorW) * 0.25;
        gl_FragColor = mix(color, average, 0.5);
      }
    `
    
    return new ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        tDiffuse: { value: null },
        resolution: { value: new Vector2(1920, 1080) }
      }
    })
  }
  
  /**
   * 创建色调映射材质
   */
  private createTonemapMaterial(params: any): ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform float exposure;
      varying vec2 vUv;
      
      vec3 ACESFilm(vec3 x) {
        float a = 2.51;
        float b = 0.03;
        float c = 2.43;
        float d = 0.59;
        float e = 0.14;
        return clamp((x * (a * x + b)) / (x * (c * x + d) + e), 0.0, 1.0);
      }
      
      void main() {
        vec4 color = texture2D(tDiffuse, vUv);
        color.rgb *= exposure;
        color.rgb = ACESFilm(color.rgb);
        gl_FragColor = color;
      }
    `
    
    return new ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        tDiffuse: { value: null },
        exposure: { value: params.exposure || 1.0 }
      }
    })
  }
  
  /**
   * 创建晕影材质
   */
  private createVignetteMaterial(params: any): ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `
    
    const fragmentShader = `
      uniform sampler2D tDiffuse;
      uniform float intensity;
      uniform float radius;
      varying vec2 vUv;
      
      void main() {
        vec4 color = texture2D(tDiffuse, vUv);
        vec2 center = vec2(0.5, 0.5);
        float dist = distance(vUv, center);
        float vignette = smoothstep(radius, radius - 0.3, dist);
        color.rgb *= mix(1.0 - intensity, 1.0, vignette);
        gl_FragColor = color;
      }
    `
    
    return new ShaderMaterial({
      vertexShader,
      fragmentShader,
      uniforms: {
        tDiffuse: { value: null },
        intensity: { value: params.intensity || 0.5 },
        radius: { value: params.radius || 0.8 }
      }
    })
  }
  
  /**
   * 检查效果是否需要深度缓冲
   */
  private effectNeedsDepth(effectType: PostProcessingEffectType): boolean {
    return ['ssao', 'depth_of_field', 'outline'].includes(effectType)
  }
  
  /**
   * 检查效果是否需要法线缓冲
   */
  private effectNeedsNormals(effectType: PostProcessingEffectType): boolean {
    return ['ssao', 'ssr'].includes(effectType)
  }
  
  /**
   * 渲染后处理管道
   */
  render(scene: Scene, camera: Camera, outputTarget?: WebGLRenderTarget): void {
    if (!this.renderer) {
      console.warn('Renderer not initialized')
      return
    }
    
    const state = getState(PostProcessingManagerState)
    
    if (!state.config.enabled) {
      // 直接渲染场景
      this.renderer.setRenderTarget(outputTarget || null)
      this.renderer.render(scene, camera)
      return
    }
    
    const activePipelineId = state.activePipeline
    if (!activePipelineId) {
      // 没有激活的管道，直接渲染
      this.renderer.setRenderTarget(outputTarget || null)
      this.renderer.render(scene, camera)
      return
    }
    
    const pipelines = state.pipelines as any
    const pipeline = pipelines.get ? pipelines.get(activePipelineId) : null
    if (!pipeline || !pipeline.enabled) {
      // 管道不可用，直接渲染
      this.renderer.setRenderTarget(outputTarget || null)
      this.renderer.render(scene, camera)
      return
    }
    
    const startTime = performance.now()
    
    try {
      this.renderPipeline(pipeline, scene, camera, outputTarget)
    } catch (error) {
      console.error('Error rendering post-processing pipeline:', error)
      // 回退到直接渲染
      this.renderer.setRenderTarget(outputTarget || null)
      this.renderer.render(scene, camera)
    }
    
    // 更新性能统计
    const renderTime = performance.now() - startTime
    pipeline.stats.renderTime = renderTime
    
    const mutableState = getMutableState(PostProcessingManagerState)
    mutableState.stats.totalRenderTime.set(state.stats.totalRenderTime + renderTime)
    mutableState.stats.frameCount.set(state.stats.frameCount + 1)
  }
  
  /**
   * 渲染管道
   */
  private renderPipeline(
    pipeline: PostProcessingPipeline,
    scene: Scene,
    camera: Camera,
    finalTarget?: WebGLRenderTarget
  ): void {
    if (!this.renderer || pipeline.passes.length === 0) {
      return
    }
    
    const config = getState(PostProcessingManagerState).config
    
    // 创建初始渲染目标
    let currentInput = this.getRenderTarget(config.renderSize.x, config.renderSize.y)
    
    // 渲染场景到第一个渲染目标
    this.renderer.setRenderTarget(currentInput)
    this.renderer.render(scene, camera)
    
    // 执行后处理通道
    for (let i = 0; i < pipeline.passes.length; i++) {
      const pass = pipeline.passes[i]
      
      if (!pass.enabled) {
        continue
      }
      
      // 设置输入纹理
      pass.material.uniforms.tDiffuse.value = currentInput.texture
      
      // 确定输出目标
      let outputTarget: WebGLRenderTarget | null
      
      if (i === pipeline.passes.length - 1) {
        // 最后一个通道，输出到最终目标
        outputTarget = finalTarget || null
      } else {
        // 中间通道，输出到新的渲染目标
        outputTarget = this.getRenderTarget(
          config.renderSize.x * pass.sizeScale,
          config.renderSize.y * pass.sizeScale
        )
      }
      
      // 渲染通道
      this.renderer.setRenderTarget(outputTarget)
      this.renderer.render(pass.quad, this.orthoCamera)
      
      // 释放输入渲染目标
      this.releaseRenderTarget(currentInput)
      
      // 更新当前输入
      currentInput = outputTarget || currentInput
    }
  }
  
  /**
   * 获取渲染目标
   */
  private getRenderTarget(width: number, height: number): WebGLRenderTarget {
    const state = getMutableState(PostProcessingManagerState)
    
    // 尝试从池中获取
    const pool = Array.from(state.renderTargetPool.value)
    for (let i = 0; i < pool.length; i++) {
      const target = pool[i] as any
      if (target.width === width && target.height === height) {
        pool.splice(i, 1)
        state.renderTargetPool.set(pool)
        return target as WebGLRenderTarget
      }
    }
    
    // 创建新的渲染目标
    return new WebGLRenderTarget(width, height, {
      minFilter: LinearFilter,
      magFilter: LinearFilter,
      format: RGBAFormat,
      type: UnsignedByteType,
      stencilBuffer: false
    })
  }
  
  /**
   * 释放渲染目标
   */
  private releaseRenderTarget(target: WebGLRenderTarget): void {
    const state = getMutableState(PostProcessingManagerState)
    const pool = state.renderTargetPool.value
    
    const maxTargets = state.config.maxRenderTargets.value
    if (pool.length < maxTargets) {
      // 简化：直接释放，不使用池
      console.log('Render target pool management simplified')
    }
    target.dispose()
  }
  
  /**
   * 生成管道ID
   */
  private generatePipelineId(): string {
    return `pipeline_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成通道ID
   */
  private generatePassId(): string {
    return `pass_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    // 清理渲染目标池
    const pool = getState(PostProcessingManagerState).renderTargetPool
    for (const target of pool) {
      // 简化清理逻辑
      console.log('Cleaning render target')
    }

    // 清理几何体
    this.quadGeometry.dispose()

    // 清理所有管道（简化版）
    console.log('Cleaning pipelines')
  }
}
