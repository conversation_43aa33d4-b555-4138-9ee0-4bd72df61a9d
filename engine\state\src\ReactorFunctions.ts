/**
 * DL-Engine Reactor系统
 * 响应式计算和副作用管理
 */

import { State, hookstate } from '@hookstate/core'
import { DeepReadonly, StateDefinition, ReceptorMap } from './types'

/**
 * Reactor根接口
 */
export interface ReactorRoot {
  /** 运行Reactor */
  run(): void
  
  /** 停止Reactor */
  stop(): void
  
  /** 是否正在运行 */
  isRunning: boolean
}

/**
 * Reactor函数类型
 */
export type ReactorFunction = () => void | (() => void)

/**
 * Reactor配置
 */
export interface ReactorConfig {
  /** Reactor名称 */
  name: string
  
  /** 是否自动启动 */
  autoStart?: boolean
  
  /** 依赖的状态 */
  dependencies?: StateDefinition<any, any, any, any>[]
  
  /** 错误处理器 */
  onError?: (error: Error) => void
}

/**
 * 简单的Reactor实现
 */
export class SimpleReactor implements ReactorRoot {
  private reactorFn: ReactorFunction
  private cleanupFn?: () => void
  private _isRunning: boolean = false
  private config: ReactorConfig
  
  constructor(reactorFn: ReactorFunction, config: ReactorConfig) {
    this.reactorFn = reactorFn
    this.config = config
    
    if (config.autoStart) {
      this.run()
    }
  }
  
  run(): void {
    if (this._isRunning) {
      return
    }
    
    try {
      this._isRunning = true
      const result = this.reactorFn()
      
      if (typeof result === 'function') {
        this.cleanupFn = result
      }
    } catch (error) {
      this._isRunning = false
      if (this.config.onError) {
        this.config.onError(error as Error)
      } else {
        console.error(`Reactor ${this.config.name} error:`, error)
      }
    }
  }
  
  stop(): void {
    if (!this._isRunning) {
      return
    }
    
    this._isRunning = false
    
    if (this.cleanupFn) {
      try {
        this.cleanupFn()
      } catch (error) {
        console.error(`Reactor ${this.config.name} cleanup error:`, error)
      }
      this.cleanupFn = undefined
    }
  }
  
  get isRunning(): boolean {
    return this._isRunning
  }
}

/**
 * Reactor管理器
 */
export class ReactorManager {
  private static instance: ReactorManager | null = null
  private reactors: Map<string, ReactorRoot> = new Map()
  
  static getInstance(): ReactorManager {
    if (!ReactorManager.instance) {
      ReactorManager.instance = new ReactorManager()
    }
    return ReactorManager.instance
  }
  
  /**
   * 创建并注册Reactor
   */
  createReactor(reactorFn: ReactorFunction, config: ReactorConfig): ReactorRoot {
    if (this.reactors.has(config.name)) {
      throw new Error(`Reactor with name ${config.name} already exists`)
    }
    
    const reactor = new SimpleReactor(reactorFn, config)
    this.reactors.set(config.name, reactor)
    
    return reactor
  }
  
  /**
   * 获取Reactor
   */
  getReactor(name: string): ReactorRoot | undefined {
    return this.reactors.get(name)
  }
  
  /**
   * 移除Reactor
   */
  removeReactor(name: string): boolean {
    const reactor = this.reactors.get(name)
    if (reactor) {
      reactor.stop()
      this.reactors.delete(name)
      return true
    }
    return false
  }
  
  /**
   * 启动所有Reactor
   */
  startAll(): void {
    for (const reactor of this.reactors.values()) {
      reactor.run()
    }
  }
  
  /**
   * 停止所有Reactor
   */
  stopAll(): void {
    for (const reactor of this.reactors.values()) {
      reactor.stop()
    }
  }
  
  /**
   * 清理所有Reactor
   */
  cleanup(): void {
    this.stopAll()
    this.reactors.clear()
  }
  
  /**
   * 获取所有Reactor的状态
   */
  getStatus(): Array<{ name: string; isRunning: boolean }> {
    const status: Array<{ name: string; isRunning: boolean }> = []
    
    for (const [name, reactor] of this.reactors) {
      status.push({
        name,
        isRunning: reactor.isRunning
      })
    }
    
    return status
  }
}

/**
 * 创建Reactor的便捷函数
 */
export function createReactor(reactorFn: ReactorFunction, config: ReactorConfig): ReactorRoot {
  return ReactorManager.getInstance().createReactor(reactorFn, config)
}

/**
 * 启动Reactor的便捷函数
 */
export function startReactor(name: string): boolean {
  const reactor = ReactorManager.getInstance().getReactor(name)
  if (reactor) {
    reactor.run()
    return true
  }
  return false
}

/**
 * 停止Reactor的便捷函数
 */
export function stopReactor(name: string): boolean {
  const reactor = ReactorManager.getInstance().getReactor(name)
  if (reactor) {
    reactor.stop()
    return true
  }
  return false
}

/**
 * 状态观察器
 */
export function watchState<S>(
  state: State<S>,
  callback: (newValue: S, oldValue: S) => void,
  immediate: boolean = false
): () => void {
  let oldValue = state.get({ noproxy: true })

  if (immediate) {
    callback(oldValue as S, oldValue as S)
  }

  // 使用简单的轮询机制来模拟状态观察
  let isActive = true
  const checkForChanges = () => {
    if (!isActive) return

    const newValue = state.get({ noproxy: true })
    if (newValue !== oldValue) {
      callback(newValue as S, oldValue as S)
      oldValue = newValue
    }

    if (isActive) {
      setTimeout(checkForChanges, 16) // 约60fps的检查频率
    }
  }

  checkForChanges()

  return () => {
    isActive = false
  }
}

/**
 * 计算属性
 */
export function computed<T>(
  computeFn: () => T,
  dependencies: State<any>[],
  config?: { name?: string; onError?: (error: Error) => void }
): State<T> {
  // 这是一个简化的计算属性实现
  // 实际实现可能需要更复杂的依赖追踪
  
  let cachedValue: T
  let isValid = false
  
  const compute = () => {
    try {
      cachedValue = computeFn()
      isValid = true
    } catch (error) {
      isValid = false
      if (config?.onError) {
        config.onError(error as Error)
      } else {
        console.error(`Computed ${config?.name || 'anonymous'} error:`, error)
      }
      throw error
    }
  }
  
  // 初始计算
  compute()
  
  // 监听依赖变化 - 使用简单的轮询机制
  let isWatching = true
  const watchDependencies = () => {
    if (!isWatching) return

    // 简单地标记为无效，下次访问时重新计算
    isValid = false

    setTimeout(watchDependencies, 100) // 每100ms检查一次
  }

  if (dependencies.length > 0) {
    watchDependencies()
  }

  // 创建一个真实的 hookstate 实例
  const computedState = hookstate(() => {
    if (!isValid) {
      compute()
    }
    return cachedValue
  })

  return computedState
}

/**
 * 副作用Hook
 */
export function useEffect(
  effectFn: () => void | (() => void),
  dependencies: State<any>[],
  config?: { name?: string; immediate?: boolean }
): () => void {
  let cleanupFn: (() => void) | undefined
  let isActive = true
  
  const runEffect = () => {
    if (!isActive) return
    
    // 清理上一次的副作用
    if (cleanupFn) {
      cleanupFn()
      cleanupFn = undefined
    }
    
    try {
      const result = effectFn()
      if (typeof result === 'function') {
        cleanupFn = result
      }
    } catch (error) {
      console.error(`Effect ${config?.name || 'anonymous'} error:`, error)
    }
  }
  
  // 立即执行（如果配置了immediate）
  if (config?.immediate !== false) {
    runEffect()
  }
  
  // 监听依赖变化 - 使用简单的轮询机制
  let lastDependencyValues = dependencies.map(dep => dep.get({ noproxy: true }))

  const checkDependencies = () => {
    if (!isActive) return

    const currentValues = dependencies.map(dep => dep.get({ noproxy: true }))
    const hasChanged = currentValues.some((value, index) => value !== lastDependencyValues[index])

    if (hasChanged) {
      lastDependencyValues = currentValues
      runEffect()
    }

    if (isActive) {
      setTimeout(checkDependencies, 16) // 约60fps的检查频率
    }
  }

  if (dependencies.length > 0) {
    checkDependencies()
  }

  // 返回清理函数
  return () => {
    isActive = false

    if (cleanupFn) {
      cleanupFn()
    }
  }
}

/**
 * 全局Reactor管理器实例
 */
export const globalReactorManager = ReactorManager.getInstance()
