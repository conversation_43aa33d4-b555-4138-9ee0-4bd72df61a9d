/**
 * DL-Engine 网络状态同步系统
 * 处理多客户端状态同步和网络通信
 */

import { 
  Action, 
  ResolvedActionType, 
  Topic, 
  PeerID, 
  UserID, 
  NetworkState,
  NetworkStats,
  PeerInfo 
} from '../types'

/**
 * 网络连接类型
 */
export enum NetworkConnectionType {
  WEBSOCKET = 'websocket',
  WEBRTC = 'webrtc',
  OFFLINE = 'offline'
}

/**
 * 网络事件类型
 */
export enum NetworkEventType {
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  PEER_JOINED = 'peer_joined',
  PEER_LEFT = 'peer_left',
  ACTION_RECEIVED = 'action_received',
  STATE_SYNC = 'state_sync',
  ERROR = 'error'
}

/**
 * 网络事件
 */
export interface NetworkEvent {
  type: NetworkEventType
  data?: any
  timestamp: number
  source?: PeerID
}

/**
 * 网络配置
 */
export interface NetworkConfig {
  /** 连接类型 */
  connectionType: NetworkConnectionType
  
  /** 服务器URL */
  serverUrl?: string
  
  /** 自动重连 */
  autoReconnect?: boolean
  
  /** 重连间隔（毫秒） */
  reconnectInterval?: number
  
  /** 最大重连次数 */
  maxReconnectAttempts?: number
  
  /** 心跳间隔（毫秒） */
  heartbeatInterval?: number
  
  /** 状态同步间隔（毫秒） */
  stateSyncInterval?: number
}

/**
 * 网络管理器接口
 */
export interface INetworkManager {
  /** 连接到网络 */
  connect(config: NetworkConfig): Promise<void>
  
  /** 断开网络连接 */
  disconnect(): Promise<void>
  
  /** 发送动作 */
  sendAction(action: ResolvedActionType, target?: PeerID | PeerID[]): Promise<void>
  
  /** 广播动作 */
  broadcastAction(action: ResolvedActionType): Promise<void>
  
  /** 同步状态 */
  syncState(stateName: string, stateData: any): Promise<void>
  
  /** 获取网络状态 */
  getNetworkState(): NetworkState
  
  /** 添加事件监听器 */
  addEventListener(type: NetworkEventType, listener: (event: NetworkEvent) => void): void
  
  /** 移除事件监听器 */
  removeEventListener(type: NetworkEventType, listener: (event: NetworkEvent) => void): void
}

/**
 * 简单的网络管理器实现
 */
export class SimpleNetworkManager implements INetworkManager {
  private config?: NetworkConfig
  private connection?: WebSocket
  private isConnected: boolean = false
  private eventListeners: Map<NetworkEventType, Set<(event: NetworkEvent) => void>> = new Map()
  private networkState: NetworkState
  private reconnectAttempts: number = 0
  private heartbeatTimer?: NodeJS.Timeout
  private stateSyncTimer?: NodeJS.Timeout
  
  constructor() {
    this.networkState = {
      connected: false,
      connectionType: NetworkConnectionType.OFFLINE,
      latency: 0,
      bandwidth: 0,
      peers: new Map(),
      stats: {
        bytesSent: 0,
        bytesReceived: 0,
        messagesSent: 0,
        messagesReceived: 0,
        connectionTime: 0,
        lastActivity: 0
      }
    }
  }
  
  async connect(config: NetworkConfig): Promise<void> {
    this.config = config
    
    if (config.connectionType === NetworkConnectionType.OFFLINE) {
      this.networkState.connected = false
      this.networkState.connectionType = NetworkConnectionType.OFFLINE
      return
    }
    
    if (config.connectionType === NetworkConnectionType.WEBSOCKET && config.serverUrl) {
      await this.connectWebSocket(config.serverUrl)
    }
    
    // 启动心跳
    if (config.heartbeatInterval) {
      this.startHeartbeat(config.heartbeatInterval)
    }
    
    // 启动状态同步
    if (config.stateSyncInterval) {
      this.startStateSync(config.stateSyncInterval)
    }
  }
  
  async disconnect(): Promise<void> {
    this.isConnected = false
    this.networkState.connected = false
    
    if (this.connection) {
      this.connection.close()
      this.connection = undefined
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = undefined
    }
    
    if (this.stateSyncTimer) {
      clearInterval(this.stateSyncTimer)
      this.stateSyncTimer = undefined
    }
    
    this.emitEvent({
      type: NetworkEventType.DISCONNECTED,
      timestamp: Date.now()
    })
  }
  
  async sendAction(action: ResolvedActionType, target?: PeerID | PeerID[]): Promise<void> {
    if (!this.isConnected || !this.connection) {
      throw new Error('Not connected to network')
    }
    
    const message = {
      type: 'action',
      action,
      target,
      timestamp: Date.now()
    }
    
    this.connection.send(JSON.stringify(message))
    this.networkState.stats.messagesSent++
  }
  
  async broadcastAction(action: ResolvedActionType): Promise<void> {
    return this.sendAction(action)
  }
  
  async syncState(stateName: string, stateData: any): Promise<void> {
    if (!this.isConnected || !this.connection) {
      return
    }
    
    const message = {
      type: 'state_sync',
      stateName,
      stateData,
      timestamp: Date.now()
    }
    
    this.connection.send(JSON.stringify(message))
    this.networkState.stats.messagesSent++
  }
  
  getNetworkState(): NetworkState {
    return { ...this.networkState }
  }
  
  addEventListener(type: NetworkEventType, listener: (event: NetworkEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set())
    }
    this.eventListeners.get(type)!.add(listener)
  }
  
  removeEventListener(type: NetworkEventType, listener: (event: NetworkEvent) => void): void {
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      listeners.delete(listener)
    }
  }
  
  private async connectWebSocket(url: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.connection = new WebSocket(url)
        
        this.connection.onopen = () => {
          this.isConnected = true
          this.networkState.connected = true
          this.networkState.connectionType = NetworkConnectionType.WEBSOCKET
          this.networkState.stats.connectionTime = Date.now()
          this.reconnectAttempts = 0
          
          this.emitEvent({
            type: NetworkEventType.CONNECTED,
            timestamp: Date.now()
          })
          
          resolve()
        }
        
        this.connection.onmessage = (event) => {
          this.handleMessage(event.data)
        }
        
        this.connection.onclose = () => {
          this.isConnected = false
          this.networkState.connected = false
          
          this.emitEvent({
            type: NetworkEventType.DISCONNECTED,
            timestamp: Date.now()
          })
          
          // 自动重连
          if (this.config?.autoReconnect && this.reconnectAttempts < (this.config.maxReconnectAttempts || 5)) {
            setTimeout(() => {
              this.reconnectAttempts++
              this.connect(this.config!)
            }, this.config.reconnectInterval || 5000)
          }
        }
        
        this.connection.onerror = (error) => {
          this.emitEvent({
            type: NetworkEventType.ERROR,
            data: error,
            timestamp: Date.now()
          })
          
          reject(error)
        }
        
      } catch (error) {
        reject(error)
      }
    })
  }
  
  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data)
      this.networkState.stats.messagesReceived++
      this.networkState.stats.lastActivity = Date.now()
      
      switch (message.type) {
        case 'action':
          this.emitEvent({
            type: NetworkEventType.ACTION_RECEIVED,
            data: message.action,
            timestamp: Date.now(),
            source: message.source
          })
          break
          
        case 'state_sync':
          this.emitEvent({
            type: NetworkEventType.STATE_SYNC,
            data: {
              stateName: message.stateName,
              stateData: message.stateData
            },
            timestamp: Date.now(),
            source: message.source
          })
          break
          
        case 'peer_joined':
          this.networkState.peers.set(message.peerId, message.peerInfo)
          this.emitEvent({
            type: NetworkEventType.PEER_JOINED,
            data: message.peerInfo,
            timestamp: Date.now()
          })
          break
          
        case 'peer_left':
          this.networkState.peers.delete(message.peerId)
          this.emitEvent({
            type: NetworkEventType.PEER_LEFT,
            data: { peerId: message.peerId },
            timestamp: Date.now()
          })
          break
      }
      
    } catch (error) {
      console.error('Failed to parse network message:', error)
    }
  }
  
  private emitEvent(event: NetworkEvent): void {
    const listeners = this.eventListeners.get(event.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error('Network event listener error:', error)
        }
      })
    }
  }
  
  private startHeartbeat(interval: number): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.connection) {
        const heartbeat = {
          type: 'heartbeat',
          timestamp: Date.now()
        }
        this.connection.send(JSON.stringify(heartbeat))
      }
    }, interval)
  }
  
  private startStateSync(interval: number): void {
    this.stateSyncTimer = setInterval(() => {
      // 这里可以实现定期状态同步逻辑
      // 例如同步关键状态到服务器
    }, interval)
  }
}

/**
 * 全局网络管理器实例
 */
export const globalNetworkManager = new SimpleNetworkManager()

/**
 * 网络工具函数
 */
export const NetworkUtils = {
  /**
   * 生成唯一的PeerID
   */
  generatePeerID(): PeerID {
    return `peer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` as PeerID
  },
  
  /**
   * 检查网络连接状态
   */
  isOnline(): boolean {
    return navigator.onLine
  },
  
  /**
   * 估算网络延迟
   */
  async measureLatency(url: string): Promise<number> {
    const start = performance.now()
    try {
      await fetch(url, { method: 'HEAD', mode: 'no-cors' })
      return performance.now() - start
    } catch {
      return -1
    }
  }
}
