/**
 * DL-Engine 内容推荐器
 * 专门用于学习内容的个性化推荐
 */

import { Entity } from '@etherealengine/ecs'
import { 
  BaseRecommendationEngine, 
  RecommendationItem, 
  RecommendationContext, 
  RecommendationResult,
  RecommendationConfig 
} from './RecommendationEngine'

/**
 * 学习内容元数据
 */
export interface ContentMetadata {
  /** 内容ID */
  id: string
  
  /** 内容标题 */
  title: string
  
  /** 内容描述 */
  description: string
  
  /** 内容类型 */
  type: 'video' | 'text' | 'interactive' | 'quiz' | 'exercise' | 'project'
  
  /** 难度级别 (1-5) */
  difficulty: number
  
  /** 主题标签 */
  topics: string[]
  
  /** 技能标签 */
  skills: string[]
  
  /** 预估学习时间（分钟） */
  estimatedDuration: number
  
  /** 前置要求 */
  prerequisites: string[]
  
  /** 学习目标 */
  learningObjectives: string[]
  
  /** 内容质量评分 */
  qualityScore: number
  
  /** 受欢迎程度 */
  popularity: number
  
  /** 创建时间 */
  createdAt: Date
  
  /** 更新时间 */
  updatedAt: Date
}

/**
 * 用户学习档案
 */
export interface UserLearningProfile {
  /** 用户ID */
  userId: Entity
  
  /** 当前技能水平 */
  skillLevels: Record<string, number>
  
  /** 学习偏好 */
  preferences: {
    /** 偏好的内容类型 */
    contentTypes: string[]
    
    /** 偏好的难度 */
    difficultyRange: [number, number]
    
    /** 偏好的学习时长 */
    sessionDuration: number
    
    /** 学习风格 */
    learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'reading'
  }
  
  /** 学习历史 */
  learningHistory: {
    /** 已完成的内容 */
    completedContent: string[]
    
    /** 正在学习的内容 */
    inProgressContent: string[]
    
    /** 收藏的内容 */
    bookmarkedContent: string[]
    
    /** 跳过的内容 */
    skippedContent: string[]
  }
  
  /** 学习目标 */
  learningGoals: string[]
  
  /** 最后活动时间 */
  lastActiveAt: Date
}

/**
 * 内容推荐器
 */
export class ContentRecommender extends BaseRecommendationEngine {
  private contentDatabase = new Map<string, ContentMetadata>()
  private userProfiles = new Map<string, UserLearningProfile>()
  private contentSimilarity = new Map<string, Map<string, number>>()
  
  constructor(config?: Partial<RecommendationConfig>) {
    super(config)
  }
  
  /**
   * 添加内容到数据库
   */
  addContent(content: ContentMetadata): void {
    this.contentDatabase.set(content.id, content)
    this.updateContentSimilarity(content)
  }
  
  /**
   * 更新用户档案
   */
  updateUserProfile(profile: UserLearningProfile): void {
    this.userProfiles.set(profile.userId.toString(), profile)
  }
  
  /**
   * 生成推荐
   */
  async generateRecommendations(
    context: RecommendationContext
  ): Promise<RecommendationResult> {
    const startTime = Date.now()
    const userProfile = this.userProfiles.get(context.userId.toString())
    
    if (!userProfile) {
      throw new Error('User profile not found')
    }
    
    const recommendations: RecommendationItem[] = []
    
    // 基于内容的推荐
    if (this.config.enabledAlgorithms.includes('content_based')) {
      const contentBasedRecs = await this.generateContentBasedRecommendations(
        userProfile, 
        context
      )
      recommendations.push(...contentBasedRecs)
    }
    
    // 基于协同过滤的推荐
    if (this.config.enabledAlgorithms.includes('collaborative_filtering')) {
      const collaborativeRecs = await this.generateCollaborativeRecommendations(
        userProfile, 
        context
      )
      recommendations.push(...collaborativeRecs)
    }
    
    // 基于知识的推荐
    if (this.config.enabledAlgorithms.includes('knowledge_based')) {
      const knowledgeBasedRecs = await this.generateKnowledgeBasedRecommendations(
        userProfile, 
        context
      )
      recommendations.push(...knowledgeBasedRecs)
    }
    
    // 热门推荐
    if (this.config.enabledAlgorithms.includes('popularity')) {
      const popularityRecs = await this.generatePopularityRecommendations(
        userProfile, 
        context
      )
      recommendations.push(...popularityRecs)
    }
    
    // 合并和排序
    const mergedRecommendations = this.mergeRecommendations(recommendations)
    const finalRecommendations = mergedRecommendations.slice(0, this.config.limit)
    
    return {
      items: finalRecommendations,
      total: finalRecommendations.length,
      algorithmUsage: this.calculateAlgorithmUsage(recommendations),
      generatedAt: new Date(),
      processingTime: Date.now() - startTime,
      fromCache: false
    }
  }
  
  /**
   * 基于内容的推荐
   */
  private async generateContentBasedRecommendations(
    userProfile: UserLearningProfile,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = []
    
    // 获取用户最近学习的内容
    const recentContent = userProfile.learningHistory.completedContent.slice(-5)
    
    for (const contentId of recentContent) {
      const similarContent = this.findSimilarContent(contentId, 3)
      
      for (const [similarId, similarity] of similarContent) {
        const content = this.contentDatabase.get(similarId)
        if (!content || userProfile.learningHistory.completedContent.includes(similarId)) {
          continue
        }
        
        // 检查难度匹配
        const difficultyMatch = this.checkDifficultyMatch(content, userProfile)
        if (!difficultyMatch) continue
        
        // 检查前置要求
        const prerequisitesMet = this.checkPrerequisites(content, userProfile)
        if (!prerequisitesMet) continue
        
        const score = similarity * 0.7 + difficultyMatch * 0.3
        
        recommendations.push({
          id: content.id,
          type: 'content',
          title: content.title,
          description: content.description,
          score,
          reason: `与您学习过的"${this.contentDatabase.get(contentId)?.title}"相似`,
          algorithm: 'content_based',
          confidence: similarity,
          metadata: {
            similarity,
            difficultyMatch,
            contentType: content.type,
            topics: content.topics
          },
          generatedAt: new Date()
        })
      }
    }
    
    return recommendations
  }
  
  /**
   * 协同过滤推荐
   */
  private async generateCollaborativeRecommendations(
    userProfile: UserLearningProfile,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = []
    
    // 找到相似用户
    const similarUsers = this.findSimilarUsers(userProfile, 10)
    
    for (const [similarUserId, similarity] of similarUsers) {
      const similarUserProfile = this.userProfiles.get(similarUserId)
      if (!similarUserProfile) continue
      
      // 获取相似用户喜欢但当前用户未学习的内容
      const candidateContent = similarUserProfile.learningHistory.completedContent
        .filter(contentId => !userProfile.learningHistory.completedContent.includes(contentId))
        .slice(0, 5)
      
      for (const contentId of candidateContent) {
        const content = this.contentDatabase.get(contentId)
        if (!content) continue
        
        const score = similarity * 0.8
        
        recommendations.push({
          id: content.id,
          type: 'content',
          title: content.title,
          description: content.description,
          score,
          reason: '与您学习偏好相似的用户也学习了这个内容',
          algorithm: 'collaborative_filtering',
          confidence: similarity,
          metadata: {
            similarity,
            similarUserId,
            contentType: content.type
          },
          generatedAt: new Date()
        })
      }
    }
    
    return recommendations
  }
  
  /**
   * 基于知识的推荐
   */
  private async generateKnowledgeBasedRecommendations(
    userProfile: UserLearningProfile,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = []
    
    // 基于学习目标推荐
    for (const goal of userProfile.learningGoals) {
      const relevantContent = Array.from(this.contentDatabase.values())
        .filter(content => 
          content.learningObjectives.some(obj => 
            obj.toLowerCase().includes(goal.toLowerCase())
          ) ||
          content.topics.some(topic => 
            topic.toLowerCase().includes(goal.toLowerCase())
          )
        )
        .filter(content => 
          !userProfile.learningHistory.completedContent.includes(content.id)
        )
        .slice(0, 3)
      
      for (const content of relevantContent) {
        const difficultyMatch = this.checkDifficultyMatch(content, userProfile)
        const prerequisitesMet = this.checkPrerequisites(content, userProfile)
        
        if (!prerequisitesMet) continue
        
        const score = 0.8 * difficultyMatch
        
        recommendations.push({
          id: content.id,
          type: 'content',
          title: content.title,
          description: content.description,
          score,
          reason: `有助于实现您的学习目标："${goal}"`,
          algorithm: 'knowledge_based',
          confidence: 0.9,
          metadata: {
            goal,
            difficultyMatch,
            contentType: content.type
          },
          generatedAt: new Date()
        })
      }
    }
    
    return recommendations
  }
  
  /**
   * 热门推荐
   */
  private async generatePopularityRecommendations(
    userProfile: UserLearningProfile,
    context: RecommendationContext
  ): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = []
    
    const popularContent = Array.from(this.contentDatabase.values())
      .filter(content => 
        !userProfile.learningHistory.completedContent.includes(content.id)
      )
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, 5)
    
    for (const content of popularContent) {
      const difficultyMatch = this.checkDifficultyMatch(content, userProfile)
      if (difficultyMatch < 0.3) continue
      
      const score = (content.popularity / 100) * 0.6 + difficultyMatch * 0.4
      
      recommendations.push({
        id: content.id,
        type: 'content',
        title: content.title,
        description: content.description,
        score,
        reason: '热门学习内容',
        algorithm: 'popularity',
        confidence: 0.7,
        metadata: {
          popularity: content.popularity,
          difficultyMatch,
          contentType: content.type
        },
        generatedAt: new Date()
      })
    }
    
    return recommendations
  }
  
  /**
   * 更新用户反馈
   */
  async updateFeedback(
    userId: Entity,
    itemId: string,
    feedback: 'like' | 'dislike' | 'view' | 'click' | 'complete'
  ): Promise<void> {
    const userProfile = this.userProfiles.get(userId.toString())
    if (!userProfile) return
    
    switch (feedback) {
      case 'complete':
        if (!userProfile.learningHistory.completedContent.includes(itemId)) {
          userProfile.learningHistory.completedContent.push(itemId)
        }
        // 从进行中移除
        const inProgressIndex = userProfile.learningHistory.inProgressContent.indexOf(itemId)
        if (inProgressIndex > -1) {
          userProfile.learningHistory.inProgressContent.splice(inProgressIndex, 1)
        }
        break
        
      case 'like':
        if (!userProfile.learningHistory.bookmarkedContent.includes(itemId)) {
          userProfile.learningHistory.bookmarkedContent.push(itemId)
        }
        break
        
      case 'dislike':
        if (!userProfile.learningHistory.skippedContent.includes(itemId)) {
          userProfile.learningHistory.skippedContent.push(itemId)
        }
        break
    }
    
    userProfile.lastActiveAt = new Date()
    this.userProfiles.set(userId.toString(), userProfile)
  }
  
  /**
   * 获取推荐解释
   */
  async explainRecommendation(
    userId: Entity,
    itemId: string
  ): Promise<string> {
    const content = this.contentDatabase.get(itemId)
    const userProfile = this.userProfiles.get(userId.toString())
    
    if (!content || !userProfile) {
      return '推荐理由不可用'
    }
    
    const explanations: string[] = []
    
    // 基于技能匹配
    const matchingSkills = content.skills.filter(skill => 
      userProfile.skillLevels[skill] !== undefined
    )
    if (matchingSkills.length > 0) {
      explanations.push(`与您的技能领域匹配：${matchingSkills.join(', ')}`)
    }
    
    // 基于学习目标
    const matchingGoals = userProfile.learningGoals.filter(goal =>
      content.learningObjectives.some(obj => 
        obj.toLowerCase().includes(goal.toLowerCase())
      )
    )
    if (matchingGoals.length > 0) {
      explanations.push(`有助于实现您的学习目标：${matchingGoals.join(', ')}`)
    }
    
    // 基于难度适配
    const avgSkillLevel = Object.values(userProfile.skillLevels).reduce((a, b) => a + b, 0) / 
                         Object.values(userProfile.skillLevels).length
    if (Math.abs(content.difficulty - avgSkillLevel) <= 1) {
      explanations.push('难度适合您当前的学习水平')
    }
    
    return explanations.length > 0 ? explanations.join('；') : '基于您的学习历史和偏好推荐'
  }
  
  // 辅助方法
  private updateContentSimilarity(content: ContentMetadata): void {
    if (!this.contentSimilarity.has(content.id)) {
      this.contentSimilarity.set(content.id, new Map())
    }
    
    // 计算与其他内容的相似度
    for (const [otherId, otherContent] of this.contentDatabase) {
      if (otherId === content.id) continue
      
      const similarity = this.calculateContentSimilarity(content, otherContent)
      this.contentSimilarity.get(content.id)!.set(otherId, similarity)
      
      if (!this.contentSimilarity.has(otherId)) {
        this.contentSimilarity.set(otherId, new Map())
      }
      this.contentSimilarity.get(otherId)!.set(content.id, similarity)
    }
  }
  
  private calculateContentSimilarity(
    content1: ContentMetadata, 
    content2: ContentMetadata
  ): number {
    // 主题相似度
    const topicSimilarity = this.calculateJaccardSimilarity(content1.topics, content2.topics)
    
    // 技能相似度
    const skillSimilarity = this.calculateJaccardSimilarity(content1.skills, content2.skills)
    
    // 难度相似度
    const difficultySimilarity = 1 - Math.abs(content1.difficulty - content2.difficulty) / 4
    
    // 类型相似度
    const typeSimilarity = content1.type === content2.type ? 1 : 0
    
    return (topicSimilarity * 0.4 + skillSimilarity * 0.3 + 
            difficultySimilarity * 0.2 + typeSimilarity * 0.1)
  }
  
  private calculateJaccardSimilarity(set1: string[], set2: string[]): number {
    const intersection = set1.filter(item => set2.includes(item)).length
    const union = new Set([...set1, ...set2]).size
    return union === 0 ? 0 : intersection / union
  }
  
  private findSimilarContent(contentId: string, limit: number): [string, number][] {
    const similarities = this.contentSimilarity.get(contentId)
    if (!similarities) return []
    
    return Array.from(similarities.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
  }
  
  private findSimilarUsers(userProfile: UserLearningProfile, limit: number): [string, number][] {
    const similarities: [string, number][] = []
    
    for (const [userId, otherProfile] of this.userProfiles) {
      if (userId === userProfile.userId.toString()) continue
      
      const similarity = this.calculateUserSimilarity(userProfile, otherProfile)
      similarities.push([userId, similarity])
    }
    
    return similarities
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
  }
  
  private calculateUserSimilarity(
    user1: UserLearningProfile, 
    user2: UserLearningProfile
  ): number {
    // 学习历史相似度
    const historySimilarity = this.calculateJaccardSimilarity(
      user1.learningHistory.completedContent,
      user2.learningHistory.completedContent
    )
    
    // 技能相似度
    const skillSimilarity = this.calculateSkillSimilarity(
      user1.skillLevels,
      user2.skillLevels
    )
    
    // 偏好相似度
    const preferenceSimilarity = this.calculateJaccardSimilarity(
      user1.preferences.contentTypes,
      user2.preferences.contentTypes
    )
    
    return historySimilarity * 0.5 + skillSimilarity * 0.3 + preferenceSimilarity * 0.2
  }
  
  private calculateSkillSimilarity(
    skills1: Record<string, number>,
    skills2: Record<string, number>
  ): number {
    const commonSkills = Object.keys(skills1).filter(skill => skills2[skill] !== undefined)
    if (commonSkills.length === 0) return 0
    
    let totalSimilarity = 0
    for (const skill of commonSkills) {
      const similarity = 1 - Math.abs(skills1[skill] - skills2[skill]) / 4
      totalSimilarity += similarity
    }
    
    return totalSimilarity / commonSkills.length
  }
  
  private checkDifficultyMatch(content: ContentMetadata, userProfile: UserLearningProfile): number {
    const [minDiff, maxDiff] = userProfile.preferences.difficultyRange
    if (content.difficulty >= minDiff && content.difficulty <= maxDiff) {
      return 1.0
    }
    
    const distance = Math.min(
      Math.abs(content.difficulty - minDiff),
      Math.abs(content.difficulty - maxDiff)
    )
    
    return Math.max(0, 1 - distance / 2)
  }
  
  private checkPrerequisites(content: ContentMetadata, userProfile: UserLearningProfile): boolean {
    return content.prerequisites.every(prereq =>
      userProfile.learningHistory.completedContent.includes(prereq)
    )
  }
  
  private mergeRecommendations(recommendations: RecommendationItem[]): RecommendationItem[] {
    const merged = new Map<string, RecommendationItem>()
    
    for (const rec of recommendations) {
      const existing = merged.get(rec.id)
      if (!existing || rec.score > existing.score) {
        merged.set(rec.id, rec)
      }
    }
    
    return Array.from(merged.values())
      .sort((a, b) => b.score - a.score)
  }
  
  private calculateAlgorithmUsage(recommendations: RecommendationItem[]): Record<string, number> {
    const usage: Record<string, number> = {}
    
    for (const rec of recommendations) {
      usage[rec.algorithm] = (usage[rec.algorithm] || 0) + 1
    }
    
    return usage
  }
}
