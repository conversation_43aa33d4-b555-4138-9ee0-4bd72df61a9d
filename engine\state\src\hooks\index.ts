/**
 * DL-Engine 状态管理Hooks
 * 提供React风格的状态管理Hooks
 */

import { State } from '@hookstate/core'
import { StateDefinition, DeepReadonly, ReceptorMap, Action, ResolvedActionType } from '../types'
import { getMutableState, getState } from '../StateFunctions'
import { StateUtils, CommonUtils } from '../utils'

/**
 * Hook依赖数组类型
 */
export type HookDependencies = any[]

/**
 * 状态选择器函数类型
 */
export type StateSelector<S, T> = (state: DeepReadonly<S>) => T

/**
 * 副作用清理函数类型
 */
export type EffectCleanup = () => void

/**
 * 副作用函数类型
 */
export type EffectFunction = () => void | EffectCleanup

/**
 * 使用状态Hook
 */
export function useState<S, I = {}, E = {}, R extends ReceptorMap = {}>(
  stateDefinition: StateDefinition<S, I, E, R>
): [DeepReadonly<S>, State<S, I & E>] {
  const mutableState = getMutableState(stateDefinition)
  const readonlyState = getState(stateDefinition)

  return [readonlyState, mutableState as State<S, I & E>]
}

/**
 * 使用状态选择器Hook
 */
export function useStateSelector<S, T, I = {}, E = {}, R extends ReceptorMap = {}>(
  stateDefinition: StateDefinition<S, I, E, R>,
  selector: StateSelector<S, T>,
  dependencies?: HookDependencies
): T {
  const state = getState(stateDefinition)
  
  // 简化的选择器实现，实际应用中可能需要更复杂的缓存和比较逻辑
  return selector(state)
}

/**
 * 使用计算状态Hook
 */
export function useComputedState<T>(
  computeFn: () => T,
  dependencies: HookDependencies = []
): T {
  // 简化的计算状态实现
  return computeFn()
}

/**
 * 使用副作用Hook
 */
export function useEffect(
  effect: EffectFunction,
  dependencies?: HookDependencies
): void {
  // 简化的副作用实现
  // 实际应用中需要更复杂的依赖追踪和清理逻辑
  
  let cleanup: EffectCleanup | undefined
  
  const runEffect = () => {
    if (cleanup) {
      cleanup()
    }
    
    const result = effect()
    if (typeof result === 'function') {
      cleanup = result
    }
  }
  
  // 立即执行副作用
  runEffect()
  
  // 在组件卸载时清理
  // 注意：这是一个简化的实现，实际需要与组件生命周期集成
}

/**
 * 使用异步状态Hook
 */
export function useAsyncState<T>(
  asyncFn: () => Promise<T>,
  dependencies?: HookDependencies
): {
  data: T | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
} {
  let data: T | null = null
  let loading = false
  let error: Error | null = null
  
  const execute = async () => {
    loading = true
    error = null
    
    try {
      data = await asyncFn()
    } catch (err) {
      error = err as Error
    } finally {
      loading = false
    }
  }
  
  // 立即执行
  execute()
  
  return {
    data,
    loading,
    error,
    refetch: execute
  }
}

/**
 * 使用防抖Hook
 */
export function useDebounce<T>(value: T, delay: number): T {
  let debouncedValue = value
  
  // 简化的防抖实现
  const debouncedUpdate = CommonUtils.debounce((newValue: T) => {
    debouncedValue = newValue
  }, delay)
  
  debouncedUpdate(value)
  
  return debouncedValue
}

/**
 * 使用节流Hook
 */
export function useThrottle<T>(value: T, delay: number): T {
  let throttledValue = value
  
  // 简化的节流实现
  const throttledUpdate = CommonUtils.throttle((newValue: T) => {
    throttledValue = newValue
  }, delay)
  
  throttledUpdate(value)
  
  return throttledValue
}

/**
 * 使用本地存储Hook
 */
export function useLocalStorage<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void, () => void] {
  const getStoredValue = (): T => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('Error reading from localStorage:', error)
      return defaultValue
    }
  }
  
  let storedValue = getStoredValue()
  
  const setValue = (value: T) => {
    try {
      storedValue = value
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Error writing to localStorage:', error)
    }
  }
  
  const removeValue = () => {
    try {
      localStorage.removeItem(key)
      storedValue = defaultValue
    } catch (error) {
      console.error('Error removing from localStorage:', error)
    }
  }
  
  return [storedValue, setValue, removeValue]
}

/**
 * 使用会话存储Hook
 */
export function useSessionStorage<T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void, () => void] {
  const getStoredValue = (): T => {
    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error('Error reading from sessionStorage:', error)
      return defaultValue
    }
  }
  
  let storedValue = getStoredValue()
  
  const setValue = (value: T) => {
    try {
      storedValue = value
      sessionStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Error writing to sessionStorage:', error)
    }
  }
  
  const removeValue = () => {
    try {
      sessionStorage.removeItem(key)
      storedValue = defaultValue
    } catch (error) {
      console.error('Error removing from sessionStorage:', error)
    }
  }
  
  return [storedValue, setValue, removeValue]
}

/**
 * 使用状态历史Hook
 */
export function useStateHistory<T>(
  initialValue: T,
  maxHistorySize: number = 10
): {
  value: T
  setValue: (value: T) => void
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
  history: T[]
  historyIndex: number
} {
  let history: T[] = [initialValue]
  let historyIndex = 0
  
  const setValue = (value: T) => {
    // 移除当前索引之后的历史记录
    history = history.slice(0, historyIndex + 1)
    
    // 添加新值
    history.push(value)
    historyIndex = history.length - 1
    
    // 限制历史记录大小
    if (history.length > maxHistorySize) {
      history = history.slice(-maxHistorySize)
      historyIndex = history.length - 1
    }
  }
  
  const undo = () => {
    if (historyIndex > 0) {
      historyIndex--
    }
  }
  
  const redo = () => {
    if (historyIndex < history.length - 1) {
      historyIndex++
    }
  }
  
  return {
    value: history[historyIndex],
    setValue,
    undo,
    redo,
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    history: [...history],
    historyIndex
  }
}

/**
 * 使用状态验证Hook
 */
export function useStateValidation<T>(
  value: T,
  validator: (value: T) => { valid: boolean; errors: string[] }
): {
  isValid: boolean
  errors: string[]
  validate: () => { valid: boolean; errors: string[] }
} {
  const result = validator(value)
  
  return {
    isValid: result.valid,
    errors: result.errors,
    validate: () => validator(value)
  }
}

/**
 * 使用状态比较Hook
 */
export function useStateComparison<T>(
  value: T,
  compareWith: T,
  compareFn?: (a: T, b: T) => boolean
): {
  isEqual: boolean
  hasChanged: boolean
} {
  const isEqual = compareFn ? compareFn(value, compareWith) : StateUtils.deepEqual(value, compareWith)
  
  return {
    isEqual,
    hasChanged: !isEqual
  }
}

/**
 * 使用状态性能监控Hook
 */
export function useStatePerformance<T>(
  value: T,
  name: string
): {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
} {
  let renderCount = 0
  let renderTimes: number[] = []
  
  const startTime = performance.now()
  
  // 模拟渲染完成
  setTimeout(() => {
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    renderCount++
    renderTimes.push(renderTime)
    
    // 限制记录数量
    if (renderTimes.length > 100) {
      renderTimes = renderTimes.slice(-50)
    }
  }, 0)
  
  const averageRenderTime = renderTimes.length > 0 
    ? renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length 
    : 0
  
  return {
    renderCount,
    lastRenderTime: renderTimes[renderTimes.length - 1] || 0,
    averageRenderTime
  }
}

/**
 * Hook工具函数
 */
export const HookUtils = {
  /**
   * 创建自定义Hook
   */
  createHook<T extends (...args: any[]) => any>(hookFn: T): T {
    return hookFn
  },
  
  /**
   * 组合多个Hook
   */
  combineHooks<T extends {}>(...hooks: Array<() => Partial<T>>): () => T {
    return () => {
      const result = {} as T
      for (const hook of hooks) {
        Object.assign(result, hook())
      }
      return result
    }
  },
  
  /**
   * 条件Hook
   */
  conditionalHook<T>(condition: boolean, hook: () => T, fallback: () => T): () => T {
    return () => condition ? hook() : fallback()
  }
}
