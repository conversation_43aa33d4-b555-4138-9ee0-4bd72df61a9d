/**
 * DL-Engine Three.js 集成模块
 * 提供与Three.js的深度集成功能
 */

import * as THREE from 'three'

/**
 * Three.js 版本信息
 */
export const ThreeVersion = THREE.REVISION

/**
 * Three.js 工具函数
 */
export const ThreeUtils = {
  /**
   * 创建默认场景
   */
  createDefaultScene(): THREE.Scene {
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000)
    return scene
  },

  /**
   * 创建默认相机
   */
  createDefaultCamera(aspect: number = 1): THREE.PerspectiveCamera {
    const camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000)
    camera.position.set(0, 0, 5)
    return camera
  },

  /**
   * 创建默认光源
   */
  createDefaultLights(): THREE.Light[] {
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
    directionalLight.position.set(1, 1, 1)
    directionalLight.castShadow = true
    
    return [ambientLight, directionalLight]
  },

  /**
   * 设置阴影
   */
  setupShadows(renderer: THREE.WebGLRenderer): void {
    renderer.shadowMap.enabled = true
    renderer.shadowMap.type = THREE.PCFSoftShadowMap
  },

  /**
   * 优化渲染器设置
   */
  optimizeRenderer(renderer: THREE.WebGLRenderer): void {
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    renderer.outputColorSpace = THREE.SRGBColorSpace
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 1
  },

  /**
   * 创建网格辅助线
   */
  createGridHelper(size: number = 10, divisions: number = 10): THREE.GridHelper {
    return new THREE.GridHelper(size, divisions)
  },

  /**
   * 创建坐标轴辅助线
   */
  createAxesHelper(size: number = 5): THREE.AxesHelper {
    return new THREE.AxesHelper(size)
  },

  /**
   * 计算包围盒
   */
  computeBoundingBox(object: THREE.Object3D): THREE.Box3 {
    const box = new THREE.Box3()
    box.setFromObject(object)
    return box
  },

  /**
   * 居中对象
   */
  centerObject(object: THREE.Object3D): void {
    const box = this.computeBoundingBox(object)
    const center = box.getCenter(new THREE.Vector3())
    object.position.sub(center)
  },

  /**
   * 缩放对象以适应指定大小
   */
  scaleToFit(object: THREE.Object3D, targetSize: number): void {
    const box = this.computeBoundingBox(object)
    const size = box.getSize(new THREE.Vector3())
    const maxDimension = Math.max(size.x, size.y, size.z)
    const scale = targetSize / maxDimension
    object.scale.setScalar(scale)
  }
}

/**
 * Three.js 扩展
 */
export const ThreeExtensions = {
  /**
   * 添加对象到场景并返回清理函数
   */
  addToScene(scene: THREE.Scene, object: THREE.Object3D): () => void {
    scene.add(object)
    return () => scene.remove(object)
  },

  /**
   * 批量添加对象到场景
   */
  addMultipleToScene(scene: THREE.Scene, objects: THREE.Object3D[]): () => void {
    objects.forEach(obj => scene.add(obj))
    return () => objects.forEach(obj => scene.remove(obj))
  },

  /**
   * 遍历场景中的所有网格
   */
  traverseMeshes(object: THREE.Object3D, callback: (mesh: THREE.Mesh) => void): void {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        callback(child)
      }
    })
  },

  /**
   * 获取场景中的所有材质
   */
  getAllMaterials(scene: THREE.Scene): THREE.Material[] {
    const materials: THREE.Material[] = []
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        if (Array.isArray(object.material)) {
          materials.push(...object.material)
        } else {
          materials.push(object.material)
        }
      }
    })
    return materials
  },

  /**
   * 释放几何体和材质资源
   */
  disposeObject(object: THREE.Object3D): void {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (child.geometry) {
          child.geometry.dispose()
        }
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(material => material.dispose())
          } else {
            child.material.dispose()
          }
        }
      }
    })
  }
}

/**
 * Three.js 常量
 */
export const ThreeConstants = {
  // 颜色空间
  ColorSpace: {
    SRGB: THREE.SRGBColorSpace,
    LINEAR: THREE.LinearSRGBColorSpace
  },

  // 色调映射
  ToneMapping: {
    NONE: THREE.NoToneMapping,
    LINEAR: THREE.LinearToneMapping,
    REINHARD: THREE.ReinhardToneMapping,
    CINEON: THREE.CineonToneMapping,
    ACES_FILMIC: THREE.ACESFilmicToneMapping
  },

  // 阴影类型
  ShadowType: {
    BASIC: THREE.BasicShadowMap,
    PCF: THREE.PCFShadowMap,
    PCF_SOFT: THREE.PCFSoftShadowMap,
    VSM: THREE.VSMShadowMap
  },

  // 纹理过滤
  TextureFilter: {
    NEAREST: THREE.NearestFilter,
    LINEAR: THREE.LinearFilter,
    NEAREST_MIPMAP_NEAREST: THREE.NearestMipmapNearestFilter,
    NEAREST_MIPMAP_LINEAR: THREE.NearestMipmapLinearFilter,
    LINEAR_MIPMAP_NEAREST: THREE.LinearMipmapNearestFilter,
    LINEAR_MIPMAP_LINEAR: THREE.LinearMipmapLinearFilter
  },

  // 纹理包装
  TextureWrap: {
    REPEAT: THREE.RepeatWrapping,
    CLAMP: THREE.ClampToEdgeWrapping,
    MIRROR: THREE.MirroredRepeatWrapping
  }
}

/**
 * 导出Three.js核心对象
 */
export { THREE }
export default THREE
