import * as React from 'react';
import CacheEntity from './Cache';
export var ATTR_TOKEN = 'data-token-hash';
export var ATTR_MARK = 'data-css-hash';
export var ATTR_DEV_CACHE_PATH = 'data-dev-cache-path';
// Mark css-in-js instance in style element
export var CSS_IN_JS_INSTANCE = '__cssinjs_instance__';
export var CSS_IN_JS_INSTANCE_ID = Math.random().toString(12).slice(2);
export function createCache() {
  if (typeof document !== 'undefined') {
    var styles = document.body.querySelectorAll("style[".concat(ATTR_MARK, "]"));
    var firstChild = document.head.firstChild;
    Array.from(styles).forEach(function (style) {
      style[CSS_IN_JS_INSTANCE] = style[CSS_IN_JS_INSTANCE] || CSS_IN_JS_INSTANCE_ID;
      document.head.insertBefore(style, firstChild);
    });
    // Deduplicate of moved styles
    var styleHash = {};
    Array.from(document.querySelectorAll("style[".concat(ATTR_MARK, "]"))).forEach(function (style) {
      var hash = style.getAttribute(ATTR_MARK);
      if (styleHash[hash]) {
        if (style[CSS_IN_JS_INSTANCE] === CSS_IN_JS_INSTANCE_ID) {
          var _style$parentNode;
          (_style$parentNode = style.parentNode) === null || _style$parentNode === void 0 ? void 0 : _style$parentNode.removeChild(style);
        }
      } else {
        styleHash[hash] = true;
      }
    });
  }
  return new CacheEntity();
}
var StyleContext = /*#__PURE__*/React.createContext({
  hashPriority: 'low',
  cache: createCache(),
  defaultCache: true
});
export var StyleProvider = function StyleProvider(props) {
  var autoClear = props.autoClear,
    mock = props.mock,
    cache = props.cache,
    hashPriority = props.hashPriority,
    children = props.children;
  var _React$useContext = React.useContext(StyleContext),
    parentCache = _React$useContext.cache,
    parentAutoClear = _React$useContext.autoClear,
    parentMock = _React$useContext.mock,
    parentDefaultCache = _React$useContext.defaultCache,
    parentHashPriority = _React$useContext.hashPriority;
  var context = React.useMemo(function () {
    return {
      autoClear: autoClear !== null && autoClear !== void 0 ? autoClear : parentAutoClear,
      mock: mock !== null && mock !== void 0 ? mock : parentMock,
      cache: cache || parentCache || createCache(),
      defaultCache: !cache && parentDefaultCache,
      hashPriority: hashPriority !== null && hashPriority !== void 0 ? hashPriority : parentHashPriority
    };
  }, [autoClear, parentAutoClear, parentMock, parentCache, mock, cache, parentDefaultCache, hashPriority, parentHashPriority]);
  return /*#__PURE__*/React.createElement(StyleContext.Provider, {
    value: context
  }, children);
};
export default StyleContext;