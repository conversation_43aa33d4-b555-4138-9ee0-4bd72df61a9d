/**
 * DL-Engine 发布脚本
 * 自动化版本管理和发布流程
 */

import { execSync } from 'child_process'
import { readFileSync, writeFileSync } from 'fs'
import { join } from 'path'

interface ReleaseOptions {
  type: 'patch' | 'minor' | 'major'
  prerelease?: string
  dryRun: boolean
  skipTests: boolean
  skipBuild: boolean
  tag: string
}

interface PackageInfo {
  name: string
  version: string
  path: string
}

/**
 * 发布管理器
 */
class ReleaseManager {
  private options: ReleaseOptions
  private packages: PackageInfo[] = []
  
  constructor(options: Partial<ReleaseOptions> = {}) {
    this.options = {
      type: 'patch',
      dryRun: false,
      skipTests: false,
      skipBuild: false,
      tag: 'latest',
      ...options
    }
  }
  
  /**
   * 执行发布流程
   */
  async release(): Promise<void> {
    console.log('🚀 Starting DL-Engine release process...')
    console.log(`Release type: ${this.options.type}`)
    console.log(`Dry run: ${this.options.dryRun}`)
    
    try {
      // 1. 预检查
      await this.preCheck()
      
      // 2. 运行测试
      if (!this.options.skipTests) {
        await this.runTests()
      }
      
      // 3. 构建项目
      if (!this.options.skipBuild) {
        await this.buildProject()
      }
      
      // 4. 更新版本号
      await this.updateVersions()
      
      // 5. 生成变更日志
      await this.generateChangelog()
      
      // 6. 创建Git标签
      await this.createGitTag()
      
      // 7. 发布到npm
      await this.publishToNpm()
      
      // 8. 推送到Git
      await this.pushToGit()
      
      console.log('✅ Release completed successfully!')
      
    } catch (error) {
      console.error('❌ Release failed:', error)
      throw error
    }
  }
  
  /**
   * 预检查
   */
  private async preCheck(): Promise<void> {
    console.log('🔍 Running pre-release checks...')
    
    // 检查Git状态
    try {
      const gitStatus = execSync('git status --porcelain', { encoding: 'utf-8' })
      if (gitStatus.trim() && !this.options.dryRun) {
        throw new Error('Working directory is not clean. Please commit or stash changes.')
      }
    } catch (error) {
      throw new Error('Failed to check Git status')
    }
    
    // 检查当前分支
    try {
      const currentBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' }).trim()
      if (currentBranch !== 'main' && currentBranch !== 'master' && !this.options.dryRun) {
        console.warn(`⚠️  Releasing from branch: ${currentBranch}`)
      }
    } catch (error) {
      throw new Error('Failed to check current branch')
    }
    
    // 检查npm认证
    try {
      execSync('npm whoami', { stdio: 'pipe' })
    } catch (error) {
      throw new Error('Not logged in to npm. Please run "npm login"')
    }
    
    // 发现所有包
    await this.discoverPackages()
    
    console.log('✅ Pre-checks passed')
  }
  
  /**
   * 发现所有包
   */
  private async discoverPackages(): Promise<void> {
    const rootPackage = JSON.parse(readFileSync('package.json', 'utf-8'))
    
    this.packages = [
      {
        name: rootPackage.name,
        version: rootPackage.version,
        path: '.'
      }
    ]
    
    // 发现工作空间包
    if (rootPackage.workspaces) {
      for (const workspace of rootPackage.workspaces) {
        // 这里简化处理，实际应该使用glob匹配
        const workspaceDirs = ['engine/core', 'engine/ecs', 'engine/physics', 'engine/state', 'engine/xr', 'engine/ai']
        
        for (const dir of workspaceDirs) {
          try {
            const packagePath = join(dir, 'package.json')
            const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'))
            
            if (!packageJson.private) {
              this.packages.push({
                name: packageJson.name,
                version: packageJson.version,
                path: dir
              })
            }
          } catch (error) {
            // 包不存在或无法读取，跳过
          }
        }
      }
    }
    
    console.log(`Found ${this.packages.length} packages to release`)
  }
  
  /**
   * 运行测试
   */
  private async runTests(): Promise<void> {
    console.log('🧪 Running tests...')
    
    try {
      execSync('pnpm test:all', { stdio: 'inherit' })
      console.log('✅ All tests passed')
    } catch (error) {
      throw new Error('Tests failed. Please fix failing tests before release.')
    }
  }
  
  /**
   * 构建项目
   */
  private async buildProject(): Promise<void> {
    console.log('🔨 Building project...')
    
    try {
      execSync('pnpm build:all:clean', { stdio: 'inherit' })
      console.log('✅ Build completed')
    } catch (error) {
      throw new Error('Build failed. Please fix build errors before release.')
    }
  }
  
  /**
   * 更新版本号
   */
  private async updateVersions(): Promise<void> {
    console.log('📝 Updating versions...')
    
    const newVersion = this.calculateNewVersion()
    
    for (const pkg of this.packages) {
      const packagePath = join(pkg.path, 'package.json')
      const packageJson = JSON.parse(readFileSync(packagePath, 'utf-8'))
      
      packageJson.version = newVersion
      
      if (!this.options.dryRun) {
        writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n')
      }
      
      console.log(`   ${pkg.name}: ${pkg.version} → ${newVersion}`)
    }
  }
  
  /**
   * 计算新版本号
   */
  private calculateNewVersion(): string {
    const currentVersion = this.packages[0].version
    const [major, minor, patch] = currentVersion.split('.').map(Number)
    
    switch (this.options.type) {
      case 'major':
        return `${major + 1}.0.0`
      case 'minor':
        return `${major}.${minor + 1}.0`
      case 'patch':
        return `${major}.${minor}.${patch + 1}`
      default:
        throw new Error(`Invalid release type: ${this.options.type}`)
    }
  }
  
  /**
   * 生成变更日志
   */
  private async generateChangelog(): Promise<void> {
    console.log('📋 Generating changelog...')
    
    try {
      // 获取上次发布以来的提交
      const lastTag = execSync('git describe --tags --abbrev=0', { encoding: 'utf-8' }).trim()
      const commits = execSync(`git log ${lastTag}..HEAD --oneline`, { encoding: 'utf-8' })
      
      const newVersion = this.calculateNewVersion()
      const date = new Date().toISOString().split('T')[0]
      
      const changelogEntry = `
## [${newVersion}] - ${date}

### Changes
${commits.split('\n').filter(line => line.trim()).map(line => `- ${line}`).join('\n')}
`
      
      if (!this.options.dryRun) {
        let existingChangelog: string
        try {
          existingChangelog = readFileSync('CHANGELOG.md', 'utf-8')
        } catch {
          existingChangelog = '# Changelog\n\n'
        }
        const newChangelog = `# Changelog\n${changelogEntry}\n${existingChangelog.replace('# Changelog\n', '')}`
        writeFileSync('CHANGELOG.md', newChangelog)
      }
      
      console.log('✅ Changelog updated')
    } catch (error) {
      console.warn('⚠️  Could not generate changelog:', error)
    }
  }
  
  /**
   * 创建Git标签
   */
  private async createGitTag(): Promise<void> {
    console.log('🏷️  Creating Git tag...')
    
    const newVersion = this.calculateNewVersion()
    const tagName = `v${newVersion}`
    
    if (!this.options.dryRun) {
      try {
        execSync(`git add .`)
        execSync(`git commit -m "chore: release ${newVersion}"`)
        execSync(`git tag -a ${tagName} -m "Release ${newVersion}"`)
        console.log(`✅ Created tag: ${tagName}`)
      } catch (error) {
        throw new Error(`Failed to create Git tag: ${error}`)
      }
    } else {
      console.log(`Would create tag: ${tagName}`)
    }
  }
  
  /**
   * 发布到npm
   */
  private async publishToNpm(): Promise<void> {
    console.log('📦 Publishing to npm...')
    
    for (const pkg of this.packages) {
      if (pkg.name.startsWith('@dl-engine/')) {
        const publishCommand = `npm publish ${pkg.path} --tag ${this.options.tag} --access public`
        
        if (!this.options.dryRun) {
          try {
            execSync(publishCommand, { stdio: 'inherit' })
            console.log(`✅ Published: ${pkg.name}`)
          } catch (error) {
            throw new Error(`Failed to publish ${pkg.name}: ${error}`)
          }
        } else {
          console.log(`Would publish: ${pkg.name}`)
        }
      }
    }
  }
  
  /**
   * 推送到Git
   */
  private async pushToGit(): Promise<void> {
    console.log('⬆️  Pushing to Git...')
    
    if (!this.options.dryRun) {
      try {
        execSync('git push origin main --tags')
        console.log('✅ Pushed to Git')
      } catch (error) {
        throw new Error(`Failed to push to Git: ${error}`)
      }
    } else {
      console.log('Would push to Git')
    }
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  const args = process.argv.slice(2)
  
  const options: Partial<ReleaseOptions> = {
    type: (args.find(arg => ['patch', 'minor', 'major'].includes(arg)) as any) || 'patch',
    dryRun: args.includes('--dry-run'),
    skipTests: args.includes('--skip-tests'),
    skipBuild: args.includes('--skip-build'),
    tag: args.find(arg => arg.startsWith('--tag='))?.split('=')[1] || 'latest'
  }
  
  const releaseManager = new ReleaseManager(options)
  await releaseManager.release()
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Release failed:', error)
    process.exit(1)
  })
}

export { ReleaseManager, main as runRelease }
