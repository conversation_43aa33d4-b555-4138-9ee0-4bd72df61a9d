/**
 * DL-Engine 资产管理器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { AssetManager } from '../AssetManager'
import { AssetType, AssetStatus, AssetPriority } from '../AssetTypes'

// Mock fetch
global.fetch = vi.fn()

describe('AssetManager', () => {
  let assetManager: AssetManager

  beforeEach(() => {
    assetManager = new AssetManager({
      maxConcurrentLoads: 2,
      cache: {
        maxSize: 10 * 1024 * 1024, // 10MB
        strategy: 'lru' as any,
        defaultTTL: 60000,
        enablePersistence: false
      }
    })
  })

  afterEach(() => {
    assetManager.dispose()
    vi.clearAllMocks()
  })

  describe('基本功能', () => {
    it('应该能够创建实例', () => {
      expect(assetManager).toBeDefined()
      expect(assetManager).toBeInstanceOf(AssetManager)
    })

    it('应该支持单例模式', () => {
      const instance1 = AssetManager.getInstance()
      const instance2 = AssetManager.getInstance()
      expect(instance1).toBe(instance2)
    })
  })

  describe('资产加载', () => {
    it('应该能够加载纹理资产', async () => {
      // Mock fetch response
      const mockBlob = new Blob(['fake image data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers({ 'content-length': '1024' })
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      // Mock Image constructor
      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') {
            setTimeout(() => callback(), 0)
          }
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      const asset = await assetManager.load(
        'test-texture',
        '/test/texture.jpg',
        AssetType.TEXTURE
      )

      expect(asset).toBeDefined()
      expect(asset.id).toBe('test-texture')
      expect(asset.type).toBe(AssetType.TEXTURE)
      expect(asset.status).toBe(AssetStatus.LOADED)
    })

    it('应该能够处理加载错误', async () => {
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'))

      await expect(
        assetManager.load('error-asset', '/error.jpg', AssetType.TEXTURE)
      ).rejects.toThrow()
    })

    it('应该支持加载选项', async () => {
      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 512,
        height: 512
      }
      
      global.Image = vi.fn(() => mockImage) as any

      const onProgress = vi.fn()
      const onComplete = vi.fn()

      const asset = await assetManager.load(
        'test-with-options',
        '/test.jpg',
        AssetType.TEXTURE,
        {
          priority: AssetPriority.HIGH,
          onProgress,
          onComplete,
          cache: true
        }
      )

      expect(asset).toBeDefined()
      expect(onComplete).toHaveBeenCalledWith(asset)
    })
  })

  describe('批量加载', () => {
    it('应该能够批量加载多个资产', async () => {
      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      const assets = await assetManager.loadBatch([
        {
          id: 'texture1',
          url: '/texture1.jpg',
          type: AssetType.TEXTURE
        },
        {
          id: 'texture2',
          url: '/texture2.jpg',
          type: AssetType.TEXTURE
        }
      ])

      expect(assets).toHaveLength(2)
      expect(assets[0].id).toBe('texture1')
      expect(assets[1].id).toBe('texture2')
    })
  })

  describe('缓存管理', () => {
    it('应该能够缓存已加载的资产', async () => {
      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      // 第一次加载
      const asset1 = await assetManager.load(
        'cached-texture',
        '/cached.jpg',
        AssetType.TEXTURE
      )

      // 第二次加载应该从缓存获取
      const asset2 = await assetManager.load(
        'cached-texture',
        '/cached.jpg',
        AssetType.TEXTURE
      )

      expect(asset1).toBe(asset2)
      expect(fetch).toHaveBeenCalledTimes(1) // 只调用一次fetch
    })

    it('应该能够检查资产是否存在', async () => {
      expect(assetManager.has('non-existent')).toBe(false)

      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      await assetManager.load('existing', '/existing.jpg', AssetType.TEXTURE)
      expect(assetManager.has('existing')).toBe(true)
    })

    it('应该能够卸载资产', async () => {
      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      await assetManager.load('to-unload', '/unload.jpg', AssetType.TEXTURE)
      expect(assetManager.has('to-unload')).toBe(true)

      const unloaded = assetManager.unload('to-unload')
      expect(unloaded).toBe(true)
    })
  })

  describe('查询功能', () => {
    it('应该能够查询资产', async () => {
      const mockBlob = new Blob(['fake data'])
      const mockResponse = {
        ok: true,
        blob: () => Promise.resolve(mockBlob),
        headers: new Headers()
      }
      
      vi.mocked(fetch).mockResolvedValue(mockResponse as any)

      const mockImage = {
        addEventListener: vi.fn((event, callback) => {
          if (event === 'load') setTimeout(() => callback(), 0)
        }),
        removeEventListener: vi.fn(),
        width: 256,
        height: 256
      }
      
      global.Image = vi.fn(() => mockImage) as any

      // 加载一些资产
      await assetManager.load('query1', '/query1.jpg', AssetType.TEXTURE)
      await assetManager.load('query2', '/query2.jpg', AssetType.TEXTURE)

      // 查询纹理资产
      const textures = assetManager.query({
        types: [AssetType.TEXTURE],
        statuses: [AssetStatus.LOADED]
      })

      expect(textures.length).toBeGreaterThanOrEqual(2)
      expect(textures.every(asset => asset.type === AssetType.TEXTURE)).toBe(true)
    })
  })

  describe('统计信息', () => {
    it('应该能够获取统计信息', () => {
      const stats = assetManager.getStats()
      expect(stats).toBeDefined()
      expect(typeof stats.totalAssets).toBe('number')
      expect(typeof stats.totalSize).toBe('number')
      expect(typeof stats.cacheSize).toBe('number')
    })

    it('应该能够获取缓存统计信息', () => {
      const cacheStats = assetManager.getCacheStats()
      expect(cacheStats).toBeDefined()
      expect(typeof cacheStats.size).toBe('number')
      expect(typeof cacheStats.currentSize).toBe('number')
      expect(typeof cacheStats.maxSize).toBe('number')
    })
  })

  describe('清理功能', () => {
    it('应该能够清理过期资产', () => {
      const cleaned = assetManager.cleanup()
      expect(typeof cleaned).toBe('number')
    })

    it('应该能够清空所有资产', () => {
      assetManager.clear()
      const stats = assetManager.getStats()
      expect(stats.totalAssets).toBe(0)
    })
  })
})
