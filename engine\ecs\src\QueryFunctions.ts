/**
 * DL-Engine 查询系统
 * 实体查询和过滤功能
 */

// React类型定义（简化版）
type FC<P = {}> = (props: P) => any
import { useHookstate, State } from '@dl-engine/engine-state'
import { Entity } from './Entity'
import { ComponentType, hasComponent, hasComponents } from './ComponentFunctions'

/**
 * 查询条件类型
 */
export type QueryCondition<T = any> = ComponentType<T> | NotCondition<T> | WithCondition<T>

/**
 * 非条件（排除组件）
 */
export interface NotCondition<T = any> {
  _type: 'not'
  component: ComponentType<T>
}

/**
 * 包含条件（必须有组件）
 */
export interface WithCondition<T = any> {
  _type: 'with'
  component: ComponentType<T>
}

/**
 * 查询接口
 */
export interface Query {
  /** 查询ID */
  id: string
  
  /** 查询条件 */
  conditions: QueryCondition[]
  
  /** 匹配的实体列表 */
  entities: Entity[]
  
  /** 是否启用 */
  enabled: boolean
  
  /** 查询结果是否已缓存 */
  cached: boolean
  
  /** 上次更新时间 */
  lastUpdate: number
  
  /** 执行查询 */
  execute(): Entity[]
  
  /** 检查实体是否匹配 */
  matches(entity: Entity): boolean
  
  /** 添加实体 */
  addEntity(entity: Entity): void
  
  /** 移除实体 */
  removeEntity(entity: Entity): void
  
  /** 清空查询结果 */
  clear(): void
}

/**
 * 查询实现
 */
class QueryImpl implements Query {
  id: string
  conditions: QueryCondition[]
  entities: Entity[] = []
  enabled: boolean = true
  cached: boolean = false
  lastUpdate: number = 0

  constructor(id: string, conditions: QueryCondition[]) {
    this.id = id
    this.conditions = conditions
  }

  execute(): Entity[] {
    if (this.cached && this.lastUpdate > 0) {
      return this.entities
    }

    this.entities = []
    
    // 这里需要遍历所有实体，实际实现中应该从Engine获取
    // 暂时返回空数组，等待完整的实体管理系统
    
    this.cached = true
    this.lastUpdate = Date.now()
    
    return this.entities
  }

  matches(entity: Entity): boolean {
    for (const condition of this.conditions) {
      if (isNotCondition(condition)) {
        if (hasComponent(entity, condition.component)) {
          return false
        }
      } else if (isWithCondition(condition)) {
        if (!hasComponent(entity, condition.component)) {
          return false
        }
      } else {
        // 普通组件条件
        if (!hasComponent(entity, condition)) {
          return false
        }
      }
    }
    return true
  }

  addEntity(entity: Entity): void {
    if (!this.entities.includes(entity)) {
      this.entities.push(entity)
      this.lastUpdate = Date.now()
    }
  }

  removeEntity(entity: Entity): void {
    const index = this.entities.indexOf(entity)
    if (index !== -1) {
      this.entities.splice(index, 1)
      this.lastUpdate = Date.now()
    }
  }

  clear(): void {
    this.entities = []
    this.cached = false
    this.lastUpdate = Date.now()
  }
}

/**
 * 全局查询注册表
 */
export const queries: Query[] = []
const queryMap = new Map<string, Query>()

/**
 * 定义查询
 * @param conditions 查询条件
 * @returns 查询对象
 */
export function defineQuery(...conditions: QueryCondition[]): Query {
  const id = generateQueryId(conditions)
  
  // 检查是否已存在相同查询
  const existingQuery = queryMap.get(id)
  if (existingQuery) {
    return existingQuery
  }
  
  const query = new QueryImpl(id, conditions)
  queries.push(query)
  queryMap.set(id, query)
  
  return query
}

/**
 * 移除查询
 * @param query 查询对象
 */
export function removeQuery(query: Query): void {
  const index = queries.indexOf(query)
  if (index !== -1) {
    queries.splice(index, 1)
    queryMap.delete(query.id)
  }
}

/**
 * 获取查询
 * @param id 查询ID
 * @returns 查询对象或undefined
 */
export function getQuery(id: string): Query | undefined {
  return queryMap.get(id)
}

/**
 * 获取所有查询
 * @returns 查询数组
 */
export function getAllQueries(): Query[] {
  return [...queries]
}

/**
 * 创建Not条件
 * @param component 组件类型
 * @returns Not条件
 */
export function Not<T>(component: ComponentType<T>): NotCondition<T> {
  return {
    _type: 'not',
    component
  }
}

/**
 * 创建With条件
 * @param component 组件类型
 * @returns With条件
 */
export function With<T>(component: ComponentType<T>): WithCondition<T> {
  return {
    _type: 'with',
    component
  }
}

/**
 * 检查是否为Not条件
 */
function isNotCondition(condition: QueryCondition): condition is NotCondition {
  return typeof condition === 'object' && '_type' in condition && condition._type === 'not'
}

/**
 * 检查是否为With条件
 */
function isWithCondition(condition: QueryCondition): condition is WithCondition {
  return typeof condition === 'object' && '_type' in condition && condition._type === 'with'
}

/**
 * 生成查询ID
 */
function generateQueryId(conditions: QueryCondition[]): string {
  const parts: string[] = []
  
  for (const condition of conditions) {
    if (isNotCondition(condition)) {
      parts.push(`!${condition.component.name}`)
    } else if (isWithCondition(condition)) {
      parts.push(`+${condition.component.name}`)
    } else {
      parts.push(condition.name)
    }
  }
  
  return parts.sort().join('&')
}

/**
 * React Hook: 使用查询
 * @param query 查询对象
 * @returns 查询结果状态
 */
export function useQuery(query: Query): State<Entity[]> {
  const state = useHookstate(() => query.execute())
  
  // 这里应该监听实体变化并更新查询结果
  // 暂时返回静态状态
  
  return state
}

/**
 * React组件: 查询Reactor
 * 用于响应式查询处理
 */
export interface QueryReactorProps {
  query: Query
  onEntityAdded?: (entity: Entity) => void
  onEntityRemoved?: (entity: Entity) => void
  children?: FC<{ entities: Entity[] }>
}

export const QueryReactor: FC<QueryReactorProps> = ({
  query,
  onEntityAdded,
  onEntityRemoved,
  children
}: QueryReactorProps) => {
  const entities = useQuery(query)
  
  // 监听实体变化
  // 这里需要实现实体变化监听逻辑
  
  if (children) {
    return children({ entities: Array.from(entities.value) })
  }
  
  return null
}

/**
 * 查询工具函数
 */
export const QueryUtils = {
  /**
   * 创建简单查询（只包含组件）
   */
  createSimpleQuery: (...components: ComponentType[]): Query => {
    return defineQuery(...components)
  },
  
  /**
   * 创建排除查询（排除某些组件）
   */
  createExcludeQuery: (include: ComponentType[], exclude: ComponentType[]): Query => {
    const conditions: QueryCondition[] = [
      ...include,
      ...exclude.map(comp => Not(comp))
    ]
    return defineQuery(...conditions)
  },
  
  /**
   * 合并查询结果
   */
  mergeQueries: (...queries: Query[]): Entity[] => {
    const entitySet = new Set<Entity>()
    
    for (const query of queries) {
      for (const entity of query.execute()) {
        entitySet.add(entity)
      }
    }
    
    return Array.from(entitySet)
  },
  
  /**
   * 查询交集
   */
  intersectQueries: (...queries: Query[]): Entity[] => {
    if (queries.length === 0) return []
    if (queries.length === 1) return queries[0].execute()
    
    const firstQuery = queries[0].execute()
    const otherQueries = queries.slice(1)
    
    return firstQuery.filter(entity => 
      otherQueries.every(query => query.execute().includes(entity))
    )
  },
  
  /**
   * 获取查询统计信息
   */
  getQueryStats: () => {
    const totalQueries = queries.length
    const enabledQueries = queries.filter(q => q.enabled).length
    const cachedQueries = queries.filter(q => q.cached).length
    const totalEntities = queries.reduce((sum, q) => sum + q.entities.length, 0)
    
    return {
      totalQueries,
      enabledQueries,
      cachedQueries,
      totalEntities,
      averageEntitiesPerQuery: totalQueries > 0 ? totalEntities / totalQueries : 0
    }
  },
  
  /**
   * 清理所有查询缓存
   */
  clearAllCaches: () => {
    for (const query of queries) {
      query.cached = false
    }
  },
  
  /**
   * 刷新所有查询
   */
  refreshAllQueries: () => {
    for (const query of queries) {
      query.execute()
    }
  }
}
