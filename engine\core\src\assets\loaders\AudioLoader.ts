/**
 * DL-Engine 音频加载器
 * 加载各种格式的音频资产
 */

import { AssetLoader } from '../AssetLoader'
import { 
  Asset, 
  AssetType, 
  AssetLoadOptions, 
  AudioAsset 
} from '../AssetTypes'

/**
 * 音频加载选项
 */
export interface AudioLoadOptions extends AssetLoadOptions {
  /** 是否解码为AudioBuffer */
  decode?: boolean
  
  /** 音频上下文 */
  audioContext?: AudioContext
  
  /** 是否启用流式播放 */
  streaming?: boolean
  
  /** 预加载策略 */
  preload?: 'none' | 'metadata' | 'auto'
  
  /** 音量 */
  volume?: number
  
  /** 是否循环 */
  loop?: boolean
  
  /** 是否自动播放 */
  autoplay?: boolean
}

/**
 * 音频格式支持检测
 */
export class AudioFormatDetector {
  private static supportCache = new Map<string, boolean>()

  /**
   * 检查音频格式支持
   */
  static isSupported(mimeType: string): boolean {
    if (this.supportCache.has(mimeType)) {
      return this.supportCache.get(mimeType)!
    }

    const audio = document.createElement('audio')
    const canPlay = audio.canPlayType(mimeType)
    const supported = canPlay === 'probably' || canPlay === 'maybe'
    
    this.supportCache.set(mimeType, supported)
    return supported
  }

  /**
   * 获取支持的格式列表
   */
  static getSupportedFormats(): string[] {
    const formats = [
      'audio/mpeg',     // MP3
      'audio/wav',      // WAV
      'audio/ogg',      // OGG
      'audio/mp4',      // MP4/AAC
      'audio/webm',     // WebM
      'audio/flac'      // FLAC
    ]

    return formats.filter(format => this.isSupported(format))
  }

  /**
   * 根据URL获取最佳格式
   */
  static getBestFormat(urls: string[]): string | null {
    const supportedFormats = this.getSupportedFormats()
    
    for (const url of urls) {
      const extension = url.split('.').pop()?.toLowerCase()
      const mimeType = this.getMimeTypeFromExtension(extension || '')
      
      if (supportedFormats.includes(mimeType)) {
        return url
      }
    }

    return null
  }

  /**
   * 从扩展名获取MIME类型
   */
  private static getMimeTypeFromExtension(extension: string): string {
    const mimeTypes: Record<string, string> = {
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',
      'mp4': 'audio/mp4',
      'm4a': 'audio/mp4',
      'webm': 'audio/webm',
      'flac': 'audio/flac'
    }

    return mimeTypes[extension] || 'audio/mpeg'
  }
}

/**
 * 音频加载器
 */
export class AudioLoader extends AssetLoader<AudioAsset> {
  private audioContext?: AudioContext

  constructor(audioContext?: AudioContext) {
    super()
    this.supportedTypes = [AssetType.AUDIO]
    this.audioContext = audioContext
  }

  /**
   * 设置音频上下文
   */
  setAudioContext(audioContext: AudioContext): void {
    this.audioContext = audioContext
  }

  /**
   * 执行音频加载
   */
  protected async performLoad(
    asset: Asset, 
    options: AudioLoadOptions = {}
  ): Promise<AudioAsset> {
    const progressUpdater = this.createProgressUpdater(asset.id, options)
    
    try {
      let audioData: AudioBuffer
      let metadata: AudioAsset['metadata']

      if (options.decode !== false && (this.audioContext || options.audioContext)) {
        // 解码为AudioBuffer
        const result = await this.loadAndDecodeAudio(asset.url, options, progressUpdater)
        audioData = result.buffer
        metadata = result.metadata
      } else {
        // 加载为HTML Audio元素
        const result = await this.loadHTMLAudio(asset.url, options, progressUpdater)
        audioData = result.buffer
        metadata = result.metadata
      }

      // 计算音频大小
      const size = this.calculateAudioSize(audioData)

      // 创建音频资产
      const audioAsset: AudioAsset = {
        ...asset,
        type: AssetType.AUDIO,
        data: audioData,
        size,
        metadata
      }

      return audioAsset

    } catch (error) {
      throw new Error(`音频加载失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * 加载并解码音频
   */
  private async loadAndDecodeAudio(
    url: string,
    options: AudioLoadOptions,
    onProgress: (loaded: number, total: number) => void
  ): Promise<{ buffer: AudioBuffer; metadata: AudioAsset['metadata'] }> {
    const audioContext = options.audioContext || this.audioContext
    
    if (!audioContext) {
      throw new Error('需要AudioContext来解码音频')
    }

    // 获取音频数据
    const response = await this.fetchWithProgress(url, options, onProgress)
    const arrayBuffer = await response.arrayBuffer()

    // 解码音频
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

    const metadata: AudioAsset['metadata'] = {
      duration: audioBuffer.duration,
      sampleRate: audioBuffer.sampleRate,
      numberOfChannels: audioBuffer.numberOfChannels,
      bitRate: this.estimateBitRate(arrayBuffer.byteLength, audioBuffer.duration)
    }

    return { buffer: audioBuffer, metadata }
  }

  /**
   * 加载HTML音频元素
   */
  private async loadHTMLAudio(
    url: string,
    options: AudioLoadOptions,
    onProgress: (loaded: number, total: number) => void
  ): Promise<{ buffer: AudioBuffer; metadata: AudioAsset['metadata'] }> {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      
      // 设置音频属性
      if (options.preload) {
        audio.preload = options.preload
      }
      if (options.volume !== undefined) {
        audio.volume = options.volume
      }
      if (options.loop !== undefined) {
        audio.loop = options.loop
      }
      if (options.autoplay !== undefined) {
        audio.autoplay = options.autoplay
      }

      // 进度监听
      audio.addEventListener('progress', () => {
        if (audio.buffered.length > 0) {
          const loaded = audio.buffered.end(audio.buffered.length - 1)
          const total = audio.duration || 0
          if (total > 0) {
            onProgress(loaded, total)
          }
        }
      })

      // 元数据加载完成
      audio.addEventListener('loadedmetadata', () => {
        const metadata: AudioAsset['metadata'] = {
          duration: audio.duration,
          sampleRate: 44100, // 默认值，HTML Audio无法获取确切值
          numberOfChannels: 2, // 默认值
          bitRate: this.estimateBitRateFromAudio(audio)
        }

        // 创建虚拟AudioBuffer（用于兼容）
        const buffer = this.createVirtualAudioBuffer(audio, metadata)
        
        resolve({ buffer, metadata })
      })

      // 错误处理
      audio.addEventListener('error', (event) => {
        reject(new Error(`音频加载失败: ${audio.error?.message || 'Unknown error'}`))
      })

      // 开始加载
      audio.src = url
      audio.load()
    })
  }

  /**
   * 创建虚拟AudioBuffer
   */
  private createVirtualAudioBuffer(
    audio: HTMLAudioElement,
    metadata: AudioAsset['metadata']
  ): AudioBuffer {
    // 注意：这是一个虚拟的AudioBuffer，实际音频数据在HTMLAudioElement中
    // 在实际使用时需要特殊处理
    const buffer = {
      duration: metadata.duration,
      sampleRate: metadata.sampleRate,
      numberOfChannels: metadata.numberOfChannels,
      length: Math.floor(metadata.duration * metadata.sampleRate),
      getChannelData: (channel: number) => {
        // 返回空的Float32Array，实际播放时使用HTMLAudioElement
        return new Float32Array(Math.floor(metadata.duration * metadata.sampleRate))
      },
      copyFromChannel: () => {},
      copyToChannel: () => {},
      // 添加自定义属性来存储HTMLAudioElement
      _htmlAudio: audio
    } as any

    return buffer
  }

  /**
   * 估算比特率
   */
  private estimateBitRate(fileSize: number, duration: number): number {
    if (duration <= 0) return 0
    return Math.round((fileSize * 8) / duration / 1000) // kbps
  }

  /**
   * 从HTML Audio估算比特率
   */
  private estimateBitRateFromAudio(audio: HTMLAudioElement): number {
    // HTML Audio无法直接获取比特率，返回估算值
    return 128 // 默认128kbps
  }

  /**
   * 计算音频大小
   */
  private calculateAudioSize(audioBuffer: AudioBuffer): number {
    if (!audioBuffer) return 0
    
    // AudioBuffer的大小 = 采样数 * 通道数 * 每个样本的字节数(4字节float32)
    return audioBuffer.length * audioBuffer.numberOfChannels * 4
  }

  /**
   * 创建音频分析器
   */
  createAnalyser(
    audioBuffer: AudioBuffer,
    audioContext: AudioContext
  ): AnalyserNode {
    const analyser = audioContext.createAnalyser()
    analyser.fftSize = 2048
    analyser.smoothingTimeConstant = 0.8
    
    return analyser
  }

  /**
   * 创建音频源节点
   */
  createSourceNode(
    audioBuffer: AudioBuffer,
    audioContext: AudioContext
  ): AudioBufferSourceNode {
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    
    return source
  }

  /**
   * 播放音频
   */
  async playAudio(
    audioAsset: AudioAsset,
    audioContext: AudioContext,
    options: {
      volume?: number
      loop?: boolean
      startTime?: number
      duration?: number
    } = {}
  ): Promise<AudioBufferSourceNode> {
    const source = this.createSourceNode(audioAsset.data, audioContext)
    
    // 创建增益节点控制音量
    const gainNode = audioContext.createGain()
    gainNode.gain.value = options.volume || 1.0
    
    // 连接节点
    source.connect(gainNode)
    gainNode.connect(audioContext.destination)
    
    // 设置循环
    if (options.loop) {
      source.loop = true
    }
    
    // 开始播放
    const startTime = options.startTime || 0
    const duration = options.duration
    
    if (duration) {
      source.start(audioContext.currentTime, startTime, duration)
    } else {
      source.start(audioContext.currentTime, startTime)
    }
    
    return source
  }

  /**
   * 停止音频
   */
  stopAudio(source: AudioBufferSourceNode): void {
    try {
      source.stop()
    } catch (error) {
      // 忽略已经停止的音频源错误
    }
  }

  /**
   * 获取音频频谱数据
   */
  getFrequencyData(analyser: AnalyserNode): Uint8Array {
    const dataArray = new Uint8Array(analyser.frequencyBinCount)
    analyser.getByteFrequencyData(dataArray)
    return dataArray
  }

  /**
   * 获取音频波形数据
   */
  getWaveformData(analyser: AnalyserNode): Uint8Array {
    const dataArray = new Uint8Array(analyser.frequencyBinCount)
    analyser.getByteTimeDomainData(dataArray)
    return dataArray
  }
}
