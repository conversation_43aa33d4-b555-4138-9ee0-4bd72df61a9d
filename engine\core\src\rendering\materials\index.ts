/**
 * DL-Engine 材质系统
 * 提供各种预定义材质和材质工具
 */

import * as THREE from 'three'

/**
 * 材质类型枚举
 */
export enum MaterialType {
  BASIC = 'basic',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  TOON = 'toon',
  MATCAP = 'matcap',
  SHADER = 'shader'
}

/**
 * 材质配置接口
 */
export interface MaterialConfig {
  type: MaterialType
  color?: number | string
  opacity?: number
  transparent?: boolean
  wireframe?: boolean
  side?: THREE.Side
  [key: string]: any
}

/**
 * 材质工厂
 */
export class MaterialFactory {
  private static materialCache = new Map<string, THREE.Material>()

  /**
   * 创建材质
   */
  static create(config: MaterialConfig): THREE.Material {
    const cacheKey = JSON.stringify(config)
    
    if (this.materialCache.has(cacheKey)) {
      return this.materialCache.get(cacheKey)!.clone()
    }

    let material: THREE.Material

    switch (config.type) {
      case MaterialType.BASIC:
        material = new THREE.MeshBasicMaterial(config)
        break
      case MaterialType.LAMBERT:
        material = new THREE.MeshLambertMaterial(config)
        break
      case MaterialType.PHONG:
        material = new THREE.MeshPhongMaterial(config)
        break
      case MaterialType.STANDARD:
        material = new THREE.MeshStandardMaterial(config)
        break
      case MaterialType.PHYSICAL:
        material = new THREE.MeshPhysicalMaterial(config)
        break
      case MaterialType.TOON:
        material = new THREE.MeshToonMaterial(config)
        break
      case MaterialType.MATCAP:
        material = new THREE.MeshMatcapMaterial(config)
        break
      default:
        material = new THREE.MeshStandardMaterial(config)
    }

    this.materialCache.set(cacheKey, material)
    return material.clone()
  }

  /**
   * 清理材质缓存
   */
  static clearCache(): void {
    this.materialCache.forEach(material => material.dispose())
    this.materialCache.clear()
  }
}

/**
 * 预定义材质
 */
export const PredefinedMaterials = {
  /**
   * 基础材质
   */
  basic: {
    red: () => MaterialFactory.create({ type: MaterialType.BASIC, color: 0xff0000 }),
    green: () => MaterialFactory.create({ type: MaterialType.BASIC, color: 0x00ff00 }),
    blue: () => MaterialFactory.create({ type: MaterialType.BASIC, color: 0x0000ff }),
    white: () => MaterialFactory.create({ type: MaterialType.BASIC, color: 0xffffff }),
    black: () => MaterialFactory.create({ type: MaterialType.BASIC, color: 0x000000 }),
    wireframe: () => MaterialFactory.create({ type: MaterialType.BASIC, wireframe: true })
  },

  /**
   * 标准材质
   */
  standard: {
    metal: () => MaterialFactory.create({
      type: MaterialType.STANDARD,
      color: 0x888888,
      metalness: 1.0,
      roughness: 0.1
    }),
    plastic: () => MaterialFactory.create({
      type: MaterialType.STANDARD,
      color: 0xffffff,
      metalness: 0.0,
      roughness: 0.5
    }),
    glass: () => MaterialFactory.create({
      type: MaterialType.PHYSICAL,
      color: 0xffffff,
      metalness: 0.0,
      roughness: 0.0,
      transmission: 1.0,
      transparent: true
    }),
    rubber: () => MaterialFactory.create({
      type: MaterialType.STANDARD,
      color: 0x333333,
      metalness: 0.0,
      roughness: 0.9
    })
  },

  /**
   * 教育用材质
   */
  educational: {
    highlight: () => MaterialFactory.create({
      type: MaterialType.BASIC,
      color: 0xffff00,
      transparent: true,
      opacity: 0.7
    }),
    correct: () => MaterialFactory.create({
      type: MaterialType.BASIC,
      color: 0x00ff00,
      transparent: true,
      opacity: 0.8
    }),
    incorrect: () => MaterialFactory.create({
      type: MaterialType.BASIC,
      color: 0xff0000,
      transparent: true,
      opacity: 0.8
    }),
    interactive: () => MaterialFactory.create({
      type: MaterialType.STANDARD,
      color: 0x4488ff,
      metalness: 0.2,
      roughness: 0.3,
      emissive: 0x001122
    })
  }
}

/**
 * 材质工具函数
 */
export const MaterialUtils = {
  /**
   * 设置材质透明度
   */
  setOpacity(material: THREE.Material, opacity: number): void {
    material.opacity = opacity
    material.transparent = opacity < 1.0
    material.needsUpdate = true
  },

  /**
   * 设置材质颜色
   */
  setColor(material: THREE.Material, color: number | string): void {
    if ('color' in material) {
      (material as any).color.set(color)
      material.needsUpdate = true
    }
  },

  /**
   * 设置材质发光
   */
  setEmissive(material: THREE.Material, color: number | string, intensity: number = 1): void {
    if ('emissive' in material) {
      (material as any).emissive.set(color)
      if ('emissiveIntensity' in material) {
        (material as any).emissiveIntensity = intensity
      }
      material.needsUpdate = true
    }
  },

  /**
   * 克隆材质
   */
  clone(material: THREE.Material): THREE.Material {
    return material.clone()
  },

  /**
   * 释放材质资源
   */
  dispose(material: THREE.Material | THREE.Material[]): void {
    if (Array.isArray(material)) {
      material.forEach(mat => mat.dispose())
    } else {
      material.dispose()
    }
  },

  /**
   * 创建渐变材质
   */
  createGradientMaterial(color1: number, color2: number, direction: 'horizontal' | 'vertical' = 'vertical'): THREE.ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform vec3 color1;
      uniform vec3 color2;
      varying vec2 vUv;
      void main() {
        float mixValue = ${direction === 'vertical' ? 'vUv.y' : 'vUv.x'};
        gl_FragColor = vec4(mix(color1, color2, mixValue), 1.0);
      }
    `

    return new THREE.ShaderMaterial({
      uniforms: {
        color1: { value: new THREE.Color(color1) },
        color2: { value: new THREE.Color(color2) }
      },
      vertexShader,
      fragmentShader
    })
  },

  /**
   * 创建动画材质
   */
  createAnimatedMaterial(baseColor: number, speed: number = 1.0): THREE.ShaderMaterial {
    const vertexShader = `
      varying vec2 vUv;
      void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    `

    const fragmentShader = `
      uniform vec3 baseColor;
      uniform float time;
      uniform float speed;
      varying vec2 vUv;
      void main() {
        float wave = sin(vUv.x * 10.0 + time * speed) * 0.5 + 0.5;
        vec3 color = baseColor * (0.5 + wave * 0.5);
        gl_FragColor = vec4(color, 1.0);
      }
    `

    const material = new THREE.ShaderMaterial({
      uniforms: {
        baseColor: { value: new THREE.Color(baseColor) },
        time: { value: 0 },
        speed: { value: speed }
      },
      vertexShader,
      fragmentShader
    })

    // 添加动画更新函数
    ;(material as any).update = (deltaTime: number) => {
      material.uniforms.time.value += deltaTime
    }

    return material
  }
}

/**
 * 材质管理器
 */
export class MaterialManager {
  private materials = new Map<string, THREE.Material>()

  /**
   * 注册材质
   */
  register(name: string, material: THREE.Material): void {
    this.materials.set(name, material)
  }

  /**
   * 获取材质
   */
  get(name: string): THREE.Material | undefined {
    const material = this.materials.get(name)
    return material ? material.clone() : undefined
  }

  /**
   * 移除材质
   */
  remove(name: string): boolean {
    const material = this.materials.get(name)
    if (material) {
      material.dispose()
      this.materials.delete(name)
      return true
    }
    return false
  }

  /**
   * 清理所有材质
   */
  clear(): void {
    this.materials.forEach(material => material.dispose())
    this.materials.clear()
  }

  /**
   * 获取所有材质名称
   */
  getNames(): string[] {
    return Array.from(this.materials.keys())
  }
}

/**
 * 全局材质管理器实例
 */
export const globalMaterialManager = new MaterialManager()
