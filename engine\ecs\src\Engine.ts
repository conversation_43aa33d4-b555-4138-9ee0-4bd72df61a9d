/**
 * DL-Engine 引擎核心
 * 基于bitECS和Hyperflux的引擎实例管理
 */

import * as bitECS from 'bitecs'
import { getAllEntities } from 'bitecs'

import {
  getState
} from '@dl-engine/engine-state'

import { $RemovedComponent, removeEntity } from './ComponentFunctions'
import { ECSState } from './ECSState'
import { EngineState } from './EngineState'
import { Entity } from './Entity'
import { queries, removeQuery } from './QueryFunctions'
import { SystemState } from './SystemState'

/**
 * DL-Engine 引擎类
 * 管理ECS世界和系统状态
 */
export class Engine {
  /** 引擎单例实例 */
  static instance: Engine = null!

  /** ECS世界存储 */
  store: any = null!

  /** 引擎是否已初始化 */
  isInitialized = false

  /** 引擎版本 */
  static readonly VERSION = '1.0.0'

  /** 引擎名称 */
  static readonly NAME = 'Digital Learning Engine'

  constructor() {
    if (Engine.instance) {
      throw new Error('Engine instance already exists. Use Engine.instance instead.')
    }
  }

  /**
   * 获取引擎实例
   */
  static getInstance(): Engine {
    if (!Engine.instance) {
      throw new Error('Engine not initialized. Call createEngine() first.')
    }
    return Engine.instance
  }

  /**
   * 检查引擎是否已初始化
   */
  static isInitialized(): boolean {
    return Engine.instance?.isInitialized ?? false
  }
}

// 全局引擎引用
;(globalThis as any).Engine = Engine

/**
 * 创建DL-Engine引擎实例
 * @param hyperstore 可选的HyperStore实例
 */
export function createEngine(): Engine {
  if (Engine.instance) {
    throw new Error('Engine already exists. Call destroyEngine() first.')
  }

  // 创建引擎实例
  Engine.instance = new Engine()

  // 创建ECS世界
  Engine.instance.store = bitECS.createWorld()

  // 创建未定义实体（实体ID为0）
  const UndefinedEntity = bitECS.addEntity(Engine.instance.store)

  // 标记为已初始化
  Engine.instance.isInitialized = true

  console.log(`${Engine.NAME} v${Engine.VERSION} initialized successfully`)

  return Engine.instance
}

/**
 * 销毁DL-Engine引擎实例
 * 清理所有资源和状态
 */
export function destroyEngine(): void {
  if (!Engine.instance) {
    console.warn('Engine not initialized, nothing to destroy')
    return
  }

  try {
    // 清理定时器
    const timer = getState(ECSState).timer
    if (timer) {
      timer.clear()
    }

    // 移除所有实体
    try {
      const entities = getAllEntities(Engine.instance.store) as Entity[]
      for (const entity of entities) {
        removeEntity(entity)
      }
    } catch (error) {
      console.warn('Error removing entities during engine destruction:', error)
      // 某些错误是由于组件onRemove中的副作用引起的
      // 我们需要将这些逻辑移到reactors中
    }

    // 清理已移除组件标记
    $RemovedComponent.exists.fill(0)

    // 移除所有查询
    for (const query of queries) {
      removeQuery(query)
    }

    // 销毁ECS世界
    bitECS.deleteWorld(Engine.instance.store)

    // 清理引用
    Engine.instance.store = null!
    Engine.instance.isInitialized = false
    Engine.instance = null!

    console.log(`${Engine.NAME} destroyed successfully`)
  } catch (error) {
    console.error('Error during engine destruction:', error)
    throw error
  }
}

/**
 * 重启引擎
 * 先销毁再重新创建
 */
export function restartEngine(): Engine {
  destroyEngine()
  return createEngine()
}

/**
 * 获取引擎信息
 */
export function getEngineInfo() {
  return {
    name: Engine.NAME,
    version: Engine.VERSION,
    isInitialized: Engine.isInitialized(),
    entityCount: Engine.instance?.store ? getAllEntities(Engine.instance.store).length : 0
  }
}
