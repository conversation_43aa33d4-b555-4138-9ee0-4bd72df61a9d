/**
 * DL-Engine 后处理系统
 * 提供各种后处理效果和管理功能
 */

import * as THREE from 'three'

/**
 * 后处理效果类型枚举
 */
export enum PostProcessingEffect {
  BLOOM = 'bloom',
  BLUR = 'blur',
  FXAA = 'fxaa',
  SSAO = 'ssao',
  DOF = 'dof',
  TONE_MAPPING = 'toneMapping',
  COLOR_CORRECTION = 'colorCorrection',
  VIGNETTE = 'vignette',
  FILM_GRAIN = 'filmGrain',
  CHROMATIC_ABERRATION = 'chromaticAberration'
}

/**
 * 后处理配置接口
 */
export interface PostProcessingConfig {
  enabled: boolean
  effects: {
    [key in PostProcessingEffect]?: {
      enabled: boolean
      [key: string]: any
    }
  }
}

/**
 * 后处理通道基类
 */
export abstract class PostProcessingPass {
  public enabled: boolean = true
  public needsSwap: boolean = true
  protected material: THREE.ShaderMaterial | null = null

  abstract render(
    renderer: THREE.WebGLRenderer,
    writeBuffer: THREE.WebGLRenderTarget,
    readBuffer: THREE.WebGLRenderTarget,
    deltaTime: number
  ): void

  dispose(): void {
    if (this.material) {
      this.material.dispose()
    }
  }
}

/**
 * 渲染通道
 */
export class RenderPass extends PostProcessingPass {
  public scene: THREE.Scene
  public camera: THREE.Camera
  public overrideMaterial: THREE.Material | null = null
  public clearColor: THREE.Color | null = null
  public clearAlpha: number = 0

  constructor(scene: THREE.Scene, camera: THREE.Camera) {
    super()
    this.scene = scene
    this.camera = camera
    this.needsSwap = false
  }

  render(
    renderer: THREE.WebGLRenderer,
    writeBuffer: THREE.WebGLRenderTarget,
    readBuffer: THREE.WebGLRenderTarget
  ): void {
    const oldAutoClear = renderer.autoClear
    renderer.autoClear = false

    let oldClearColor: THREE.Color | undefined
    let oldClearAlpha: number | undefined

    if (this.clearColor) {
      oldClearColor = renderer.getClearColor(new THREE.Color())
      oldClearAlpha = renderer.getClearAlpha()
      renderer.setClearColor(this.clearColor, this.clearAlpha)
    }

    if (this.overrideMaterial) {
      const oldOverrideMaterial = this.scene.overrideMaterial
      this.scene.overrideMaterial = this.overrideMaterial
      renderer.setRenderTarget(writeBuffer)
      renderer.clear()
      renderer.render(this.scene, this.camera)
      this.scene.overrideMaterial = oldOverrideMaterial
    } else {
      renderer.setRenderTarget(writeBuffer)
      renderer.clear()
      renderer.render(this.scene, this.camera)
    }

    if (this.clearColor) {
      renderer.setClearColor(oldClearColor!, oldClearAlpha!)
    }

    renderer.autoClear = oldAutoClear
  }
}

/**
 * 着色器通道
 */
export class ShaderPass extends PostProcessingPass {
  public uniforms: { [key: string]: THREE.IUniform }
  private quad: THREE.Mesh
  private camera: THREE.OrthographicCamera

  constructor(shader: { uniforms: any; vertexShader: string; fragmentShader: string }) {
    super()

    this.uniforms = THREE.UniformsUtils.clone(shader.uniforms)
    this.material = new THREE.ShaderMaterial({
      uniforms: this.uniforms,
      vertexShader: shader.vertexShader,
      fragmentShader: shader.fragmentShader
    })

    this.camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1)
    this.quad = new THREE.Mesh(new THREE.PlaneGeometry(2, 2), this.material)
    this.quad.frustumCulled = false
  }

  render(
    renderer: THREE.WebGLRenderer,
    writeBuffer: THREE.WebGLRenderTarget,
    readBuffer: THREE.WebGLRenderTarget
  ): void {
    if (this.uniforms.tDiffuse) {
      this.uniforms.tDiffuse.value = readBuffer.texture
    }

    renderer.setRenderTarget(writeBuffer)
    renderer.clear()
    renderer.render(this.quad, this.camera)
  }

  dispose(): void {
    super.dispose()
    this.quad.geometry.dispose()
  }
}

/**
 * 复制通道
 */
export class CopyPass extends ShaderPass {
  constructor() {
    super({
      uniforms: {
        tDiffuse: { value: null },
        opacity: { value: 1.0 }
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform sampler2D tDiffuse;
        uniform float opacity;
        varying vec2 vUv;
        void main() {
          vec4 texel = texture2D(tDiffuse, vUv);
          gl_FragColor = opacity * texel;
        }
      `
    })
  }
}

/**
 * 后处理组合器
 */
export class PostProcessingComposer {
  private renderer: THREE.WebGLRenderer
  private renderTarget1: THREE.WebGLRenderTarget
  private renderTarget2: THREE.WebGLRenderTarget
  private passes: PostProcessingPass[] = []
  private copyPass: CopyPass
  private writeBuffer: THREE.WebGLRenderTarget
  private readBuffer: THREE.WebGLRenderTarget

  constructor(renderer: THREE.WebGLRenderer, renderTarget?: THREE.WebGLRenderTarget) {
    this.renderer = renderer

    const size = renderer.getSize(new THREE.Vector2())
    const pixelRatio = renderer.getPixelRatio()

    this.renderTarget1 = renderTarget || new THREE.WebGLRenderTarget(
      size.width * pixelRatio,
      size.height * pixelRatio,
      {
        minFilter: THREE.LinearFilter,
        magFilter: THREE.LinearFilter,
        format: THREE.RGBAFormat,
        stencilBuffer: false
      }
    )

    this.renderTarget2 = this.renderTarget1.clone()
    this.writeBuffer = this.renderTarget1
    this.readBuffer = this.renderTarget2
    this.copyPass = new CopyPass()
  }

  /**
   * 添加通道
   */
  addPass(pass: PostProcessingPass): void {
    this.passes.push(pass)
  }

  /**
   * 插入通道
   */
  insertPass(pass: PostProcessingPass, index: number): void {
    this.passes.splice(index, 0, pass)
  }

  /**
   * 移除通道
   */
  removePass(pass: PostProcessingPass): void {
    const index = this.passes.indexOf(pass)
    if (index !== -1) {
      this.passes.splice(index, 1)
    }
  }

  /**
   * 渲染
   */
  render(deltaTime: number = 0): void {
    let currentRenderTarget: THREE.WebGLRenderTarget | null = null

    for (let i = 0; i < this.passes.length; i++) {
      const pass = this.passes[i]

      if (!pass.enabled) continue

      pass.render(this.renderer, this.writeBuffer, this.readBuffer, deltaTime)

      if (pass.needsSwap) {
        if (currentRenderTarget === null) {
          currentRenderTarget = this.writeBuffer
          this.writeBuffer = this.readBuffer
          this.readBuffer = currentRenderTarget
        } else {
          const temp = this.writeBuffer
          this.writeBuffer = this.readBuffer
          this.readBuffer = temp
        }
      }
    }

    // 最后一个通道渲染到屏幕
    const lastPass = this.passes[this.passes.length - 1]
    if (lastPass && lastPass.needsSwap) {
      this.copyPass.render(this.renderer, null as any, this.readBuffer)
    }
  }

  /**
   * 设置大小
   */
  setSize(width: number, height: number): void {
    this.renderTarget1.setSize(width, height)
    this.renderTarget2.setSize(width, height)

    this.passes.forEach(pass => {
      if ('setSize' in pass) {
        (pass as any).setSize(width, height)
      }
    })
  }

  /**
   * 重置
   */
  reset(renderTarget?: THREE.WebGLRenderTarget): void {
    if (renderTarget) {
      this.renderTarget1.dispose()
      this.renderTarget2.dispose()
      this.renderTarget1 = renderTarget
      this.renderTarget2 = renderTarget.clone()
    }

    this.writeBuffer = this.renderTarget1
    this.readBuffer = this.renderTarget2
  }

  /**
   * 释放资源
   */
  dispose(): void {
    this.renderTarget1.dispose()
    this.renderTarget2.dispose()
    this.copyPass.dispose()

    this.passes.forEach(pass => {
      pass.dispose()
    })
  }
}

/**
 * 后处理管理器
 */
export class PostProcessingManager {
  private composer: PostProcessingComposer | null = null
  private config: PostProcessingConfig
  private passes: Map<string, PostProcessingPass> = new Map()

  constructor() {
    this.config = {
      enabled: true,
      effects: {}
    }
  }

  /**
   * 初始化
   */
  initialize(renderer: THREE.WebGLRenderer): void {
    this.composer = new PostProcessingComposer(renderer)
  }

  /**
   * 添加渲染通道
   */
  addRenderPass(scene: THREE.Scene, camera: THREE.Camera): void {
    if (!this.composer) return

    const renderPass = new RenderPass(scene, camera)
    this.composer.addPass(renderPass)
    this.passes.set('render', renderPass)
  }

  /**
   * 添加效果
   */
  addEffect(name: string, pass: PostProcessingPass): void {
    if (!this.composer) return

    this.composer.addPass(pass)
    this.passes.set(name, pass)
  }

  /**
   * 移除效果
   */
  removeEffect(name: string): void {
    const pass = this.passes.get(name)
    if (pass && this.composer) {
      this.composer.removePass(pass)
      this.passes.delete(name)
    }
  }

  /**
   * 启用/禁用效果
   */
  setEffectEnabled(name: string, enabled: boolean): void {
    const pass = this.passes.get(name)
    if (pass) {
      pass.enabled = enabled
    }
  }

  /**
   * 渲染
   */
  render(deltaTime: number = 0): void {
    if (this.composer && this.config.enabled) {
      this.composer.render(deltaTime)
    }
  }

  /**
   * 设置大小
   */
  setSize(width: number, height: number): void {
    if (this.composer) {
      this.composer.setSize(width, height)
    }
  }

  /**
   * 配置
   */
  configure(config: Partial<PostProcessingConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * 获取配置
   */
  getConfig(): PostProcessingConfig {
    return { ...this.config }
  }

  /**
   * 释放资源
   */
  dispose(): void {
    if (this.composer) {
      this.composer.dispose()
    }
    this.passes.clear()
  }
}

/**
 * 全局后处理管理器实例
 */
export const globalPostProcessingManager = new PostProcessingManager()
