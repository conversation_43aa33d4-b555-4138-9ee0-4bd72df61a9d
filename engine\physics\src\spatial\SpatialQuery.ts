/**
 * DL-Engine 空间查询系统
 * 高效的空间查询和邻近检测
 * 支持射线投射、范围查询、最近邻搜索等功能
 */

import {
  Ray as RapierRay,
  RayColliderToi,
  RayColliderIntersection,
  Vector3 as RapierVector3,
  Collider,
  QueryFilterFlags,
  Ball,
  Rotation as RapierRotation
} from '@dimforge/rapier3d'

import { Entity } from '@dl-engine/engine-ecs'
import { PhysicsVector3, PhysicsQuaternion, SpatialQueryResult } from '../types/PhysicsTypes'
import { DLPhysicsWorld } from '../PhysicsWorld'

/**
 * 空间查询选项
 */
export interface SpatialQueryOptions {
  /** 最大距离 */
  maxDistance?: number
  
  /** 最大结果数量 */
  maxResults?: number
  
  /** 是否包含静态对象 */
  includeStatic?: boolean
  
  /** 是否包含动态对象 */
  includeDynamic?: boolean
  
  /** 是否包含运动学对象 */
  includeKinematic?: boolean
  
  /** 排除的实体列表 */
  excludeEntities?: Entity[]
  
  /** 碰撞组过滤 */
  collisionGroups?: number
  
  /** 自定义过滤函数 */
  filter?: (entity: Entity) => boolean
}

/**
 * 边界框
 */
export interface BoundingBox {
  min: PhysicsVector3
  max: PhysicsVector3
}

/**
 * 球形边界
 */
export interface BoundingSphere {
  center: PhysicsVector3
  radius: number
}

/**
 * 空间查询接口
 */
export interface ISpatialQuery {
  /**
   * 范围查询 - 查找指定范围内的所有对象
   */
  queryRange(bounds: BoundingBox, options?: SpatialQueryOptions): SpatialQueryResult[]
  
  /**
   * 球形查询 - 查找指定球形范围内的所有对象
   */
  querySphere(sphere: BoundingSphere, options?: SpatialQueryOptions): SpatialQueryResult[]
  
  /**
   * 最近邻查询 - 查找距离指定点最近的对象
   */
  queryNearest(point: PhysicsVector3, options?: SpatialQueryOptions): SpatialQueryResult | null
  
  /**
   * K最近邻查询 - 查找距离指定点最近的K个对象
   */
  queryKNearest(point: PhysicsVector3, k: number, options?: SpatialQueryOptions): SpatialQueryResult[]
  
  /**
   * 射线查询 - 查找与射线相交的对象
   */
  queryRay(origin: PhysicsVector3, direction: PhysicsVector3, options?: SpatialQueryOptions): SpatialQueryResult[]
}

/**
 * 空间查询实现
 */
export class SpatialQuery implements ISpatialQuery {
  private entities: Map<Entity, SpatialQueryResult> = new Map()
  
  /**
   * 添加实体到空间索引
   */
  addEntity(entity: Entity, position: PhysicsVector3, bounds?: BoundingBox): void {
    const result: SpatialQueryResult = {
      entity,
      collider: null as any, // 需要从物理世界获取
      distance: 0,
      position
    }
    
    this.entities.set(entity, result)
  }
  
  /**
   * 从空间索引移除实体
   */
  removeEntity(entity: Entity): void {
    this.entities.delete(entity)
  }
  
  /**
   * 更新实体位置
   */
  updateEntity(entity: Entity, position: PhysicsVector3, bounds?: BoundingBox): void {
    const result = this.entities.get(entity)
    if (result) {
      result.position = position
    }
  }
  
  /**
   * 范围查询
   */
  queryRange(bounds: BoundingBox, options: SpatialQueryOptions = {}): SpatialQueryResult[] {
    const results: SpatialQueryResult[] = []
    const maxResults = options.maxResults || Number.MAX_SAFE_INTEGER
    
    for (const [entity, result] of this.entities) {
      if (results.length >= maxResults) break
      
      // 检查是否在边界框内
      if (this.isPointInBounds(result.position, bounds)) {
        // 应用过滤器
        if (this.passesFilter(entity, options)) {
          results.push({ ...result })
        }
      }
    }
    
    return results
  }
  
  /**
   * 球形查询
   */
  querySphere(sphere: BoundingSphere, options: SpatialQueryOptions = {}): SpatialQueryResult[] {
    const results: SpatialQueryResult[] = []
    const maxResults = options.maxResults || Number.MAX_SAFE_INTEGER
    const radiusSquared = sphere.radius * sphere.radius
    
    for (const [entity, result] of this.entities) {
      if (results.length >= maxResults) break
      
      // 计算距离的平方
      const distanceSquared = this.distanceSquared(result.position, sphere.center)
      
      // 检查是否在球形范围内
      if (distanceSquared <= radiusSquared) {
        // 应用过滤器
        if (this.passesFilter(entity, options)) {
          const resultCopy = { ...result }
          resultCopy.distance = Math.sqrt(distanceSquared)
          results.push(resultCopy)
        }
      }
    }
    
    // 按距离排序
    results.sort((a, b) => a.distance - b.distance)
    
    return results
  }
  
  /**
   * 最近邻查询
   */
  queryNearest(point: PhysicsVector3, options: SpatialQueryOptions = {}): SpatialQueryResult | null {
    let nearest: SpatialQueryResult | null = null
    let minDistance = options.maxDistance || Number.MAX_VALUE
    
    for (const [entity, result] of this.entities) {
      const distance = this.distance(result.position, point)
      
      if (distance < minDistance && this.passesFilter(entity, options)) {
        minDistance = distance
        nearest = { ...result, distance }
      }
    }
    
    return nearest
  }
  
  /**
   * K最近邻查询
   */
  queryKNearest(point: PhysicsVector3, k: number, options: SpatialQueryOptions = {}): SpatialQueryResult[] {
    const candidates: Array<SpatialQueryResult & { distance: number }> = []
    
    // 收集所有候选对象
    for (const [entity, result] of this.entities) {
      if (this.passesFilter(entity, options)) {
        const distance = this.distance(result.position, point)
        
        if (!options.maxDistance || distance <= options.maxDistance) {
          candidates.push({ ...result, distance })
        }
      }
    }
    
    // 按距离排序并取前K个
    candidates.sort((a, b) => a.distance - b.distance)
    return candidates.slice(0, k)
  }
  
  /**
   * 射线查询
   */
  queryRay(
    origin: PhysicsVector3, 
    direction: PhysicsVector3, 
    options: SpatialQueryOptions = {}
  ): SpatialQueryResult[] {
    const results: SpatialQueryResult[] = []
    const maxDistance = options.maxDistance || 1000
    const maxResults = options.maxResults || Number.MAX_SAFE_INTEGER
    
    // 归一化方向向量
    const dirLength = Math.sqrt(
      direction.x * direction.x + 
      direction.y * direction.y + 
      direction.z * direction.z
    )
    
    if (dirLength === 0) return results
    
    const normalizedDir = {
      x: direction.x / dirLength,
      y: direction.y / dirLength,
      z: direction.z / dirLength
    }
    
    for (const [entity, result] of this.entities) {
      if (results.length >= maxResults) break
      
      // 简化的点到射线距离计算
      const toPoint = {
        x: result.position.x - origin.x,
        y: result.position.y - origin.y,
        z: result.position.z - origin.z
      }
      
      // 投影到射线上的距离
      const projectionLength = 
        toPoint.x * normalizedDir.x + 
        toPoint.y * normalizedDir.y + 
        toPoint.z * normalizedDir.z
      
      if (projectionLength >= 0 && projectionLength <= maxDistance) {
        // 计算垂直距离
        const projectionPoint = {
          x: origin.x + normalizedDir.x * projectionLength,
          y: origin.y + normalizedDir.y * projectionLength,
          z: origin.z + normalizedDir.z * projectionLength
        }
        
        const perpDistance = this.distance(result.position, projectionPoint)
        
        // 简单的阈值检查（实际应该基于对象的边界）
        if (perpDistance < 1.0 && this.passesFilter(entity, options)) {
          results.push({ ...result, distance: projectionLength })
        }
      }
    }
    
    // 按距离排序
    results.sort((a, b) => a.distance - b.distance)
    
    return results
  }
  
  /**
   * 检查点是否在边界框内
   */
  private isPointInBounds(point: PhysicsVector3, bounds: BoundingBox): boolean {
    return (
      point.x >= bounds.min.x && point.x <= bounds.max.x &&
      point.y >= bounds.min.y && point.y <= bounds.max.y &&
      point.z >= bounds.min.z && point.z <= bounds.max.z
    )
  }
  
  /**
   * 计算两点间距离
   */
  private distance(a: PhysicsVector3, b: PhysicsVector3): number {
    return Math.sqrt(this.distanceSquared(a, b))
  }
  
  /**
   * 计算两点间距离的平方
   */
  private distanceSquared(a: PhysicsVector3, b: PhysicsVector3): number {
    const dx = a.x - b.x
    const dy = a.y - b.y
    const dz = a.z - b.z
    return dx * dx + dy * dy + dz * dz
  }
  
  /**
   * 检查实体是否通过过滤器
   */
  private passesFilter(entity: Entity, options: SpatialQueryOptions): boolean {
    // 检查排除列表
    if (options.excludeEntities?.includes(entity)) {
      return false
    }
    
    // 应用自定义过滤器
    if (options.filter && !options.filter(entity)) {
      return false
    }
    
    // 其他过滤条件可以在这里添加
    
    return true
  }
  
  /**
   * 获取所有实体数量
   */
  getEntityCount(): number {
    return this.entities.size
  }
  
  /**
   * 清空所有实体
   */
  clear(): void {
    this.entities.clear()
  }
}

/**
 * 全局空间查询实例
 */
export const spatialQuery = new SpatialQuery()

/**
 * 便捷的范围查询函数
 */
export function queryRange(
  min: PhysicsVector3,
  max: PhysicsVector3,
  options?: SpatialQueryOptions
): SpatialQueryResult[] {
  return spatialQuery.queryRange({ min, max }, options)
}

/**
 * 便捷的球形查询函数
 */
export function querySphere(
  center: PhysicsVector3,
  radius: number,
  options?: SpatialQueryOptions
): SpatialQueryResult[] {
  return spatialQuery.querySphere({ center, radius }, options)
}

/**
 * 便捷的最近邻查询函数
 */
export function queryNearest(
  point: PhysicsVector3,
  options?: SpatialQueryOptions
): SpatialQueryResult | null {
  return spatialQuery.queryNearest(point, options)
}

/**
 * 射线投射结果
 */
export interface RaycastHit {
  /** 碰撞的实体 */
  entity: Entity

  /** 碰撞的碰撞体 */
  collider: Collider

  /** 碰撞点（世界坐标） */
  point: PhysicsVector3

  /** 碰撞点法向量 */
  normal: PhysicsVector3

  /** 射线起点到碰撞点的距离 */
  distance: number

  /** 射线参数 t (0-1) */
  toi: number

  /** 碰撞面的特征ID */
  featureId: number
}

/**
 * 射线投射配置
 */
export interface RaycastConfig {
  /** 最大距离 */
  maxDistance: number

  /** 是否包含传感器 */
  includeSensors: boolean

  /** 查询过滤器 */
  filterFlags: QueryFilterFlags

  /** 排除的实体列表 */
  excludeEntities: Entity[]

  /** 碰撞组过滤 */
  collisionGroups?: number

  /** 是否只查询第一个碰撞 */
  firstHitOnly: boolean
}

/**
 * 高级空间查询管理器
 */
export class AdvancedSpatialQuery {
  private static instance: AdvancedSpatialQuery | null = null

  /**
   * 获取单例实例
   */
  static getInstance(): AdvancedSpatialQuery {
    if (!AdvancedSpatialQuery.instance) {
      AdvancedSpatialQuery.instance = new AdvancedSpatialQuery()
    }
    return AdvancedSpatialQuery.instance
  }

  /**
   * 射线投射查询
   */
  raycast(
    world: DLPhysicsWorld,
    origin: PhysicsVector3,
    direction: PhysicsVector3,
    config: Partial<RaycastConfig> = {}
  ): RaycastHit[] {
    const defaultConfig: RaycastConfig = {
      maxDistance: 1000,
      includeSensors: false,
      filterFlags: QueryFilterFlags.EXCLUDE_SENSORS,
      excludeEntities: [],
      firstHitOnly: false
    }

    const finalConfig = { ...defaultConfig, ...config }

    // 创建Rapier射线
    const rapierOrigin = new RapierVector3(origin.x, origin.y, origin.z)
    const rapierDirection = new RapierVector3(direction.x, direction.y, direction.z)
    const ray = new RapierRay(rapierOrigin, rapierDirection)

    const hits: RaycastHit[] = []

    if (finalConfig.firstHitOnly) {
      // 查询第一个碰撞
      const hit = world.castRay(ray, finalConfig.maxDistance, true, finalConfig.filterFlags)
      if (hit) {
        const raycastHit = this.convertRaycastHit(world, hit, ray)
        if (raycastHit && !finalConfig.excludeEntities.includes(raycastHit.entity)) {
          hits.push(raycastHit)
        }
      }
    } else {
      // 查询所有碰撞
      world.intersectionsWithRay(ray, finalConfig.maxDistance, true, (hit) => {
        const raycastHit = this.convertRaycastHit(world, hit, ray)
        if (raycastHit && !finalConfig.excludeEntities.includes(raycastHit.entity)) {
          hits.push(raycastHit)
        }
        return true // 继续查询
      }, finalConfig.filterFlags)
    }

    // 按距离排序
    hits.sort((a, b) => a.distance - b.distance)

    return hits
  }

  /**
   * 球形重叠查询
   */
  overlapSphere(
    world: DLPhysicsWorld,
    center: PhysicsVector3,
    radius: number,
    excludeEntities: Entity[] = []
  ): Array<{ entity: Entity; collider: Collider }> {
    const results: Array<{ entity: Entity; collider: Collider }> = []
    const rapierCenter = new RapierVector3(center.x, center.y, center.z)
    const ballShape = new Ball(radius)
    const rotation = { w: 1, x: 0, y: 0, z: 0 }

    world.intersectionsWithShape(rapierCenter, rotation, ballShape, (collider: Collider) => {
      const entity = this.getEntityFromCollider(world, collider)
      if (entity && !excludeEntities.includes(entity)) {
        results.push({ entity, collider })
      }
      return true // 继续查询
    })

    return results
  }

  /**
   * 转换Rapier射线碰撞结果
   */
  private convertRaycastHit(
    world: DLPhysicsWorld,
    hit: RayColliderToi | RayColliderIntersection,
    ray: RapierRay
  ): RaycastHit | null {
    const entity = this.getEntityFromCollider(world, hit.collider)
    if (!entity) return null

    const point = ray.pointAt(hit.toi)

    return {
      entity,
      collider: hit.collider,
      point: { x: point.x, y: point.y, z: point.z },
      normal: 'normal' in hit ?
        { x: hit.normal.x, y: hit.normal.y, z: hit.normal.z } :
        { x: 0, y: 1, z: 0 }, // 默认法向量
      distance: hit.toi,
      toi: hit.toi,
      featureId: 'featureId' in hit ? (hit.featureId ?? 0) : 0
    }
  }

  /**
   * 从碰撞体获取实体
   */
  private getEntityFromCollider(world: DLPhysicsWorld, collider: Collider): Entity | null {
    // 遍历世界中的碰撞体映射
    for (const [entity, worldCollider] of world.colliders) {
      if (worldCollider === collider) {
        return entity
      }
    }
    return null
  }
}

/**
 * 全局高级空间查询实例
 */
export const advancedSpatialQuery = AdvancedSpatialQuery.getInstance()

/**
 * 便捷的射线投射函数
 */
export function raycast(
  world: DLPhysicsWorld,
  origin: PhysicsVector3,
  direction: PhysicsVector3,
  maxDistance: number = 1000,
  firstHitOnly: boolean = true
): RaycastHit[] {
  return advancedSpatialQuery.raycast(world, origin, direction, {
    maxDistance,
    firstHitOnly
  })
}
