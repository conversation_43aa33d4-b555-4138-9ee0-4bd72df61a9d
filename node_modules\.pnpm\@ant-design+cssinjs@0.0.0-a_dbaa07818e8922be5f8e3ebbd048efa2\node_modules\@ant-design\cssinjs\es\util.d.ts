export declare function flattenToken(token: any): string;
/**
 * Convert derivative token to key string
 */
export declare function token2key(token: any, slat: string): string;
export declare function warning(message: string, path?: string): void;
export declare const styleValidate: (key: string, value: string | number | boolean | null | undefined, info?: {
    path?: string;
    hashId?: string;
}) => void;
export declare function supportLayer(): boolean;
