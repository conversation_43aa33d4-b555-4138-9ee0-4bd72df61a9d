/**
 * DL-Engine 教育系统管理器
 * 统一管理和协调所有教育组件，提供完整的教育功能
 */

import { Engine } from '../Engine'
import { Entity } from '../Entity'
import { defineSystem, SystemUUID } from '../SystemFunctions'
import { LearningProgressComponent } from './LearningProgressComponent'
import { KnowledgePointComponent, KnowledgePointUtils } from './KnowledgePointComponent'
import { LearningAnalyticsComponent, LearningAnalyticsUtils } from './LearningAnalyticsComponent'
import { LearningPathComponent, LearningPathUtils } from './LearningPathComponent'
import { CollaborativeLearningComponent, CollaborativeLearningUtils } from './CollaborativeLearningComponent'
import { AdaptiveLearningComponent, AdaptiveLearningUtils } from './AdaptiveLearningComponent'
import { VirtualLabComponent, VirtualLabUtils } from './VirtualLabComponent'
import { GamificationComponent, GamificationUtils } from './GamificationComponent'
import { LearningResourceComponent, LearningResourceUtils } from './LearningResourceComponent'

/**
 * 教育系统配置
 */
export interface EducationSystemConfig {
  /** 是否启用学习分析 */
  enableAnalytics: boolean
  
  /** 是否启用自适应学习 */
  enableAdaptiveLearning: boolean
  
  /** 是否启用游戏化 */
  enableGamification: boolean
  
  /** 是否启用协作学习 */
  enableCollaboration: boolean
  
  /** 是否启用虚拟实验室 */
  enableVirtualLab: boolean
  
  /** 分析更新频率（分钟） */
  analyticsUpdateFrequency: number
  
  /** 自适应检查频率（分钟） */
  adaptiveCheckFrequency: number
  
  /** 游戏化更新频率（分钟） */
  gamificationUpdateFrequency: number
}

/**
 * 学习会话数据
 */
export interface LearningSession {
  /** 会话ID */
  sessionId: string
  
  /** 学习者ID */
  learnerId: string
  
  /** 课程ID */
  courseId: string
  
  /** 会话开始时间 */
  startTime: Date
  
  /** 会话结束时间 */
  endTime?: Date
  
  /** 当前知识点 */
  currentKnowledgePoint?: string
  
  /** 学习活动记录 */
  activities: Array<{
    timestamp: Date
    type: string
    data: any
  }>
  
  /** 会话统计 */
  statistics: {
    totalTime: number
    knowledgePointsVisited: number
    tasksCompleted: number
    averageScore: number
    engagementLevel: number
  }
}

/**
 * 教育系统管理器
 */
export class EducationSystemManager {
  private static instance: EducationSystemManager
  private config: EducationSystemConfig
  private activeSessions: Map<string, LearningSession> = new Map()
  private lastAnalyticsUpdate: Date = new Date()
  private lastAdaptiveCheck: Date = new Date()
  private lastGamificationUpdate: Date = new Date()

  constructor(config: EducationSystemConfig) {
    this.config = config
  }

  static getInstance(config?: EducationSystemConfig): EducationSystemManager {
    if (!EducationSystemManager.instance) {
      EducationSystemManager.instance = new EducationSystemManager(
        config || {
          enableAnalytics: true,
          enableAdaptiveLearning: true,
          enableGamification: true,
          enableCollaboration: true,
          enableVirtualLab: true,
          analyticsUpdateFrequency: 5,
          adaptiveCheckFrequency: 10,
          gamificationUpdateFrequency: 1
        }
      )
    }
    return EducationSystemManager.instance
  }

  /**
   * 初始化教育系统
   */
  initialize(): void {
    console.log('🎓 Initializing Education System Manager')
    
    // 注册教育系统
    this.registerEducationSystem()
    
    console.log('✅ Education System Manager initialized successfully')
  }

  /**
   * 注册教育系统
   */
  private registerEducationSystem(): void {
    defineSystem({
      uuid: 'education-system' as SystemUUID,
      insert: { with: 'education-system' as SystemUUID },
      execute: () => {
        this.updateEducationSystems()
      }
    })
  }

  /**
   * 更新教育系统
   */
  private updateEducationSystems(): void {
    const now = new Date()
    
    // 更新学习分析
    if (this.config.enableAnalytics && 
        this.shouldUpdate(this.lastAnalyticsUpdate, this.config.analyticsUpdateFrequency)) {
      this.updateLearningAnalytics()
      this.lastAnalyticsUpdate = now
    }
    
    // 检查自适应学习
    if (this.config.enableAdaptiveLearning && 
        this.shouldUpdate(this.lastAdaptiveCheck, this.config.adaptiveCheckFrequency)) {
      this.checkAdaptiveLearning()
      this.lastAdaptiveCheck = now
    }
    
    // 更新游戏化系统
    if (this.config.enableGamification && 
        this.shouldUpdate(this.lastGamificationUpdate, this.config.gamificationUpdateFrequency)) {
      this.updateGamification()
      this.lastGamificationUpdate = now
    }
  }

  /**
   * 检查是否需要更新
   */
  private shouldUpdate(lastUpdate: Date, frequencyMinutes: number): boolean {
    const now = new Date()
    const timeDiff = (now.getTime() - lastUpdate.getTime()) / (1000 * 60)
    return timeDiff >= frequencyMinutes
  }

  /**
   * 开始学习会话
   */
  startLearningSession(learnerId: string, courseId: string): string {
    const sessionId = `session_${Date.now()}_${learnerId}`
    
    const session: LearningSession = {
      sessionId,
      learnerId,
      courseId,
      startTime: new Date(),
      activities: [],
      statistics: {
        totalTime: 0,
        knowledgePointsVisited: 0,
        tasksCompleted: 0,
        averageScore: 0,
        engagementLevel: 50
      }
    }
    
    this.activeSessions.set(sessionId, session)
    
    // 初始化学习者的教育组件
    this.initializeLearnerComponents(learnerId, courseId)
    
    console.log(`Learning session started: ${sessionId}`)
    return sessionId
  }

  /**
   * 结束学习会话
   */
  endLearningSession(sessionId: string): void {
    const session = this.activeSessions.get(sessionId)
    if (!session) return
    
    session.endTime = new Date()
    session.statistics.totalTime = (session.endTime.getTime() - session.startTime.getTime()) / (1000 * 60)
    
    // 保存会话数据到学习分析
    this.saveLearningSessionData(session)
    
    this.activeSessions.delete(sessionId)
    console.log(`Learning session ended: ${sessionId}`)
  }

  /**
   * 初始化学习者组件
   */
  private initializeLearnerComponents(learnerId: string, courseId: string): void {
    const learnerEntity = Engine.instance.store.getEntityByName(`learner_${learnerId}`) || 
                         Engine.instance.store.createEntity()
    
    // 添加学习进度组件
    if (!learnerEntity.hasComponent(LearningProgressComponent)) {
      learnerEntity.setComponent(LearningProgressComponent, {
        learnerId,
        courseId,
        overallProgress: 0,
        completedLessons: [],
        currentLesson: '',
        timeSpent: 0,
        lastAccessTime: new Date(),
        achievements: [],
        milestones: [],
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    // 添加学习分析组件
    if (this.config.enableAnalytics && !learnerEntity.hasComponent(LearningAnalyticsComponent)) {
      learnerEntity.setComponent(LearningAnalyticsComponent, {
        learnerId,
        courseId,
        analysisStartTime: new Date(),
        lastUpdateTime: new Date(),
        behaviors: [],
        patterns: [],
        recommendations: [],
        statistics: {
          totalStudyTime: 0,
          activeDays: 0,
          completedTasks: 0,
          averageScore: 0,
          learningFrequency: 0,
          averageSessionDuration: 0,
          knowledgeMasteryRate: 0,
          errorRate: 0,
          helpSeekingFrequency: 0,
          collaborationLevel: 0
        },
        predictions: [],
        learningStyleAnalysis: { visual: 25, auditory: 25, kinesthetic: 25, reading: 25 },
        cognitiveLoadAnalysis: { intrinsic: 0, extraneous: 0, germane: 0 },
        emotionalStateAnalysis: { engagement: 50, frustration: 0, confidence: 50, anxiety: 0 },
        socialNetworkAnalysis: { connections: [], centrality: 0, clustering: 0 }
      })
    }
    
    // 添加自适应学习组件
    if (this.config.enableAdaptiveLearning && !learnerEntity.hasComponent(AdaptiveLearningComponent)) {
      learnerEntity.setComponent(AdaptiveLearningComponent, {
        learnerId,
        courseId,
        learnerCharacteristics: {
          cognitiveAbility: { workingMemory: 50, processingSpeed: 50, attention: 50, reasoning: 50 },
          learningStyle: { visual: 25, auditory: 25, kinesthetic: 25, reading: 25 },
          motivation: { intrinsic: 50, extrinsic: 50, achievement: 50, curiosity: 50 },
          emotionalState: { engagement: 50, frustration: 0, confidence: 50, anxiety: 0 },
          metacognitive: { planning: 50, monitoring: 50, evaluation: 50, regulation: 50 }
        },
        currentDifficulty: 3,
        currentPace: 1.0,
        adaptationRules: [],
        adaptationHistory: [],
        predictionModels: [],
        personalizationParameters: {
          contentDifficultyMultiplier: 1.0,
          feedbackDelayMultiplier: 1.0,
          hintFrequencyMultiplier: 1.0,
          assessmentFrequencyMultiplier: 1.0,
          socialInteractionPreference: 0.5
        },
        adaptationConfig: {
          enabled: true,
          adaptationFrequency: 5,
          minAdaptationInterval: 2,
          maxAdaptationsPerSession: 3,
          learnerControlLevel: 0.3
        },
        performanceMetrics: {
          averageScore: 0,
          learningEfficiency: 0,
          retentionRate: 0,
          engagementLevel: 50,
          frustrationLevel: 0,
          timeOnTask: 0
        },
        lastAdaptationTime: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
    
    // 添加游戏化组件
    if (this.config.enableGamification && !learnerEntity.hasComponent(GamificationComponent)) {
      learnerEntity.setComponent(GamificationComponent, {
        learnerId,
        courseId,
        levelSystem: {
          currentLevel: 1,
          currentXP: 0,
          currentLevelXP: 0,
          nextLevelXP: 100,
          levelProgress: 0,
          levelTitle: '新手学习者',
          levelIcon: 'novice.png',
          levelPerks: []
        },
        achievements: [],
        quests: [],
        statistics: {
          totalPoints: 0,
          todayPoints: 0,
          weekPoints: 0,
          monthPoints: 0,
          streakDays: 0,
          maxStreakDays: 0,
          achievementsUnlocked: 0,
          questsCompleted: 0,
          totalStudyTime: 0,
          averageDailyTime: 0,
          efficiencyScore: 0
        },
        leaderboard: [],
        inventory: [],
        settings: {
          enableNotifications: true,
          enableSounds: true,
          enableAnimations: true,
          showProgress: true,
          shareAchievements: false,
          competitiveMode: true
        },
        dailyCheckin: {
          lastCheckinDate: undefined,
          consecutiveDays: 0,
          totalCheckins: 0,
          checkinRewards: []
        },
        activityHistory: [],
        createdAt: new Date(),
        updatedAt: new Date()
      })
    }
  }

  /**
   * 更新学习分析
   */
  private updateLearningAnalytics(): void {
    const analyticsEntities = Engine.instance.store.getEntitiesWith(LearningAnalyticsComponent)
    
    analyticsEntities.forEach((entity: Entity) => {
      const analytics = entity.getComponent(LearningAnalyticsComponent)
      if (analytics) {
        LearningAnalyticsUtils.updateStatistics(analytics)
        LearningAnalyticsUtils.analyzePatterns(analytics)
        LearningAnalyticsUtils.generateRecommendations(analytics)
      }
    })
  }

  /**
   * 检查自适应学习
   */
  private checkAdaptiveLearning(): void {
    const adaptiveEntities = Engine.instance.store.getEntitiesWith(AdaptiveLearningComponent)
    
    adaptiveEntities.forEach((entity: Entity) => {
      const adaptive = entity.getComponent(AdaptiveLearningComponent)
      if (adaptive && AdaptiveLearningUtils.checkAdaptationNeeded(adaptive)) {
        // 找到需要触发的规则
        const triggeredRules = adaptive.adaptationRules.filter((rule: any) =>
          rule.enabled && AdaptiveLearningUtils.evaluateRule(adaptive, rule)
        )

        // 应用适应策略
        triggeredRules.forEach((rule: any) => {
          AdaptiveLearningUtils.applyAdaptation(adaptive, rule)
        })
      }
    })
  }

  /**
   * 更新游戏化系统
   */
  private updateGamification(): void {
    const gamificationEntities = Engine.instance.store.getEntitiesWith(GamificationComponent)
    
    gamificationEntities.forEach((entity: Entity) => {
      const gamification = entity.getComponent(GamificationComponent)
      if (gamification) {
        // 检查成就解锁
        gamification.achievements.forEach((achievement: any) => {
          if (!achievement.unlocked) {
            const shouldUnlock = achievement.unlockConditions.every((condition: any) =>
              condition.current >= condition.target
            )

            if (shouldUnlock) {
              GamificationUtils.unlockAchievement(gamification, achievement.id)
            }
          }
        })

        // 检查任务完成
        gamification.quests.forEach((quest: any) => {
          if (quest.status === 'in_progress') {
            const allObjectivesCompleted = quest.objectives.every((obj: any) => obj.completed)
            if (allObjectivesCompleted) {
              GamificationUtils.completeQuest(gamification, quest.id)
            }
          }
        })
      }
    })
  }

  /**
   * 保存学习会话数据
   */
  private saveLearningSessionData(session: LearningSession): void {
    const learnerEntity = Engine.instance.store.getEntityByName(`learner_${session.learnerId}`)
    if (!learnerEntity) return
    
    // 更新学习分析数据
    const analytics = learnerEntity.getComponent(LearningAnalyticsComponent)
    if (analytics) {
      session.activities.forEach(activity => {
        LearningAnalyticsUtils.recordBehavior(analytics, {
          id: `behavior_${Date.now()}`,
          learnerId: session.learnerId,
          type: activity.type as any,
          timestamp: activity.timestamp,
          contentId: activity.data.contentId,
          duration: activity.data.duration || 0,
          result: activity.data.result,
          score: activity.data.score,
          context: {
            deviceType: 'web',
            browserType: 'chrome',
            screenResolution: '1920x1080'
          },
          metadata: activity.data
        })
      })
    }
    
    // 更新游戏化数据
    const gamification = learnerEntity.getComponent(GamificationComponent)
    if (gamification) {
      GamificationUtils.addExperience(gamification, Math.floor(session.statistics.totalTime), '学习会话完成')
    }
  }

  /**
   * 记录学习活动
   */
  recordLearningActivity(sessionId: string, type: string, data: any): void {
    const session = this.activeSessions.get(sessionId)
    if (!session) return
    
    session.activities.push({
      timestamp: new Date(),
      type,
      data
    })
    
    // 更新会话统计
    this.updateSessionStatistics(session, type, data)
  }

  /**
   * 更新会话统计
   */
  private updateSessionStatistics(session: LearningSession, type: string, data: any): void {
    switch (type) {
      case 'knowledge_point_visited':
        session.statistics.knowledgePointsVisited++
        break
      case 'task_completed':
        session.statistics.tasksCompleted++
        if (data.score) {
          const currentAvg = session.statistics.averageScore
          const count = session.statistics.tasksCompleted
          session.statistics.averageScore = (currentAvg * (count - 1) + data.score) / count
        }
        break
      case 'engagement_update':
        session.statistics.engagementLevel = data.level
        break
    }
  }

  /**
   * 获取学习者的综合报告
   */
  getLearnerReport(learnerId: string): any {
    const learnerEntity = Engine.instance.store.getEntityByName(`learner_${learnerId}`)
    if (!learnerEntity) return null
    
    const progress = learnerEntity.getComponent(LearningProgressComponent)
    const analytics = learnerEntity.getComponent(LearningAnalyticsComponent)
    const adaptive = learnerEntity.getComponent(AdaptiveLearningComponent)
    const gamification = learnerEntity.getComponent(GamificationComponent)
    
    return {
      learnerId,
      progress: progress ? {
        overallProgress: progress.overallProgress,
        completedLessons: progress.completedLessons.length,
        timeSpent: progress.timeSpent,
        achievements: progress.achievements.length
      } : null,
      analytics: analytics ? {
        totalStudyTime: analytics.statistics.totalStudyTime,
        averageScore: analytics.statistics.averageScore,
        learningFrequency: analytics.statistics.learningFrequency,
        recommendations: analytics.recommendations.length
      } : null,
      adaptive: adaptive ? {
        currentLevel: adaptive.currentDifficulty,
        engagementLevel: adaptive.performanceMetrics.engagementLevel,
        adaptationsApplied: adaptive.adaptationHistory.length
      } : null,
      gamification: gamification ? {
        level: gamification.levelSystem.currentLevel,
        totalPoints: gamification.statistics.totalPoints,
        achievementsUnlocked: gamification.statistics.achievementsUnlocked,
        questsCompleted: gamification.statistics.questsCompleted
      } : null
    }
  }
}

/**
 * 导出教育系统管理器实例
 */
export const educationSystemManager = EducationSystemManager.getInstance()
