# @ant-design/cssinjs

[![NPM version][npm-image]][npm-url]
[![npm download][download-image]][download-url]
[![dumi](https://img.shields.io/badge/docs%20by-dumi-blue?style=flat-square)](https://github.com/umijs/dumi)
[![build status][github-actions-image]][github-actions-url]
[![Codecov][codecov-image]][codecov-url]
[![Dependencies][david-image]][david-url]
[![DevDependencies][david-dev-image]][david-dev-url]
[![bundle size][bundlephobia-image]][bundlephobia-url]

[npm-image]: http://img.shields.io/npm/v/@ant-design/cssinjs.svg?style=flat-square
[npm-url]: http://npmjs.org/package/@ant-design/cssinjs
[github-actions-image]: https://github.com/ant-design/cssinjs/workflows/CI/badge.svg
[github-actions-url]: https://github.com/ant-design/cssinjs/actions
[codecov-image]: https://img.shields.io/codecov/c/github/ant-design/cssinjs/master.svg?style=flat-square
[codecov-url]: https://codecov.io/gh/ant-design/cssinjs/branch/master
[david-url]: https://david-dm.org/ant-design/cssinjs
[david-image]: https://david-dm.org/ant-design/cssinjs/status.svg?style=flat-square
[david-dev-url]: https://david-dm.org/ant-design/cssinjs?type=dev
[david-dev-image]: https://david-dm.org/ant-design/cssinjs/dev-status.svg?style=flat-square
[download-image]: https://img.shields.io/npm/dm/@ant-design/cssinjs.svg?style=flat-square
[download-url]: https://npmjs.org/package/@ant-design/cssinjs
[bundlephobia-url]: https://bundlephobia.com/result?p=@ant-design/cssinjs
[bundlephobia-image]: https://badgen.net/bundlephobia/minzip/@ant-design/cssinjs

Component level cssinjs solution used in [ant.design](https://ant.design).
It's a subset of [Emotion](https://emotion.sh/) with design token logic wrapper. Please feel free to use emotion directly if you want to find a web cssinjs solution. cssinjs related dep packages:

- stylis
- @emotion/hash
- @emotion/unitless

## Live Demo

https://cssinjs.vercel.app/

## Install

[![@ant-design/cssinjs](https://nodei.co/npm/@ant-design/cssinjs.png)](https://npmjs.org/package/@ant-design/cssinjs)

## Development

```
npm install
npm start
```

## License

@ant-design/cssinjs is released under the MIT license.
