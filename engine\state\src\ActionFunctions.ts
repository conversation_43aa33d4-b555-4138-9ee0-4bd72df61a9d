/**
 * DL-Engine 动作系统
 * 管理动作的分发、处理和同步
 */

import { v4 as uuidv4 } from 'uuid'
import { matches, Validator } from 'ts-matches'
import { 
  Action, 
  ActionOptions, 
  ResolvedActionType, 
  ActionReceptor,
  ActionQueueHandle,
  ActionQueueInstance,
  Topic,
  PeerID,
  UserID
} from './types'
import { getDLEngineStore } from './StoreFunctions'

/**
 * 动作匹配器
 */
export const matchesAction = <A extends Action>(action: A): Validator<unknown, A> => {
  // 创建一个简单的形状匹配器，避免ts-matches的类型问题
  const shapeValidator = (input: unknown): input is A => {
    if (typeof input !== 'object' || input === null) {
      return false
    }

    const inputObj = input as any

    // 检查type属性
    if (typeof action.type === 'string') {
      return inputObj.type === action.type
    } else if (Array.isArray(action.type)) {
      return Array.isArray(inputObj.type) &&
             action.type.every((t, i) => inputObj.type[i] === t)
    }

    return false
  }

  return shapeValidator as unknown as Validator<unknown, A>
}

/**
 * 创建动作
 * @param action 动作对象
 * @param options 动作选项
 * @returns 解析后的动作
 */
export function createAction<A extends Action>(
  action: A,
  options: ActionOptions = {}
): ResolvedActionType<A> {
  const store = getDLEngineStore()
  
  const resolvedAction: ResolvedActionType<A> = {
    ...action,
    $uuid: options.$uuid || uuidv4(),
    $time: options.$time || store.getDispatchTime(),
    $topic: options.$topic || store.defaultTopic,
    $from: options.$from || store.peerID,
    $to: options.$to || store.peerID, // 提供默认值避免undefined
    $cache: options.$cache || false,
    $delay: options.$delay || 0,
    $stack: options.$stack || []
  }
  
  return resolvedAction
}

/**
 * 分发动作
 * @param action 动作对象
 * @param options 动作选项
 */
export function dispatchAction<A extends Action>(
  action: A,
  options: ActionOptions = {}
): void {
  const store = getDLEngineStore()
  const resolvedAction = createAction(action, options)
  
  // 检查是否已知的UUID（防止重复处理）
  if (store.actions.knownUUIDs.has(resolvedAction.$uuid)) {
    return
  }
  
  // 标记为已知
  store.actions.knownUUIDs.add(resolvedAction.$uuid)
  
  // 处理延迟
  if (resolvedAction.$delay > 0) {
    setTimeout(() => {
      processAction(resolvedAction)
    }, resolvedAction.$delay)
  } else {
    processAction(resolvedAction)
  }
}

/**
 * 处理动作
 * @param action 解析后的动作
 */
function processAction<A extends Action>(action: ResolvedActionType<A>): void {
  const store = getDLEngineStore()
  
  // 添加到传入队列
  store.actions.incoming.push(action)
  
  // 添加到历史
  store.actions.history.push(action)
  
  // 如果需要缓存
  if (action.$cache) {
    store.actions.cached.push(action)
  }
  
  // 处理转发
  if (store.forwardingTopics.has(action.$topic)) {
    forwardAction(action)
  }
  
  // 触发动作处理器
  processActionReceptors(action)
}

/**
 * 转发动作到其他对等节点
 * @param action 动作
 */
function forwardAction<A extends Action>(action: ResolvedActionType<A>): void {
  const store = getDLEngineStore()
  const topic = action.$topic
  
  const outgoing = store.actions.outgoing as any
  if (!outgoing[topic]) {
    outgoing[topic] = {
      queue: [],
      history: [],
      forwardedUUIDs: new Set()
    }
  }

  // 检查是否已转发
  if (outgoing[topic].forwardedUUIDs.has(action.$uuid)) {
    return
  }

  // 标记为已转发
  outgoing[topic].forwardedUUIDs.add(action.$uuid)

  // 添加到传出队列
  outgoing[topic].queue.push(action)
  outgoing[topic].history.push(action)
}

/**
 * 处理动作接收器
 * @param action 动作
 */
function processActionReceptors<A extends Action>(action: ResolvedActionType<A>): void {
  // 这里需要遍历所有状态定义的接收器
  // 由于循环依赖问题，这里提供基础实现
  console.log('Processing action:', action.type, action)
}

/**
 * 创建动作队列
 * @param autoClear 是否自动清理
 * @returns 动作队列句柄
 */
export function createActionQueue(autoClear: boolean = true): ActionQueueHandle {
  const store = getDLEngineStore()
  const handle = uuidv4() as ActionQueueHandle
  
  const queue: ResolvedActionType[] = []
  
  const queueInstance: ActionQueueInstance = {
    handle,
    queue: () => queue,
    autoClear
  }
  
  store.actions.queues.set(handle, queueInstance)
  
  return handle
}

/**
 * 获取动作队列
 * @param handle 队列句柄
 * @returns 动作数组
 */
export function getActionQueue(handle: ActionQueueHandle): ResolvedActionType[] {
  const store = getDLEngineStore()
  const queueInstance = store.actions.queues.get(handle)
  
  if (!queueInstance) {
    return []
  }
  
  const actions = queueInstance.queue()
  
  // 自动清理
  if (queueInstance.autoClear) {
    actions.length = 0
  }
  
  return [...actions]
}

/**
 * 清空动作队列
 * @param handle 队列句柄
 */
export function clearActionQueue(handle: ActionQueueHandle): void {
  const store = getDLEngineStore()
  const queueInstance = store.actions.queues.get(handle)
  
  if (queueInstance) {
    queueInstance.queue().length = 0
  }
}

/**
 * 销毁动作队列
 * @param handle 队列句柄
 */
export function destroyActionQueue(handle: ActionQueueHandle): void {
  const store = getDLEngineStore()
  store.actions.queues.delete(handle)
}

/**
 * 获取传入动作
 * @param clear 是否清空队列
 * @returns 动作数组
 */
export function getIncomingActions(clear: boolean = true): ResolvedActionType[] {
  const store = getDLEngineStore()
  const actions = [...store.actions.incoming]
  
  if (clear) {
    store.actions.incoming.length = 0
  }
  
  return actions
}

/**
 * 获取传出动作
 * @param topic 主题
 * @param clear 是否清空队列
 * @returns 动作数组
 */
export function getOutgoingActions(topic: Topic, clear: boolean = true): ResolvedActionType[] {
  const store = getDLEngineStore()
  const outgoing = store.actions.outgoing as any

  if (!outgoing[topic]) {
    return []
  }

  const actions = [...outgoing[topic].queue]

  if (clear) {
    outgoing[topic].queue.length = 0
  }

  return actions
}

/**
 * 获取缓存动作
 * @param filter 过滤函数
 * @returns 动作数组
 */
export function getCachedActions(
  filter?: (action: ResolvedActionType) => boolean
): ResolvedActionType[] {
  const store = getDLEngineStore()
  const actions = store.actions.cached
  
  return filter ? actions.filter(filter) : [...actions]
}

/**
 * 清除缓存动作
 * @param filter 过滤函数，如果提供则只清除匹配的动作
 */
export function clearCachedActions(
  filter?: (action: ResolvedActionType) => boolean
): void {
  const store = getDLEngineStore()
  
  if (filter) {
    store.actions.cached = store.actions.cached.filter(action => !filter(action))
  } else {
    store.actions.cached.length = 0
  }
}

/**
 * 添加转发主题
 * @param topic 主题
 */
export function addForwardingTopic(topic: Topic): void {
  const store = getDLEngineStore()
  store.forwardingTopics.add(topic)
}

/**
 * 移除转发主题
 * @param topic 主题
 */
export function removeForwardingTopic(topic: Topic): void {
  const store = getDLEngineStore()
  store.forwardingTopics.delete(topic)
}

/**
 * 检查是否为转发主题
 * @param topic 主题
 * @returns 是否为转发主题
 */
export function isForwardingTopic(topic: Topic): boolean {
  const store = getDLEngineStore()
  return store.forwardingTopics.has(topic)
}

/**
 * 动作统计信息
 */
export interface ActionStats {
  /** 总动作数 */
  totalActions: number
  
  /** 缓存动作数 */
  cachedActions: number
  
  /** 传入动作数 */
  incomingActions: number
  
  /** 历史动作数 */
  historyActions: number
  
  /** 已知UUID数 */
  knownUUIDs: number
  
  /** 传出主题数 */
  outgoingTopics: number
  
  /** 转发主题数 */
  forwardingTopics: number
  
  /** 动作队列数 */
  actionQueues: number
}

/**
 * 获取动作统计信息
 * @returns 动作统计信息
 */
export function getActionStats(): ActionStats {
  const store = getDLEngineStore()
  
  return {
    totalActions: store.actions.history.length,
    cachedActions: store.actions.cached.length,
    incomingActions: store.actions.incoming.length,
    historyActions: store.actions.history.length,
    knownUUIDs: store.actions.knownUUIDs.size,
    outgoingTopics: Object.keys(store.actions.outgoing).length,
    forwardingTopics: store.forwardingTopics.size,
    actionQueues: store.actions.queues.size
  }
}

/**
 * 动作工具函数
 */
export const ActionUtils = {
  /**
   * 检查动作是否匹配类型
   */
  matchesType: <A extends Action>(action: ResolvedActionType, type: string | string[]): action is ResolvedActionType<A> => {
    if (Array.isArray(type)) {
      return type.includes(action.type as string)
    }
    return action.type === type
  },
  
  /**
   * 检查动作是否来自特定对等节点
   */
  isFromPeer: (action: ResolvedActionType, peerID: PeerID): boolean => {
    return action.$from === peerID
  },
  
  /**
   * 检查动作是否发送给特定对等节点
   */
  isForPeer: (action: ResolvedActionType, peerID: PeerID): boolean => {
    if (!action.$to) return false
    if (Array.isArray(action.$to)) {
      return action.$to.includes(peerID)
    }
    return action.$to === peerID
  },
  
  /**
   * 检查动作是否在指定时间范围内
   */
  isInTimeRange: (action: ResolvedActionType, startTime: number, endTime: number): boolean => {
    return action.$time >= startTime && action.$time <= endTime
  },
  
  /**
   * 获取动作的年龄（毫秒）
   */
  getActionAge: (action: ResolvedActionType): number => {
    return Date.now() - action.$time
  }
}
