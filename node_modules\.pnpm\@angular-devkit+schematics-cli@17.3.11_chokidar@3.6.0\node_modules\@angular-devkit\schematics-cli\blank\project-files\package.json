{"name": "<%= dasherize(name) %>", "version": "0.0.0", "description": "A blank schematics", "scripts": {"build": "tsc -p tsconfig.json", "test": "npm run build && jasmine src/**/*_spec.js"}, "keywords": ["schematics"], "author": "<%= author %>", "license": "MIT", "schematics": "./src/collection.json", "dependencies": {"@angular-devkit/core": "^<%= coreVersion %>", "@angular-devkit/schematics": "^<%= schematicsVersion %>", "typescript": "~5.4.2"}, "devDependencies": {"@types/node": "^18.18.0", "@types/jasmine": "~5.1.0", "jasmine": "^5.0.0"}}