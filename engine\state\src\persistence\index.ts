/**
 * DL-Engine 状态持久化系统
 * 处理状态的保存、加载和同步
 */

import { StateDefinition, DeepReadonly, ReceptorMap } from '../types'

/**
 * 持久化存储类型
 */
export enum PersistenceStorageType {
  LOCAL_STORAGE = 'localStorage',
  SESSION_STORAGE = 'sessionStorage',
  INDEXED_DB = 'indexedDB',
  MEMORY = 'memory',
  CUSTOM = 'custom'
}

/**
 * 持久化配置
 */
export interface PersistenceConfig {
  /** 存储类型 */
  storageType: PersistenceStorageType
  
  /** 存储键前缀 */
  keyPrefix?: string
  
  /** 是否启用压缩 */
  compression?: boolean
  
  /** 是否启用加密 */
  encryption?: boolean
  
  /** 加密密钥 */
  encryptionKey?: string
  
  /** 自动保存间隔（毫秒） */
  autoSaveInterval?: number
  
  /** 版本号 */
  version?: number
  
  /** 自定义序列化器 */
  serializer?: {
    serialize: (data: any) => string
    deserialize: (data: string) => any
  }
  
  /** 自定义存储适配器 */
  customStorage?: IPersistenceStorage
}

/**
 * 持久化存储接口
 */
export interface IPersistenceStorage {
  /** 获取数据 */
  getItem(key: string): Promise<string | null>
  
  /** 设置数据 */
  setItem(key: string, value: string): Promise<void>
  
  /** 移除数据 */
  removeItem(key: string): Promise<void>
  
  /** 清空所有数据 */
  clear(): Promise<void>
  
  /** 获取所有键 */
  getAllKeys(): Promise<string[]>
}

/**
 * LocalStorage适配器
 */
export class LocalStorageAdapter implements IPersistenceStorage {
  async getItem(key: string): Promise<string | null> {
    try {
      return localStorage.getItem(key)
    } catch (error) {
      console.error('LocalStorage getItem error:', error)
      return null
    }
  }
  
  async setItem(key: string, value: string): Promise<void> {
    try {
      localStorage.setItem(key, value)
    } catch (error) {
      console.error('LocalStorage setItem error:', error)
      throw error
    }
  }
  
  async removeItem(key: string): Promise<void> {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('LocalStorage removeItem error:', error)
      throw error
    }
  }
  
  async clear(): Promise<void> {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('LocalStorage clear error:', error)
      throw error
    }
  }
  
  async getAllKeys(): Promise<string[]> {
    try {
      return Object.keys(localStorage)
    } catch (error) {
      console.error('LocalStorage getAllKeys error:', error)
      return []
    }
  }
}

/**
 * SessionStorage适配器
 */
export class SessionStorageAdapter implements IPersistenceStorage {
  async getItem(key: string): Promise<string | null> {
    try {
      return sessionStorage.getItem(key)
    } catch (error) {
      console.error('SessionStorage getItem error:', error)
      return null
    }
  }
  
  async setItem(key: string, value: string): Promise<void> {
    try {
      sessionStorage.setItem(key, value)
    } catch (error) {
      console.error('SessionStorage setItem error:', error)
      throw error
    }
  }
  
  async removeItem(key: string): Promise<void> {
    try {
      sessionStorage.removeItem(key)
    } catch (error) {
      console.error('SessionStorage removeItem error:', error)
      throw error
    }
  }
  
  async clear(): Promise<void> {
    try {
      sessionStorage.clear()
    } catch (error) {
      console.error('SessionStorage clear error:', error)
      throw error
    }
  }
  
  async getAllKeys(): Promise<string[]> {
    try {
      return Object.keys(sessionStorage)
    } catch (error) {
      console.error('SessionStorage getAllKeys error:', error)
      return []
    }
  }
}

/**
 * 内存存储适配器
 */
export class MemoryStorageAdapter implements IPersistenceStorage {
  private storage: Map<string, string> = new Map()
  
  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null
  }
  
  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value)
  }
  
  async removeItem(key: string): Promise<void> {
    this.storage.delete(key)
  }
  
  async clear(): Promise<void> {
    this.storage.clear()
  }
  
  async getAllKeys(): Promise<string[]> {
    return Array.from(this.storage.keys())
  }
}

/**
 * 持久化管理器
 */
export class PersistenceManager {
  private static instance: PersistenceManager | null = null
  private storage: IPersistenceStorage
  private config: PersistenceConfig
  private autoSaveTimer?: NodeJS.Timeout
  
  constructor(config: PersistenceConfig) {
    this.config = config
    this.storage = this.createStorage(config)
    
    if (config.autoSaveInterval) {
      this.startAutoSave(config.autoSaveInterval)
    }
  }
  
  static getInstance(config?: PersistenceConfig): PersistenceManager {
    if (!PersistenceManager.instance) {
      if (!config) {
        throw new Error('PersistenceManager requires config for first initialization')
      }
      PersistenceManager.instance = new PersistenceManager(config)
    }
    return PersistenceManager.instance
  }
  
  /**
   * 保存状态
   */
  async saveState<S>(stateName: string, stateData: S): Promise<void> {
    try {
      const key = this.getStorageKey(stateName)
      const serializedData = this.serialize(stateData)
      
      await this.storage.setItem(key, serializedData)
    } catch (error) {
      console.error(`Failed to save state ${stateName}:`, error)
      throw error
    }
  }
  
  /**
   * 加载状态
   */
  async loadState<S>(stateName: string): Promise<S | null> {
    try {
      const key = this.getStorageKey(stateName)
      const serializedData = await this.storage.getItem(key)
      
      if (!serializedData) {
        return null
      }
      
      return this.deserialize(serializedData)
    } catch (error) {
      console.error(`Failed to load state ${stateName}:`, error)
      return null
    }
  }
  
  /**
   * 移除状态
   */
  async removeState(stateName: string): Promise<void> {
    try {
      const key = this.getStorageKey(stateName)
      await this.storage.removeItem(key)
    } catch (error) {
      console.error(`Failed to remove state ${stateName}:`, error)
      throw error
    }
  }
  
  /**
   * 清空所有状态
   */
  async clearAllStates(): Promise<void> {
    try {
      const keys = await this.storage.getAllKeys()
      const prefix = this.config.keyPrefix || 'dlengine_'
      
      for (const key of keys) {
        if (key.startsWith(prefix)) {
          await this.storage.removeItem(key)
        }
      }
    } catch (error) {
      console.error('Failed to clear all states:', error)
      throw error
    }
  }
  
  /**
   * 获取所有保存的状态名称
   */
  async getAllStateNames(): Promise<string[]> {
    try {
      const keys = await this.storage.getAllKeys()
      const prefix = this.config.keyPrefix || 'dlengine_'
      
      return keys
        .filter(key => key.startsWith(prefix))
        .map(key => key.substring(prefix.length))
    } catch (error) {
      console.error('Failed to get all state names:', error)
      return []
    }
  }
  
  /**
   * 检查状态是否存在
   */
  async hasState(stateName: string): Promise<boolean> {
    try {
      const key = this.getStorageKey(stateName)
      const data = await this.storage.getItem(key)
      return data !== null
    } catch (error) {
      console.error(`Failed to check state ${stateName}:`, error)
      return false
    }
  }
  
  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalStates: number
    totalSize: number
    stateNames: string[]
  }> {
    try {
      const stateNames = await this.getAllStateNames()
      let totalSize = 0
      
      for (const stateName of stateNames) {
        const key = this.getStorageKey(stateName)
        const data = await this.storage.getItem(key)
        if (data) {
          totalSize += data.length
        }
      }
      
      return {
        totalStates: stateNames.length,
        totalSize,
        stateNames
      }
    } catch (error) {
      console.error('Failed to get storage stats:', error)
      return {
        totalStates: 0,
        totalSize: 0,
        stateNames: []
      }
    }
  }
  
  /**
   * 销毁持久化管理器
   */
  destroy(): void {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = undefined
    }
  }
  
  private createStorage(config: PersistenceConfig): IPersistenceStorage {
    switch (config.storageType) {
      case PersistenceStorageType.LOCAL_STORAGE:
        return new LocalStorageAdapter()
      case PersistenceStorageType.SESSION_STORAGE:
        return new SessionStorageAdapter()
      case PersistenceStorageType.MEMORY:
        return new MemoryStorageAdapter()
      case PersistenceStorageType.CUSTOM:
        if (!config.customStorage) {
          throw new Error('Custom storage adapter is required for CUSTOM storage type')
        }
        return config.customStorage
      default:
        return new MemoryStorageAdapter()
    }
  }
  
  private getStorageKey(stateName: string): string {
    const prefix = this.config.keyPrefix || 'dlengine_'
    return `${prefix}${stateName}`
  }
  
  private serialize(data: any): string {
    if (this.config.serializer) {
      return this.config.serializer.serialize(data)
    }
    
    return JSON.stringify(data)
  }
  
  private deserialize(data: string): any {
    if (this.config.serializer) {
      return this.config.serializer.deserialize(data)
    }
    
    return JSON.parse(data)
  }
  
  private startAutoSave(interval: number): void {
    this.autoSaveTimer = setInterval(() => {
      // 这里可以实现自动保存逻辑
      // 例如保存所有标记为需要持久化的状态
    }, interval)
  }
}

/**
 * 持久化装饰器
 */
export function persistent(config?: Partial<PersistenceConfig>) {
  return function<S, I, E, R extends ReceptorMap>(stateDefinition: StateDefinition<S, I, E, R>) {
    // 这里可以扩展状态定义，添加持久化功能
    return stateDefinition
  }
}

/**
 * 便捷函数
 */
export const PersistenceUtils = {
  /**
   * 创建默认的持久化管理器
   */
  createDefaultManager(): PersistenceManager {
    return new PersistenceManager({
      storageType: PersistenceStorageType.LOCAL_STORAGE,
      keyPrefix: 'dlengine_',
      compression: false,
      encryption: false,
      autoSaveInterval: 30000 // 30秒
    })
  },
  
  /**
   * 快速保存状态
   */
  async quickSave<S>(stateName: string, stateData: S): Promise<void> {
    const manager = PersistenceManager.getInstance()
    return manager.saveState(stateName, stateData)
  },
  
  /**
   * 快速加载状态
   */
  async quickLoad<S>(stateName: string): Promise<S | null> {
    const manager = PersistenceManager.getInstance()
    return manager.loadState<S>(stateName)
  }
}
