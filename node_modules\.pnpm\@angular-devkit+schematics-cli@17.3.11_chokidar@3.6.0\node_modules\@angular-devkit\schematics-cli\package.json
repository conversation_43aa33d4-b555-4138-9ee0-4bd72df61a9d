{"name": "@angular-devkit/schematics-cli", "version": "17.3.11", "description": "Angular Schematics - CLI", "homepage": "https://github.com/angular/angular-cli", "bin": {"schematics": "./bin/schematics.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "scaffolding", "schematics", "sdk", "template", "tooling"], "schematics": "./collection.json", "dependencies": {"@angular-devkit/core": "17.3.11", "@angular-devkit/schematics": "17.3.11", "ansi-colors": "4.1.3", "inquirer": "9.2.15", "symbol-observable": "4.0.0", "yargs-parser": "21.1.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.13.0 || >=20.9.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}}