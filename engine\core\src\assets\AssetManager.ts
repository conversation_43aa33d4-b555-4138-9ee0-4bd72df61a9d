/**
 * DL-Engine 资产管理器
 * 统一管理所有资产的加载、缓存和生命周期
 */

import { EventEmitter } from 'events'
import { 
  Asset, 
  AssetType, 
  AssetStatus, 
  AssetLoadOptions, 
  AssetQueryOptions,
  AssetStats,
  AssetEvent,
  AssetEventType,
  AssetPriority
} from './AssetTypes'
import { AssetStateUtils, AssetState } from './AssetState'
import { AssetCache, CacheStrategy } from './AssetCache'
import { LoaderManager } from './AssetLoader'
import { TextureLoader } from './loaders/TextureLoader'
import { GLTFLoader } from './loaders/GLTFLoader'
import { AudioLoader } from './loaders/AudioLoader'
import { getState } from '@dl-engine/engine-state'

/**
 * 资产管理器配置
 */
export interface AssetManagerConfig {
  /** 最大并发加载数 */
  maxConcurrentLoads: number
  
  /** 缓存配置 */
  cache: {
    maxSize: number
    strategy: CacheStrategy
    defaultTTL: number
    enablePersistence: boolean
  }
  
  /** 加载超时时间 */
  defaultTimeout: number
  
  /** 默认重试次数 */
  defaultRetries: number
  
  /** 是否启用预加载 */
  enablePreloading: boolean
  
  /** 预加载优先级阈值 */
  preloadPriorityThreshold: AssetPriority
  
  /** 是否启用自动清理 */
  enableAutoCleanup: boolean
  
  /** 清理间隔（毫秒） */
  cleanupInterval: number
}

/**
 * 加载队列项
 */
interface LoadQueueItem {
  id: string
  url: string
  type: AssetType
  options: AssetLoadOptions
  priority: AssetPriority
  resolve: (asset: Asset) => void
  reject: (error: Error) => void
  retryCount: number
}

/**
 * 资产管理器
 */
export class AssetManager extends EventEmitter {
  private static instance: AssetManager | null = null
  
  private config: AssetManagerConfig
  private cache: AssetCache
  private loaderManager: LoaderManager
  private loadQueue: LoadQueueItem[] = []
  private activeLoads = new Set<string>()
  private cleanupTimer?: NodeJS.Timeout

  /**
   * 获取单例实例
   */
  static getInstance(config?: Partial<AssetManagerConfig>): AssetManager {
    if (!AssetManager.instance) {
      AssetManager.instance = new AssetManager(config)
    }
    return AssetManager.instance
  }

  constructor(config: Partial<AssetManagerConfig> = {}) {
    super()
    
    this.config = {
      maxConcurrentLoads: 4,
      cache: {
        maxSize: 512 * 1024 * 1024, // 512MB
        strategy: CacheStrategy.LRU,
        defaultTTL: 30 * 60 * 1000, // 30分钟
        enablePersistence: true
      },
      defaultTimeout: 30000, // 30秒
      defaultRetries: 3,
      enablePreloading: true,
      preloadPriorityThreshold: AssetPriority.NORMAL,
      enableAutoCleanup: true,
      cleanupInterval: 60000, // 1分钟
      ...config
    }

    this.cache = new AssetCache(this.config.cache)
    this.loaderManager = new LoaderManager()
    
    this.setupLoaders()
    this.setupEventListeners()
    
    if (this.config.enableAutoCleanup) {
      this.startCleanupTimer()
    }
  }

  /**
   * 设置加载器
   */
  private setupLoaders(): void {
    // 注册纹理加载器
    this.loaderManager.registerLoader(AssetType.TEXTURE, new TextureLoader())
    this.loaderManager.registerLoader(AssetType.IMAGE, new TextureLoader())
    
    // 注册模型加载器
    this.loaderManager.registerLoader(AssetType.MODEL, new GLTFLoader())
    
    // 注册音频加载器
    this.loaderManager.registerLoader(AssetType.AUDIO, new AudioLoader())
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听资产状态变化
    this.on('assetLoaded', (asset: Asset) => {
      this.processLoadQueue()
    })
    
    this.on('assetError', (error: Error, assetId: string) => {
      this.processLoadQueue()
    })
  }

  /**
   * 加载资产
   */
  async load(
    id: string,
    url: string,
    type: AssetType,
    options: AssetLoadOptions = {}
  ): Promise<Asset> {
    // 检查缓存
    const cached = this.cache.get(id)
    if (cached) {
      this.emit('assetLoaded', cached)
      return cached
    }

    // 检查是否已在加载
    const existingAsset = AssetStateUtils.getAsset(id)
    if (existingAsset && existingAsset.status === AssetStatus.LOADING) {
      return this.waitForAsset(id)
    }

    // 添加到加载队列
    return this.enqueueLoad(id, url, type, options)
  }

  /**
   * 批量加载资产
   */
  async loadBatch(
    assets: Array<{
      id: string
      url: string
      type: AssetType
      options?: AssetLoadOptions
    }>
  ): Promise<Asset[]> {
    const promises = assets.map(asset => 
      this.load(asset.id, asset.url, asset.type, asset.options)
    )
    
    return Promise.all(promises)
  }

  /**
   * 预加载资产
   */
  async preload(
    id: string,
    url: string,
    type: AssetType,
    options: AssetLoadOptions = {}
  ): Promise<void> {
    if (!this.config.enablePreloading) {
      return
    }

    const priority = options.priority || AssetPriority.LOW
    if (priority < this.config.preloadPriorityThreshold) {
      return
    }

    try {
      await this.load(id, url, type, { ...options, priority })
    } catch (error) {
      // 预加载失败不抛出错误
      console.warn(`预加载失败: ${id}`, error)
    }
  }

  /**
   * 获取资产
   */
  get(id: string): Asset | null {
    // 先从缓存获取
    const cached = this.cache.get(id)
    if (cached) {
      return cached
    }

    // 从状态获取
    return AssetStateUtils.getAsset(id) || null
  }

  /**
   * 检查资产是否存在
   */
  has(id: string): boolean {
    return this.cache.has(id) || AssetStateUtils.getAsset(id) !== undefined
  }

  /**
   * 卸载资产
   */
  unload(id: string): boolean {
    const asset = this.get(id)
    if (!asset) {
      return false
    }

    // 减少引用计数
    const newRefCount = Math.max(0, asset.refCount - 1)
    AssetStateUtils.updateAsset(id, { refCount: newRefCount })

    // 如果引用计数为0，从缓存移除
    if (newRefCount === 0) {
      this.cache.remove(id)
      
      // 触发事件
      AssetStateUtils.addEvent({
        type: AssetEventType.DISPOSED,
        assetId: id,
        timestamp: Date.now()
      })
      
      this.emit('assetUnloaded', asset)
    }

    return true
  }

  /**
   * 查询资产
   */
  query(options: AssetQueryOptions = {}): Asset[] {
    const allAssets = AssetStateUtils.getAllAssets()
    let filtered = allAssets

    // 类型过滤
    if (options.types && options.types.length > 0) {
      filtered = filtered.filter(asset => options.types!.includes(asset.type))
    }

    // 状态过滤
    if (options.statuses && options.statuses.length > 0) {
      filtered = filtered.filter(asset => options.statuses!.includes(asset.status))
    }

    // 标签过滤
    if (options.tags && options.tags.length > 0) {
      filtered = filtered.filter(asset => 
        options.tags!.some(tag => asset.tags.includes(tag))
      )
    }

    // 名称搜索
    if (options.namePattern) {
      const pattern = new RegExp(options.namePattern, 'i')
      filtered = filtered.filter(asset => pattern.test(asset.name))
    }

    // 排序
    if (options.sortBy) {
      const sortOrder = options.sortOrder || 'asc'
      filtered.sort((a, b) => {
        const aValue = a[options.sortBy!]
        const bValue = b[options.sortBy!]
        
        if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
        if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
        return 0
      })
    }

    // 分页
    if (options.offset || options.limit) {
      const start = options.offset || 0
      const end = options.limit ? start + options.limit : undefined
      filtered = filtered.slice(start, end)
    }

    return filtered
  }

  /**
   * 获取统计信息
   */
  getStats(): AssetStats {
    const state = getState(AssetState)
    return state.stats
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return this.cache.getStats()
  }

  /**
   * 清理过期资产
   */
  cleanup(): number {
    const cleaned = this.cache.cleanup()
    AssetStateUtils.cleanupExpiredCache()
    
    this.emit('cleanup', cleaned)
    return cleaned
  }

  /**
   * 清空所有资产
   */
  clear(): void {
    this.cache.clear()
    
    // 清空状态
    const allAssets = AssetStateUtils.getAllAssets()
    for (const asset of allAssets) {
      AssetStateUtils.removeAsset(asset.id)
    }
    
    // 清空加载队列
    this.loadQueue = []
    this.activeLoads.clear()
    
    this.emit('cleared')
  }

  /**
   * 销毁管理器
   */
  dispose(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }
    
    this.clear()
    this.cache.dispose()
    this.removeAllListeners()
    
    AssetManager.instance = null
  }

  // 私有方法

  /**
   * 添加到加载队列
   */
  private enqueueLoad(
    id: string,
    url: string,
    type: AssetType,
    options: AssetLoadOptions
  ): Promise<Asset> {
    return new Promise((resolve, reject) => {
      const priority = options.priority || AssetPriority.NORMAL
      
      const queueItem: LoadQueueItem = {
        id,
        url,
        type,
        options,
        priority,
        resolve,
        reject,
        retryCount: 0
      }

      // 按优先级插入队列
      this.insertByPriority(queueItem)
      
      // 处理队列
      this.processLoadQueue()
    })
  }

  /**
   * 按优先级插入队列
   */
  private insertByPriority(item: LoadQueueItem): void {
    let insertIndex = this.loadQueue.length
    
    for (let i = 0; i < this.loadQueue.length; i++) {
      if (this.loadQueue[i].priority < item.priority) {
        insertIndex = i
        break
      }
    }
    
    this.loadQueue.splice(insertIndex, 0, item)
  }

  /**
   * 处理加载队列
   */
  private async processLoadQueue(): Promise<void> {
    while (
      this.loadQueue.length > 0 && 
      this.activeLoads.size < this.config.maxConcurrentLoads
    ) {
      const item = this.loadQueue.shift()!
      this.activeLoads.add(item.id)
      
      try {
        const asset = await this.performLoad(item)
        
        // 添加到缓存
        if (asset.cacheable) {
          this.cache.set(asset, asset.cacheTTL)
        }
        
        item.resolve(asset)
        this.emit('assetLoaded', asset)
        
      } catch (error) {
        // 重试逻辑
        if (item.retryCount < this.config.defaultRetries) {
          item.retryCount++
          this.loadQueue.unshift(item) // 重新加入队列头部
          console.warn(`资产加载失败，重试 ${item.retryCount}/${this.config.defaultRetries}: ${item.id}`)
        } else {
          item.reject(error instanceof Error ? error : new Error(String(error)))
          this.emit('assetError', error, item.id)
        }
      } finally {
        this.activeLoads.delete(item.id)
      }
    }
  }

  /**
   * 执行加载
   */
  private async performLoad(item: LoadQueueItem): Promise<Asset> {
    const { id, url, type, options } = item
    
    // 设置默认选项
    const loadOptions: AssetLoadOptions = {
      timeout: this.config.defaultTimeout,
      ...options
    }

    // 使用加载器管理器加载
    return this.loaderManager.load(id, url, type, loadOptions)
  }

  /**
   * 等待资产加载完成
   */
  private async waitForAsset(id: string): Promise<Asset> {
    return new Promise((resolve, reject) => {
      const checkAsset = () => {
        const asset = AssetStateUtils.getAsset(id)
        if (!asset) {
          reject(new Error(`资产不存在: ${id}`))
          return
        }

        if (asset.status === AssetStatus.LOADED) {
          resolve(asset)
        } else if (asset.status === AssetStatus.ERROR) {
          reject(new Error(asset.error || '资产加载失败'))
        } else {
          // 继续等待
          setTimeout(checkAsset, 100)
        }
      }

      checkAsset()
    })
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }
}
