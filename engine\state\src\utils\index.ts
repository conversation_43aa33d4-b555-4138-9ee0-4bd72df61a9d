/**
 * DL-Engine 状态管理工具函数
 * 提供各种状态操作和工具函数
 */

import { State } from '@hookstate/core'
import { DeepReadonly, StateDefinition, ReceptorMap } from '../types'

/**
 * 状态工具类
 */
export class StateUtils {
  /**
   * 深度克隆对象
   */
  static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime()) as unknown as T
    }
    
    if (obj instanceof Array) {
      return obj.map(item => StateUtils.deepClone(item)) as unknown as T
    }
    
    if (typeof obj === 'object') {
      const cloned = {} as T
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = StateUtils.deepClone(obj[key])
        }
      }
      return cloned
    }
    
    return obj
  }
  
  /**
   * 深度比较两个对象是否相等
   */
  static deepEqual(a: any, b: any): boolean {
    if (a === b) return true
    
    if (a == null || b == null) return a === b
    
    if (typeof a !== typeof b) return false
    
    if (typeof a !== 'object') return a === b
    
    if (Array.isArray(a) !== Array.isArray(b)) return false
    
    if (Array.isArray(a)) {
      if (a.length !== b.length) return false
      for (let i = 0; i < a.length; i++) {
        if (!StateUtils.deepEqual(a[i], b[i])) return false
      }
      return true
    }
    
    const keysA = Object.keys(a)
    const keysB = Object.keys(b)
    
    if (keysA.length !== keysB.length) return false
    
    for (const key of keysA) {
      if (!keysB.includes(key)) return false
      if (!StateUtils.deepEqual(a[key], b[key])) return false
    }
    
    return true
  }
  
  /**
   * 深度合并对象
   */
  static deepMerge<T>(target: T, source: Partial<T>): T {
    const result = StateUtils.deepClone(target)
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key]
        const targetValue = result[key]
        
        if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue) &&
            targetValue && typeof targetValue === 'object' && !Array.isArray(targetValue)) {
          result[key] = StateUtils.deepMerge(targetValue, sourceValue)
        } else {
          result[key] = sourceValue as T[Extract<keyof T, string>]
        }
      }
    }
    
    return result
  }
  
  /**
   * 获取对象的深度路径值
   */
  static getDeepValue(obj: any, path: string): any {
    const keys = path.split('.')
    let current = obj
    
    for (const key of keys) {
      if (current == null || typeof current !== 'object') {
        return undefined
      }
      current = current[key]
    }
    
    return current
  }
  
  /**
   * 设置对象的深度路径值
   */
  static setDeepValue(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (current[key] == null || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    current[keys[keys.length - 1]] = value
  }
  
  /**
   * 删除对象的深度路径值
   */
  static deleteDeepValue(obj: any, path: string): boolean {
    const keys = path.split('.')
    let current = obj
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (current[key] == null || typeof current[key] !== 'object') {
        return false
      }
      current = current[key]
    }
    
    const lastKey = keys[keys.length - 1]
    if (lastKey in current) {
      delete current[lastKey]
      return true
    }
    
    return false
  }
  
  /**
   * 扁平化对象
   */
  static flatten(obj: any, prefix: string = '', separator: string = '.'): Record<string, any> {
    const result: Record<string, any> = {}
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}${separator}${key}` : key
        const value = obj[key]
        
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          Object.assign(result, StateUtils.flatten(value, newKey, separator))
        } else {
          result[newKey] = value
        }
      }
    }
    
    return result
  }
  
  /**
   * 反扁平化对象
   */
  static unflatten(obj: Record<string, any>, separator: string = '.'): any {
    const result: any = {}
    
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        StateUtils.setDeepValue(result, key.replace(new RegExp(separator, 'g'), '.'), obj[key])
      }
    }
    
    return result
  }
}

/**
 * 状态验证工具
 */
export class StateValidator {
  /**
   * 验证状态结构
   */
  static validateStructure(state: any, schema: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    const validate = (obj: any, schemaObj: any, path: string = '') => {
      if (typeof schemaObj === 'function') {
        // 类型检查
        if (typeof obj !== schemaObj.name.toLowerCase()) {
          errors.push(`${path}: expected ${schemaObj.name.toLowerCase()}, got ${typeof obj}`)
        }
      } else if (Array.isArray(schemaObj)) {
        if (!Array.isArray(obj)) {
          errors.push(`${path}: expected array, got ${typeof obj}`)
        } else if (schemaObj.length > 0) {
          obj.forEach((item, index) => {
            validate(item, schemaObj[0], `${path}[${index}]`)
          })
        }
      } else if (typeof schemaObj === 'object' && schemaObj !== null) {
        if (typeof obj !== 'object' || obj === null) {
          errors.push(`${path}: expected object, got ${typeof obj}`)
        } else {
          for (const key in schemaObj) {
            if (schemaObj.hasOwnProperty(key)) {
              const newPath = path ? `${path}.${key}` : key
              if (!(key in obj)) {
                errors.push(`${newPath}: missing required property`)
              } else {
                validate(obj[key], schemaObj[key], newPath)
              }
            }
          }
        }
      }
    }
    
    validate(state, schema)
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  /**
   * 验证状态值范围
   */
  static validateRange(value: number, min?: number, max?: number): boolean {
    if (min !== undefined && value < min) return false
    if (max !== undefined && value > max) return false
    return true
  }
  
  /**
   * 验证字符串长度
   */
  static validateStringLength(value: string, minLength?: number, maxLength?: number): boolean {
    if (minLength !== undefined && value.length < minLength) return false
    if (maxLength !== undefined && value.length > maxLength) return false
    return true
  }
  
  /**
   * 验证数组长度
   */
  static validateArrayLength(value: any[], minLength?: number, maxLength?: number): boolean {
    if (minLength !== undefined && value.length < minLength) return false
    if (maxLength !== undefined && value.length > maxLength) return false
    return true
  }
}

/**
 * 状态性能监控工具
 */
export class StatePerformanceMonitor {
  private static measurements: Map<string, number[]> = new Map()
  
  /**
   * 开始测量
   */
  static startMeasurement(name: string): () => void {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      if (!StatePerformanceMonitor.measurements.has(name)) {
        StatePerformanceMonitor.measurements.set(name, [])
      }
      
      StatePerformanceMonitor.measurements.get(name)!.push(duration)
    }
  }
  
  /**
   * 获取性能统计
   */
  static getStats(name: string): {
    count: number
    total: number
    average: number
    min: number
    max: number
  } | null {
    const measurements = StatePerformanceMonitor.measurements.get(name)
    if (!measurements || measurements.length === 0) {
      return null
    }
    
    const total = measurements.reduce((sum, duration) => sum + duration, 0)
    const average = total / measurements.length
    const min = Math.min(...measurements)
    const max = Math.max(...measurements)
    
    return {
      count: measurements.length,
      total,
      average,
      min,
      max
    }
  }
  
  /**
   * 清除测量数据
   */
  static clearMeasurements(name?: string): void {
    if (name) {
      StatePerformanceMonitor.measurements.delete(name)
    } else {
      StatePerformanceMonitor.measurements.clear()
    }
  }
  
  /**
   * 获取所有测量名称
   */
  static getAllMeasurementNames(): string[] {
    return Array.from(StatePerformanceMonitor.measurements.keys())
  }
}

/**
 * 状态调试工具
 */
export class StateDebugger {
  private static logs: Array<{
    timestamp: number
    type: 'get' | 'set' | 'action'
    stateName: string
    data: any
  }> = []
  
  /**
   * 记录状态访问
   */
  static logStateAccess(type: 'get' | 'set' | 'action', stateName: string, data: any): void {
    StateDebugger.logs.push({
      timestamp: Date.now(),
      type,
      stateName,
      data: StateUtils.deepClone(data)
    })
    
    // 限制日志数量
    if (StateDebugger.logs.length > 1000) {
      StateDebugger.logs.splice(0, 100)
    }
  }
  
  /**
   * 获取状态日志
   */
  static getLogs(stateName?: string, type?: 'get' | 'set' | 'action'): typeof StateDebugger.logs {
    let filteredLogs = StateDebugger.logs
    
    if (stateName) {
      filteredLogs = filteredLogs.filter(log => log.stateName === stateName)
    }
    
    if (type) {
      filteredLogs = filteredLogs.filter(log => log.type === type)
    }
    
    return filteredLogs
  }
  
  /**
   * 清除日志
   */
  static clearLogs(): void {
    StateDebugger.logs.length = 0
  }
  
  /**
   * 导出日志
   */
  static exportLogs(): string {
    return JSON.stringify(StateDebugger.logs, null, 2)
  }
}

/**
 * 常用工具函数
 */
export const CommonUtils = {
  /**
   * 生成唯一ID
   */
  generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },
  
  /**
   * 防抖函数
   */
  debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout
    return ((...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func.apply(this, args), delay)
    }) as T
  },
  
  /**
   * 节流函数
   */
  throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let lastCall = 0
    return ((...args: any[]) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        return func.apply(this, args)
      }
    }) as T
  },
  
  /**
   * 延迟执行
   */
  delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  },
  
  /**
   * 重试函数
   */
  async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        if (attempt < maxAttempts) {
          await CommonUtils.delay(delayMs * attempt)
        }
      }
    }
    
    throw lastError!
  }
}
