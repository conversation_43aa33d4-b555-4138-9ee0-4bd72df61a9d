/**
 * DL-Engine 学习分析模块
 * 提供学习行为分析、进度跟踪、个性化推荐等功能
 */

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'
import { OllamaClient } from '../ollama/OllamaClient'

/**
 * 学习行为类型
 */
export type LearningActionType = 
  | 'view_content'
  | 'complete_exercise'
  | 'submit_assignment'
  | 'ask_question'
  | 'participate_discussion'
  | 'use_tool'
  | 'create_content'
  | 'collaborate'
  | 'review_material'
  | 'take_assessment'

/**
 * 学习行为记录
 */
export interface LearningAction {
  /** 行为ID */
  id: string
  
  /** 用户ID */
  userId: Entity
  
  /** 行为类型 */
  type: LearningActionType
  
  /** 内容ID */
  contentId?: string
  
  /** 行为时间戳 */
  timestamp: number
  
  /** 持续时间（毫秒） */
  duration: number
  
  /** 行为结果 */
  result?: {
    /** 是否成功 */
    success: boolean
    
    /** 得分 */
    score?: number
    
    /** 完成度 */
    completion?: number
    
    /** 尝试次数 */
    attempts?: number
  }
  
  /** 上下文信息 */
  context?: {
    /** 设备类型 */
    device: string
    
    /** 学习环境 */
    environment: 'vr' | 'ar' | 'desktop' | 'mobile'
    
    /** 协作者 */
    collaborators?: Entity[]
    
    /** 使用的工具 */
    tools?: string[]
  }
  
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 学习者画像
 */
export interface LearnerProfile {
  /** 用户ID */
  userId: Entity
  
  /** 学习风格 */
  learningStyle: {
    /** 视觉学习偏好 */
    visual: number
    
    /** 听觉学习偏好 */
    auditory: number
    
    /** 动手学习偏好 */
    kinesthetic: number
    
    /** 阅读写作偏好 */
    readingWriting: number
  }
  
  /** 知识水平 */
  knowledgeLevel: Record<string, number>
  
  /** 技能掌握度 */
  skillMastery: Record<string, {
    level: number
    confidence: number
    lastAssessed: number
  }>
  
  /** 学习偏好 */
  preferences: {
    /** 难度偏好 */
    difficultyPreference: number
    
    /** 学习节奏 */
    pace: 'slow' | 'medium' | 'fast'
    
    /** 反馈频率偏好 */
    feedbackFrequency: 'immediate' | 'periodic' | 'minimal'
    
    /** 协作偏好 */
    collaborationPreference: number
  }
  
  /** 学习目标 */
  goals: Array<{
    id: string
    description: string
    targetDate: number
    progress: number
    priority: 'low' | 'medium' | 'high'
  }>
  
  /** 学习统计 */
  statistics: {
    /** 总学习时间 */
    totalLearningTime: number
    
    /** 平均会话时长 */
    averageSessionDuration: number
    
    /** 学习频率 */
    learningFrequency: number
    
    /** 完成率 */
    completionRate: number
    
    /** 平均得分 */
    averageScore: number
  }
}

/**
 * 学习路径推荐
 */
export interface LearningPathRecommendation {
  /** 推荐ID */
  id: string
  
  /** 用户ID */
  userId: Entity
  
  /** 推荐标题 */
  title: string
  
  /** 推荐描述 */
  description: string
  
  /** 推荐内容列表 */
  contents: Array<{
    id: string
    title: string
    type: 'lesson' | 'exercise' | 'project' | 'assessment'
    difficulty: number
    estimatedDuration: number
    prerequisites: string[]
  }>
  
  /** 推荐理由 */
  reasoning: string
  
  /** 置信度 */
  confidence: number
  
  /** 生成时间 */
  generatedAt: number
  
  /** 预期收益 */
  expectedOutcomes: string[]
}

/**
 * 学习分析状态
 */
export interface LearningAnalyticsState {
  /** 学习行为记录 */
  actions: Record<string, LearningAction>

  /** 学习者画像 */
  profiles: Record<Entity, LearnerProfile>

  /** 推荐记录 */
  recommendations: Record<Entity, LearningPathRecommendation[]>
  
  /** 分析配置 */
  config: {
    /** 行为记录保留天数 */
    actionRetentionDays: number
    
    /** 画像更新频率（小时） */
    profileUpdateFrequency: number
    
    /** 推荐生成频率（小时） */
    recommendationFrequency: number
    
    /** 是否启用实时分析 */
    enableRealTimeAnalysis: boolean
  }
  
  /** 统计信息 */
  stats: {
    totalActions: number
    activeUsers: number
    averageEngagement: number
    completionRates: Record<string, number>
  }
}

/**
 * 学习分析状态定义
 */
export const LearningAnalyticsState = defineState({
  name: 'DLEngine.LearningAnalytics',
  initial: (): LearningAnalyticsState => ({
    actions: {},
    profiles: {},
    recommendations: {},
    config: {
      actionRetentionDays: 90,
      profileUpdateFrequency: 24,
      recommendationFrequency: 168, // 一周
      enableRealTimeAnalysis: true
    },
    stats: {
      totalActions: 0,
      activeUsers: 0,
      averageEngagement: 0,
      completionRates: {}
    }
  })
})

/**
 * 学习分析器
 */
export class LearningAnalytics {
  private static instance: LearningAnalytics | null = null
  private ollamaClient: OllamaClient

  // 内部存储
  private actions: Map<string, LearningAction> = new Map()
  private profiles: Map<Entity, LearnerProfile> = new Map()
  private recommendations: Map<Entity, LearningPathRecommendation[]> = new Map()

  /**
   * 获取单例实例
   */
  static getInstance(): LearningAnalytics {
    if (!LearningAnalytics.instance) {
      LearningAnalytics.instance = new LearningAnalytics()
    }
    return LearningAnalytics.instance
  }

  constructor() {
    this.ollamaClient = OllamaClient.getInstance()
  }
  
  /**
   * 记录学习行为
   */
  recordAction(action: Omit<LearningAction, 'id' | 'timestamp'>): void {
    const fullAction: LearningAction = {
      ...action,
      id: this.generateActionId(),
      timestamp: Date.now()
    }

    // 添加到内部存储
    this.actions.set(fullAction.id, fullAction)

    // 更新状态
    const state = getMutableState(LearningAnalyticsState)
    state.actions[fullAction.id].set(fullAction)
    state.stats.totalActions.set(state.stats.totalActions.value + 1)

    // 如果启用实时分析，立即更新画像
    const config = getState(LearningAnalyticsState).config
    if (config.enableRealTimeAnalysis) {
      this.updateLearnerProfile(action.userId)
    }
  }
  
  /**
   * 更新学习者画像
   */
  async updateLearnerProfile(userId: Entity): Promise<void> {
    try {
      const actions = this.getUserActions(userId)
      const currentProfile = this.profiles.get(userId)

      const updatedProfile = await this.analyzeLearnerBehavior(userId, actions, currentProfile)

      // 更新内部存储
      this.profiles.set(userId, updatedProfile)

      // 更新状态
      getMutableState(LearningAnalyticsState).profiles[userId].set(updatedProfile)
      
    } catch (error) {
      console.error('Failed to update learner profile:', error)
    }
  }
  
  /**
   * 分析学习者行为
   */
  private async analyzeLearnerBehavior(
    userId: Entity,
    actions: LearningAction[],
    currentProfile?: LearnerProfile
  ): Promise<LearnerProfile> {
    // 计算学习风格
    const learningStyle = this.calculateLearningStyle(actions)
    
    // 计算知识水平
    const knowledgeLevel = this.calculateKnowledgeLevel(actions)
    
    // 计算技能掌握度
    const skillMastery = this.calculateSkillMastery(actions)
    
    // 计算学习偏好
    const preferences = this.calculateLearningPreferences(actions)
    
    // 计算学习统计
    const statistics = this.calculateLearningStatistics(actions)
    
    // 保留现有目标或创建默认目标
    const goals = currentProfile?.goals || []
    
    return {
      userId,
      learningStyle,
      knowledgeLevel,
      skillMastery,
      preferences,
      goals,
      statistics
    }
  }
  
  /**
   * 计算学习风格
   */
  private calculateLearningStyle(actions: LearningAction[]): LearnerProfile['learningStyle'] {
    let visual = 0, auditory = 0, kinesthetic = 0, readingWriting = 0
    
    actions.forEach(action => {
      switch (action.type) {
        case 'view_content':
          visual += 1
          break
        case 'participate_discussion':
          auditory += 1
          break
        case 'use_tool':
        case 'create_content':
          kinesthetic += 1
          break
        case 'complete_exercise':
        case 'submit_assignment':
          readingWriting += 1
          break
      }
    })
    
    const total = visual + auditory + kinesthetic + readingWriting
    if (total === 0) {
      return { visual: 0.25, auditory: 0.25, kinesthetic: 0.25, readingWriting: 0.25 }
    }
    
    return {
      visual: visual / total,
      auditory: auditory / total,
      kinesthetic: kinesthetic / total,
      readingWriting: readingWriting / total
    }
  }
  
  /**
   * 计算知识水平
   */
  private calculateKnowledgeLevel(actions: LearningAction[]): Record<string, number> {
    const knowledgeLevel: Record<string, number> = {}
    
    actions.forEach(action => {
      if (action.contentId && action.result?.score !== undefined) {
        // 假设contentId包含主题信息
        const topic = this.extractTopicFromContentId(action.contentId)
        if (!knowledgeLevel[topic]) {
          knowledgeLevel[topic] = 0
        }
        
        // 基于得分更新知识水平
        knowledgeLevel[topic] = Math.max(knowledgeLevel[topic], action.result.score)
      }
    })
    
    return knowledgeLevel
  }
  
  /**
   * 计算技能掌握度
   */
  private calculateSkillMastery(actions: LearningAction[]): LearnerProfile['skillMastery'] {
    const skillMastery: LearnerProfile['skillMastery'] = {}
    
    actions.forEach(action => {
      if (action.result?.score !== undefined) {
        const skill = this.extractSkillFromAction(action)
        if (skill) {
          if (!skillMastery[skill]) {
            skillMastery[skill] = { level: 0, confidence: 0, lastAssessed: 0 }
          }
          
          skillMastery[skill].level = Math.max(skillMastery[skill].level, action.result.score)
          skillMastery[skill].confidence = action.result.score
          skillMastery[skill].lastAssessed = action.timestamp
        }
      }
    })
    
    return skillMastery
  }
  
  /**
   * 计算学习偏好
   */
  private calculateLearningPreferences(actions: LearningAction[]): LearnerProfile['preferences'] {
    const durations = actions.map(a => a.duration).filter(d => d > 0)
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length || 0
    
    const collaborativeActions = actions.filter(a => a.context?.collaborators && a.context.collaborators.length > 0)
    const collaborationPreference = collaborativeActions.length / actions.length
    
    return {
      difficultyPreference: 0.5, // 默认中等难度
      pace: avgDuration > 600000 ? 'slow' : avgDuration > 300000 ? 'medium' : 'fast', // 基于平均持续时间
      feedbackFrequency: 'periodic', // 默认周期性反馈
      collaborationPreference
    }
  }
  
  /**
   * 计算学习统计
   */
  private calculateLearningStatistics(actions: LearningAction[]): LearnerProfile['statistics'] {
    const totalLearningTime = actions.reduce((sum, action) => sum + action.duration, 0)
    const averageSessionDuration = totalLearningTime / actions.length || 0
    
    const completedActions = actions.filter(a => a.result?.completion === 1)
    const completionRate = completedActions.length / actions.length || 0
    
    const scoredActions = actions.filter(a => a.result?.score !== undefined)
    const averageScore = scoredActions.reduce((sum, a) => sum + (a.result?.score || 0), 0) / scoredActions.length || 0
    
    // 计算学习频率（每天的平均行为数）
    const daySpan = Math.max(1, (Date.now() - Math.min(...actions.map(a => a.timestamp))) / (24 * 60 * 60 * 1000))
    const learningFrequency = actions.length / daySpan
    
    return {
      totalLearningTime,
      averageSessionDuration,
      learningFrequency,
      completionRate,
      averageScore
    }
  }
  
  /**
   * 生成学习路径推荐
   */
  async generateRecommendations(userId: Entity): Promise<LearningPathRecommendation> {
    try {
      const profile = this.profiles.get(userId)
      if (!profile) {
        throw new Error('User profile not found')
      }
      
      const prompt = `基于以下学习者画像，生成个性化的学习路径推荐：

学习风格：${JSON.stringify(profile.learningStyle)}
知识水平：${JSON.stringify(profile.knowledgeLevel)}
学习偏好：${JSON.stringify(profile.preferences)}
学习统计：${JSON.stringify(profile.statistics)}

请返回JSON格式的推荐：
{
  "title": "推荐标题",
  "description": "推荐描述",
  "contents": [
    {
      "id": "内容ID",
      "title": "内容标题",
      "type": "lesson|exercise|project|assessment",
      "difficulty": 0-1之间的难度值,
      "estimatedDuration": 预估时长（分钟）,
      "prerequisites": ["前置要求"]
    }
  ],
  "reasoning": "推荐理由",
  "confidence": 0-1之间的置信度,
  "expectedOutcomes": ["预期学习成果"]
}`

      const response = await this.ollamaClient.generate({
        model: 'llama2',
        prompt,
        format: 'json'
      })
      
      const recommendationData = JSON.parse(response.response)
      
      const recommendation: LearningPathRecommendation = {
        id: this.generateRecommendationId(),
        userId,
        title: recommendationData.title,
        description: recommendationData.description,
        contents: recommendationData.contents,
        reasoning: recommendationData.reasoning,
        confidence: recommendationData.confidence,
        generatedAt: Date.now(),
        expectedOutcomes: recommendationData.expectedOutcomes
      }
      
      // 添加到内部存储
      const userRecommendations = this.recommendations.get(userId) || []
      userRecommendations.push(recommendation)
      this.recommendations.set(userId, userRecommendations)

      // 更新状态
      getMutableState(LearningAnalyticsState).recommendations[userId].set(userRecommendations)
      
      return recommendation
      
    } catch (error) {
      console.error('Failed to generate recommendations:', error)
      throw error
    }
  }
  
  /**
   * 获取用户行为记录
   */
  private getUserActions(userId: Entity): LearningAction[] {
    return Array.from(this.actions.values()).filter(action => action.userId === userId)
  }
  
  /**
   * 从内容ID提取主题
   */
  private extractTopicFromContentId(contentId: string): string {
    // 简单的主题提取逻辑，实际项目中可能需要更复杂的映射
    const parts = contentId.split('_')
    return parts[0] || 'general'
  }
  
  /**
   * 从行为中提取技能
   */
  private extractSkillFromAction(action: LearningAction): string | null {
    // 基于行为类型和内容ID推断技能
    switch (action.type) {
      case 'complete_exercise':
        return 'problem_solving'
      case 'create_content':
        return 'creativity'
      case 'collaborate':
        return 'collaboration'
      case 'participate_discussion':
        return 'communication'
      default:
        return null
    }
  }
  
  /**
   * 生成行为ID
   */
  private generateActionId(): string {
    return `action_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成推荐ID
   */
  private generateRecommendationId(): string {
    return `rec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
}
