import * as React from 'react';
import CacheEntity from './Cache';
export declare const ATTR_TOKEN = "data-token-hash";
export declare const ATTR_MARK = "data-css-hash";
export declare const ATTR_DEV_CACHE_PATH = "data-dev-cache-path";
export declare const CSS_IN_JS_INSTANCE = "__cssinjs_instance__";
export declare const CSS_IN_JS_INSTANCE_ID: string;
export declare function createCache(): CacheEntity;
export declare type HashPriority = 'low' | 'high';
export interface StyleContextProps {
    autoClear?: boolean;
    /** @private Test only. Not work in production. */
    mock?: 'server' | 'client';
    /**
     * Only set when you need ssr to extract style on you own.
     * If not provided, it will auto create <style /> on the end of Provider in server side.
     */
    cache: CacheEntity;
    /** Tell children that this context is default generated context */
    defaultCache: boolean;
    /** Use `:where` selector to reduce hashId css selector priority */
    hashPriority?: HashPriority;
}
declare const StyleContext: React.Context<StyleContextProps>;
export declare type StyleProviderProps = Partial<StyleContextProps> & {
    children?: React.ReactNode;
};
export declare const StyleProvider: React.FC<StyleProviderProps>;
export default StyleContext;
