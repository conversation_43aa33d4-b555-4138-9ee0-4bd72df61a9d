/**
 * DL-Engine 资产加载器基类
 * 提供统一的资产加载接口
 */

import { 
  Asset, 
  AssetType, 
  AssetStatus, 
  AssetLoadOptions, 
  AssetPriority,
  AssetEventType,
  AssetFactory,
  AssetProcessor
} from './AssetTypes'
import { AssetStateUtils } from './AssetState'

/**
 * 加载器注册表
 */
export interface LoaderRegistry {
  [key: string]: AssetLoader<any>
}

/**
 * 加载进度信息
 */
export interface LoadProgress {
  loaded: number
  total: number
  percentage: number
  asset: Asset
}

/**
 * 资产加载器基类
 */
export abstract class AssetLoader<T extends Asset = Asset> {
  protected supportedTypes: AssetType[] = []
  protected processors: AssetProcessor[] = []

  /**
   * 检查是否支持指定类型
   */
  supports(type: AssetType): boolean {
    return this.supportedTypes.includes(type)
  }

  /**
   * 添加处理器
   */
  addProcessor(processor: AssetProcessor): void {
    this.processors.push(processor)
  }

  /**
   * 移除处理器
   */
  removeProcessor(processor: AssetProcessor): void {
    const index = this.processors.indexOf(processor)
    if (index > -1) {
      this.processors.splice(index, 1)
    }
  }

  /**
   * 加载资产
   */
  async load(
    id: string, 
    url: string, 
    type: AssetType, 
    options: AssetLoadOptions = {}
  ): Promise<T> {
    // 创建资产对象
    const asset = this.createAsset(id, url, type, options)
    
    // 添加到状态管理
    AssetStateUtils.addAsset(asset)
    AssetStateUtils.addEvent({
      type: AssetEventType.LOAD_START,
      assetId: id,
      asset,
      timestamp: Date.now()
    })

    try {
      // 更新状态为加载中
      AssetStateUtils.updateAsset(id, { status: AssetStatus.LOADING })
      AssetStateUtils.markAsLoading(id)

      // 执行实际加载
      const loadedAsset = await this.performLoad(asset, options)

      // 应用处理器
      const processedAsset = await this.applyProcessors(loadedAsset, options)

      // 更新状态为已加载
      AssetStateUtils.updateAsset(id, { 
        ...processedAsset,
        status: AssetStatus.LOADED,
        progress: 1,
        lastAccessedAt: new Date()
      })
      AssetStateUtils.markAsLoaded(id)

      // 触发完成事件
      AssetStateUtils.addEvent({
        type: AssetEventType.LOAD_COMPLETE,
        assetId: id,
        asset: processedAsset,
        timestamp: Date.now()
      })

      // 调用完成回调
      if (options.onComplete) {
        options.onComplete(processedAsset)
      }

      return processedAsset as T

    } catch (error) {
      // 更新状态为错误
      const errorMessage = error instanceof Error ? error.message : String(error)
      AssetStateUtils.updateAsset(id, { 
        status: AssetStatus.ERROR,
        error: errorMessage
      })
      AssetStateUtils.markAsLoaded(id)

      // 触发错误事件
      AssetStateUtils.addEvent({
        type: AssetEventType.LOAD_ERROR,
        assetId: id,
        error: error instanceof Error ? error : new Error(String(error)),
        timestamp: Date.now()
      })

      // 调用错误回调
      if (options.onError) {
        options.onError(error instanceof Error ? error : new Error(String(error)))
      }

      throw error
    }
  }

  /**
   * 创建资产对象
   */
  protected createAsset(
    id: string, 
    url: string, 
    type: AssetType, 
    options: AssetLoadOptions
  ): Asset {
    return {
      id,
      name: this.extractNameFromUrl(url),
      type,
      url,
      data: null,
      status: AssetStatus.UNLOADED,
      size: 0,
      mimeType: this.getMimeType(url),
      progress: 0,
      createdAt: new Date(),
      lastAccessedAt: new Date(),
      refCount: 0,
      cacheable: options.cache !== false,
      cacheTTL: options.cacheTTL || 30 * 60 * 1000, // 30分钟
      metadata: {},
      dependencies: [],
      tags: []
    }
  }

  /**
   * 执行实际加载（子类实现）
   */
  protected abstract performLoad(asset: Asset, options: AssetLoadOptions): Promise<Asset>

  /**
   * 应用处理器
   */
  protected async applyProcessors(asset: Asset, options: AssetLoadOptions): Promise<Asset> {
    let processedAsset = asset

    for (const processor of this.processors) {
      try {
        const result = await processor(processedAsset.data, processedAsset, options)
        processedAsset = {
          ...processedAsset,
          data: result
        }
      } catch (error) {
        console.warn(`处理器执行失败:`, error)
      }
    }

    return processedAsset
  }

  /**
   * 从URL提取名称
   */
  protected extractNameFromUrl(url: string): string {
    const parts = url.split('/')
    const filename = parts[parts.length - 1]
    return filename.split('.')[0] || 'unnamed'
  }

  /**
   * 获取MIME类型
   */
  protected getMimeType(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase()
    
    const mimeTypes: Record<string, string> = {
      // 图片
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'svg': 'image/svg+xml',
      
      // 3D模型
      'gltf': 'model/gltf+json',
      'glb': 'model/gltf-binary',
      'obj': 'model/obj',
      'fbx': 'model/fbx',
      
      // 音频
      'mp3': 'audio/mpeg',
      'wav': 'audio/wav',
      'ogg': 'audio/ogg',
      'flac': 'audio/flac',
      
      // 视频
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'avi': 'video/avi',
      
      // 字体
      'ttf': 'font/ttf',
      'otf': 'font/otf',
      'woff': 'font/woff',
      'woff2': 'font/woff2',
      
      // 数据
      'json': 'application/json',
      'xml': 'application/xml',
      'txt': 'text/plain'
    }

    return mimeTypes[extension || ''] || 'application/octet-stream'
  }

  /**
   * 创建进度更新函数
   */
  protected createProgressUpdater(assetId: string, options: AssetLoadOptions) {
    return (loaded: number, total: number) => {
      const progress = total > 0 ? loaded / total : 0
      
      // 更新资产进度
      AssetStateUtils.updateAsset(assetId, { progress })
      
      // 触发进度事件
      AssetStateUtils.addEvent({
        type: AssetEventType.LOAD_PROGRESS,
        assetId,
        progress,
        timestamp: Date.now()
      })
      
      // 调用进度回调
      if (options.onProgress) {
        options.onProgress(progress)
      }
    }
  }

  /**
   * 处理网络请求
   */
  protected async fetchWithProgress(
    url: string, 
    options: AssetLoadOptions,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<Response> {
    const response = await fetch(url, {
      signal: this.createAbortSignal(options.timeout)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // 如果有进度回调且支持流式读取
    if (onProgress && response.body) {
      const contentLength = response.headers.get('content-length')
      const total = contentLength ? parseInt(contentLength, 10) : 0
      
      if (total > 0) {
        const reader = response.body.getReader()
        const chunks: Uint8Array[] = []
        let loaded = 0

        while (true) {
          const { done, value } = await reader.read()
          
          if (done) break
          
          chunks.push(value)
          loaded += value.length
          onProgress(loaded, total)
        }

        // 重新构造响应
        const blob = new Blob(chunks)
        return new Response(blob, {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers
        })
      }
    }

    return response
  }

  /**
   * 创建超时信号
   */
  protected createAbortSignal(timeout?: number): AbortSignal | undefined {
    if (!timeout) return undefined
    
    const controller = new AbortController()
    setTimeout(() => controller.abort(), timeout)
    return controller.signal
  }

  /**
   * 计算文件大小
   */
  protected calculateSize(data: any): number {
    if (data instanceof ArrayBuffer) {
      return data.byteLength
    }
    if (data instanceof Blob) {
      return data.size
    }
    if (typeof data === 'string') {
      return new Blob([data]).size
    }
    if (data && typeof data === 'object') {
      return new Blob([JSON.stringify(data)]).size
    }
    return 0
  }
}

/**
 * 加载器管理器
 */
export class LoaderManager {
  private loaders = new Map<AssetType, AssetLoader>()
  private factories = new Map<AssetType, AssetFactory>()

  /**
   * 注册加载器
   */
  registerLoader(type: AssetType, loader: AssetLoader): void {
    this.loaders.set(type, loader)
  }

  /**
   * 注册工厂函数
   */
  registerFactory(type: AssetType, factory: AssetFactory): void {
    this.factories.set(type, factory)
  }

  /**
   * 获取加载器
   */
  getLoader(type: AssetType): AssetLoader | undefined {
    return this.loaders.get(type)
  }

  /**
   * 获取工厂函数
   */
  getFactory(type: AssetType): AssetFactory | undefined {
    return this.factories.get(type)
  }

  /**
   * 检查是否支持类型
   */
  supports(type: AssetType): boolean {
    return this.loaders.has(type) || this.factories.has(type)
  }

  /**
   * 加载资产
   */
  async load(
    id: string,
    url: string,
    type: AssetType,
    options: AssetLoadOptions = {}
  ): Promise<Asset> {
    const loader = this.getLoader(type)
    if (loader) {
      return loader.load(id, url, type, options)
    }

    const factory = this.getFactory(type)
    if (factory) {
      return factory(id, url, options)
    }

    throw new Error(`不支持的资产类型: ${type}`)
  }
}
