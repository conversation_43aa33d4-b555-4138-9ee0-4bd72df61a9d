/**
 * DL-Engine 推荐系统使用示例
 * 展示如何使用推荐系统的各种功能
 */

import { Entity } from '@etherealengine/ecs'
import { RecommendationService } from '../RecommendationService'
import { ContentMetadata, UserLearningProfile } from '../ContentRecommender'
import { UserBehavior } from '../CollaborativeFiltering'

/**
 * 推荐系统使用示例
 */
export class RecommendationExample {
  private recommendationService: RecommendationService
  
  constructor() {
    // 初始化推荐服务
    this.recommendationService = new RecommendationService({
      enableContentRecommendation: true,
      enableCollaborativeFiltering: true,
      defaultLimit: 10,
      cacheTimeout: 300,
      enableLogging: true
    })
  }
  
  /**
   * 运行完整示例
   */
  async runExample(): Promise<void> {
    console.log('=== DL-Engine 推荐系统示例 ===\n')
    
    // 1. 初始化示例数据
    await this.initializeExampleData()
    
    // 2. 演示内容推荐
    await this.demonstrateContentRecommendation()
    
    // 3. 演示协同过滤推荐
    await this.demonstrateCollaborativeFiltering()
    
    // 4. 演示混合推荐
    await this.demonstrateMixedRecommendation()
    
    // 5. 演示用户行为记录
    await this.demonstrateUserBehaviorTracking()
    
    // 6. 演示推荐解释
    await this.demonstrateRecommendationExplanation()
    
    // 7. 显示统计信息
    await this.showStatistics()
    
    console.log('\n=== 示例完成 ===')
  }
  
  /**
   * 初始化示例数据
   */
  private async initializeExampleData(): Promise<void> {
    console.log('1. 初始化示例数据...')
    
    // 添加示例内容
    const sampleContents: ContentMetadata[] = [
      {
        id: 'content_001',
        title: 'JavaScript 基础教程',
        description: '学习 JavaScript 编程语言的基础知识',
        type: 'video',
        difficulty: 2,
        topics: ['javascript', 'programming', 'web'],
        skills: ['javascript', 'programming'],
        estimatedDuration: 120,
        prerequisites: [],
        learningObjectives: ['掌握 JavaScript 基本语法', '理解变量和函数'],
        qualityScore: 4.5,
        popularity: 85,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'content_002',
        title: 'React 入门指南',
        description: '学习 React 框架的基础概念和用法',
        type: 'interactive',
        difficulty: 3,
        topics: ['react', 'javascript', 'frontend'],
        skills: ['react', 'javascript', 'frontend'],
        estimatedDuration: 180,
        prerequisites: ['content_001'],
        learningObjectives: ['理解 React 组件', '掌握 JSX 语法'],
        qualityScore: 4.7,
        popularity: 92,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-20')
      },
      {
        id: 'content_003',
        title: 'CSS 样式设计',
        description: '学习 CSS 样式设计和布局技巧',
        type: 'text',
        difficulty: 2,
        topics: ['css', 'design', 'web'],
        skills: ['css', 'design'],
        estimatedDuration: 90,
        prerequisites: [],
        learningObjectives: ['掌握 CSS 选择器', '理解盒模型'],
        qualityScore: 4.2,
        popularity: 78,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-12')
      },
      {
        id: 'content_004',
        title: 'Node.js 后端开发',
        description: '学习使用 Node.js 进行后端开发',
        type: 'project',
        difficulty: 4,
        topics: ['nodejs', 'backend', 'javascript'],
        skills: ['nodejs', 'backend', 'javascript'],
        estimatedDuration: 240,
        prerequisites: ['content_001'],
        learningObjectives: ['掌握 Node.js 基础', '理解异步编程'],
        qualityScore: 4.6,
        popularity: 88,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-25')
      },
      {
        id: 'content_005',
        title: 'Python 数据分析',
        description: '使用 Python 进行数据分析和可视化',
        type: 'exercise',
        difficulty: 3,
        topics: ['python', 'data-analysis', 'visualization'],
        skills: ['python', 'data-analysis'],
        estimatedDuration: 200,
        prerequisites: [],
        learningObjectives: ['掌握 Pandas 库', '学会数据可视化'],
        qualityScore: 4.4,
        popularity: 82,
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-18')
      }
    ]
    
    // 添加内容到推荐系统
    for (const content of sampleContents) {
      this.recommendationService.addContent(content)
    }
    
    // 创建示例用户档案
    const sampleUsers: UserLearningProfile[] = [
      {
        userId: 'user_001' as any,
        skillLevels: {
          'javascript': 3,
          'programming': 3,
          'web': 2
        },
        preferences: {
          contentTypes: ['video', 'interactive'],
          difficultyRange: [2, 4],
          sessionDuration: 120,
          learningStyle: 'visual'
        },
        learningHistory: {
          completedContent: ['content_001'],
          inProgressContent: ['content_002'],
          bookmarkedContent: ['content_004'],
          skippedContent: []
        },
        learningGoals: ['掌握前端开发', '学习 React 框架'],
        lastActiveAt: new Date()
      },
      {
        userId: 'user_002' as any,
        skillLevels: {
          'python': 4,
          'data-analysis': 3,
          'programming': 4
        },
        preferences: {
          contentTypes: ['exercise', 'project'],
          difficultyRange: [3, 5],
          sessionDuration: 180,
          learningStyle: 'kinesthetic'
        },
        learningHistory: {
          completedContent: ['content_005'],
          inProgressContent: [],
          bookmarkedContent: ['content_001'],
          skippedContent: []
        },
        learningGoals: ['深入数据科学', '学习机器学习'],
        lastActiveAt: new Date()
      }
    ]
    
    // 更新用户档案
    for (const user of sampleUsers) {
      this.recommendationService.updateUserProfile(user)
    }
    
    console.log(`✓ 已添加 ${sampleContents.length} 个内容和 ${sampleUsers.length} 个用户档案\n`)
  }
  
  /**
   * 演示内容推荐
   */
  private async demonstrateContentRecommendation(): Promise<void> {
    console.log('2. 演示内容推荐...')
    
    try {
      const response = await this.recommendationService.getRecommendations({
        userId: 'user_001' as any,
        type: 'content',
        limit: 5,
        context: {
          currentContent: 'content_002',
          learningGoals: ['掌握前端开发'],
          difficultyPreference: 'adaptive'
        }
      })
      
      console.log(`✓ 为用户 user_001 生成了 ${response.total} 个内容推荐:`)
      response.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.title} (分数: ${rec.score.toFixed(2)}, 算法: ${rec.algorithm})`)
        console.log(`      理由: ${rec.reason}`)
      })
      
      console.log(`   处理时间: ${response.processingTime}ms`)
      console.log(`   算法使用: ${JSON.stringify(response.algorithmUsage)}`)
      
    } catch (error) {
      console.error('✗ 内容推荐失败:', error.message)
    }
    
    console.log()
  }
  
  /**
   * 演示协同过滤推荐
   */
  private async demonstrateCollaborativeFiltering(): Promise<void> {
    console.log('3. 演示协同过滤推荐...')
    
    // 先添加一些用户行为数据
    const sampleBehaviors: UserBehavior[] = [
      {
        userId: 'user_001' as any,
        itemId: 'content_001',
        actionType: 'complete',
        value: 5,
        timestamp: new Date('2024-01-20'),
        context: { deviceType: 'desktop', sessionId: 'session_001' }
      },
      {
        userId: 'user_001' as any,
        itemId: 'content_002',
        actionType: 'view',
        value: 3,
        timestamp: new Date('2024-01-21'),
        context: { deviceType: 'mobile', sessionId: 'session_002' }
      },
      {
        userId: 'user_002' as any,
        itemId: 'content_001',
        actionType: 'like',
        value: 4,
        timestamp: new Date('2024-01-19'),
        context: { deviceType: 'desktop', sessionId: 'session_003' }
      },
      {
        userId: 'user_002' as any,
        itemId: 'content_005',
        actionType: 'complete',
        value: 5,
        timestamp: new Date('2024-01-22'),
        context: { deviceType: 'tablet', sessionId: 'session_004' }
      }
    ]
    
    try {
      // 记录用户行为
      await this.recommendationService.recordUserBehaviors(sampleBehaviors)
      console.log(`✓ 已记录 ${sampleBehaviors.length} 个用户行为`)
      
      // 生成协同过滤推荐
      // 注意：由于示例数据较少，协同过滤可能无法生成有效推荐
      console.log('   协同过滤推荐需要更多用户行为数据才能生成有效结果')
      
    } catch (error) {
      console.error('✗ 协同过滤推荐失败:', error.message)
    }
    
    console.log()
  }
  
  /**
   * 演示混合推荐
   */
  private async demonstrateMixedRecommendation(): Promise<void> {
    console.log('4. 演示混合推荐...')
    
    try {
      const response = await this.recommendationService.getRecommendations({
        userId: 'user_002' as any,
        type: 'mixed',
        limit: 8,
        context: {
          learningGoals: ['深入数据科学'],
          timeConstraint: 180
        }
      })
      
      console.log(`✓ 为用户 user_002 生成了 ${response.total} 个混合推荐:`)
      response.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.title} (分数: ${rec.score.toFixed(2)}, 算法: ${rec.algorithm})`)
      })
      
      console.log(`   处理时间: ${response.processingTime}ms`)
      
    } catch (error) {
      console.error('✗ 混合推荐失败:', error.message)
    }
    
    console.log()
  }
  
  /**
   * 演示用户行为记录
   */
  private async demonstrateUserBehaviorTracking(): Promise<void> {
    console.log('5. 演示用户行为记录...')
    
    try {
      // 记录新的用户行为
      const newBehavior: UserBehavior = {
        userId: 'user_001' as any,
        itemId: 'content_003',
        actionType: 'bookmark',
        value: 4,
        timestamp: new Date(),
        context: {
          deviceType: 'mobile',
          sessionId: 'session_005',
          referrer: 'recommendation_list'
        }
      }
      
      await this.recommendationService.recordUserBehavior(newBehavior)
      console.log('✓ 已记录用户书签行为')
      
      // 记录完成行为
      const completeBehavior: UserBehavior = {
        userId: 'user_001' as any,
        itemId: 'content_002',
        actionType: 'complete',
        value: 5,
        timestamp: new Date(),
        context: {
          deviceType: 'desktop',
          sessionId: 'session_006'
        }
      }
      
      await this.recommendationService.recordUserBehavior(completeBehavior)
      console.log('✓ 已记录用户完成行为')
      
    } catch (error) {
      console.error('✗ 用户行为记录失败:', error.message)
    }
    
    console.log()
  }
  
  /**
   * 演示推荐解释
   */
  private async demonstrateRecommendationExplanation(): Promise<void> {
    console.log('6. 演示推荐解释...')
    
    try {
      const explanation = await this.recommendationService.explainRecommendation(
        'user_001' as any,
        'content_004'
      )
      
      console.log('✓ 推荐解释:')
      console.log(`   内容: content_004`)
      console.log(`   解释: ${explanation}`)
      
    } catch (error) {
      console.error('✗ 推荐解释失败:', error.message)
    }
    
    console.log()
  }
  
  /**
   * 显示统计信息
   */
  private async showStatistics(): Promise<void> {
    console.log('7. 推荐系统统计信息...')
    
    try {
      const stats = this.recommendationService.getStatistics()
      
      console.log('✓ 统计信息:')
      console.log(`   缓存大小: ${stats.cacheSize}`)
      console.log(`   总请求数: ${stats.totalRequests}`)
      
      if (stats.collaborativeFiltering) {
        console.log('   协同过滤统计:')
        console.log(`     总用户数: ${stats.collaborativeFiltering.totalUsers}`)
        console.log(`     总项目数: ${stats.collaborativeFiltering.totalItems}`)
        console.log(`     总行为数: ${stats.collaborativeFiltering.totalBehaviors}`)
        console.log(`     数据稀疏度: ${(stats.collaborativeFiltering.sparsity * 100).toFixed(2)}%`)
      }
      
    } catch (error) {
      console.error('✗ 获取统计信息失败:', error.message)
    }
    
    console.log()
  }
}

/**
 * 运行示例
 */
export async function runRecommendationExample(): Promise<void> {
  const example = new RecommendationExample()
  await example.runExample()
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  runRecommendationExample().catch(console.error)
}
