/**
 * DL-Engine 空间索引系统
 * 提供高效的空间数据结构和查询算法
 */

import { Entity } from '@dl-engine/engine-ecs'
import { PhysicsVector3 } from '../types/PhysicsTypes'
import { Collider } from '@dimforge/rapier3d'

/**
 * 空间索引项
 */
export interface SpatialIndexItem {
  /** 实体ID */
  entity: Entity
  
  /** 碰撞体 */
  collider: Collider
  
  /** 位置 */
  position: PhysicsVector3
  
  /** 包围盒最小点 */
  aabbMin: PhysicsVector3
  
  /** 包围盒最大点 */
  aabbMax: PhysicsVector3
  
  /** 用户数据 */
  userData?: any
}

/**
 * 空间索引接口
 */
export interface ISpatialIndex {
  /** 插入项目 */
  insert(item: SpatialIndexItem): void
  
  /** 移除项目 */
  remove(entity: Entity): boolean
  
  /** 更新项目 */
  update(entity: Entity, newPosition: PhysicsVector3, newAABB?: { min: PhysicsVector3; max: PhysicsVector3 }): boolean
  
  /** 查询范围内的项目 */
  queryRange(min: PhysicsVector3, max: PhysicsVector3): SpatialIndexItem[]
  
  /** 查询球形范围内的项目 */
  querySphere(center: PhysicsVector3, radius: number): SpatialIndexItem[]
  
  /** 查询最近的N个项目 */
  queryNearest(position: PhysicsVector3, count: number, maxDistance?: number): SpatialIndexItem[]
  
  /** 清空索引 */
  clear(): void
  
  /** 获取项目数量 */
  size(): number
}

/**
 * 简单的网格空间索引
 */
export class GridSpatialIndex implements ISpatialIndex {
  private cellSize: number
  private grid: Map<string, SpatialIndexItem[]>
  private items: Map<Entity, SpatialIndexItem>
  
  constructor(cellSize: number = 10) {
    this.cellSize = cellSize
    this.grid = new Map()
    this.items = new Map()
  }
  
  /**
   * 获取网格坐标
   */
  private getGridKey(position: PhysicsVector3): string {
    const x = Math.floor(position.x / this.cellSize)
    const y = Math.floor(position.y / this.cellSize)
    const z = Math.floor(position.z / this.cellSize)
    return `${x},${y},${z}`
  }
  
  /**
   * 获取AABB覆盖的所有网格键
   */
  private getGridKeys(min: PhysicsVector3, max: PhysicsVector3): string[] {
    const keys: string[] = []
    const minX = Math.floor(min.x / this.cellSize)
    const minY = Math.floor(min.y / this.cellSize)
    const minZ = Math.floor(min.z / this.cellSize)
    const maxX = Math.floor(max.x / this.cellSize)
    const maxY = Math.floor(max.y / this.cellSize)
    const maxZ = Math.floor(max.z / this.cellSize)
    
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        for (let z = minZ; z <= maxZ; z++) {
          keys.push(`${x},${y},${z}`)
        }
      }
    }
    
    return keys
  }
  
  insert(item: SpatialIndexItem): void {
    // 移除旧的项目（如果存在）
    this.remove(item.entity)
    
    // 添加到项目映射
    this.items.set(item.entity, item)
    
    // 添加到网格
    const keys = this.getGridKeys(item.aabbMin, item.aabbMax)
    for (const key of keys) {
      if (!this.grid.has(key)) {
        this.grid.set(key, [])
      }
      this.grid.get(key)!.push(item)
    }
  }
  
  remove(entity: Entity): boolean {
    const item = this.items.get(entity)
    if (!item) return false
    
    // 从项目映射中移除
    this.items.delete(entity)
    
    // 从网格中移除
    const keys = this.getGridKeys(item.aabbMin, item.aabbMax)
    for (const key of keys) {
      const cell = this.grid.get(key)
      if (cell) {
        const index = cell.findIndex(i => i.entity === entity)
        if (index !== -1) {
          cell.splice(index, 1)
        }
        if (cell.length === 0) {
          this.grid.delete(key)
        }
      }
    }
    
    return true
  }
  
  update(entity: Entity, newPosition: PhysicsVector3, newAABB?: { min: PhysicsVector3; max: PhysicsVector3 }): boolean {
    const item = this.items.get(entity)
    if (!item) return false
    
    // 更新位置和AABB
    item.position = newPosition
    if (newAABB) {
      item.aabbMin = newAABB.min
      item.aabbMax = newAABB.max
    }
    
    // 重新插入
    this.insert(item)
    return true
  }
  
  queryRange(min: PhysicsVector3, max: PhysicsVector3): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    const seen = new Set<Entity>()
    
    const keys = this.getGridKeys(min, max)
    for (const key of keys) {
      const cell = this.grid.get(key)
      if (cell) {
        for (const item of cell) {
          if (!seen.has(item.entity)) {
            // 检查AABB重叠
            if (this.aabbOverlaps(item.aabbMin, item.aabbMax, min, max)) {
              results.push(item)
              seen.add(item.entity)
            }
          }
        }
      }
    }
    
    return results
  }
  
  querySphere(center: PhysicsVector3, radius: number): SpatialIndexItem[] {
    const min = {
      x: center.x - radius,
      y: center.y - radius,
      z: center.z - radius
    }
    const max = {
      x: center.x + radius,
      y: center.y + radius,
      z: center.z + radius
    }
    
    const candidates = this.queryRange(min, max)
    const results: SpatialIndexItem[] = []
    
    for (const item of candidates) {
      const distance = this.distance(center, item.position)
      if (distance <= radius) {
        results.push(item)
      }
    }
    
    return results
  }
  
  queryNearest(position: PhysicsVector3, count: number, maxDistance?: number): SpatialIndexItem[] {
    const allItems = Array.from(this.items.values())
    
    // 计算距离并排序
    const itemsWithDistance = allItems.map(item => ({
      item,
      distance: this.distance(position, item.position)
    })).filter(({ distance }) => !maxDistance || distance <= maxDistance)
    
    itemsWithDistance.sort((a, b) => a.distance - b.distance)
    
    return itemsWithDistance.slice(0, count).map(({ item }) => item)
  }
  
  clear(): void {
    this.grid.clear()
    this.items.clear()
  }
  
  size(): number {
    return this.items.size
  }
  
  /**
   * 检查两个AABB是否重叠
   */
  private aabbOverlaps(min1: PhysicsVector3, max1: PhysicsVector3, min2: PhysicsVector3, max2: PhysicsVector3): boolean {
    return min1.x <= max2.x && max1.x >= min2.x &&
           min1.y <= max2.y && max1.y >= min2.y &&
           min1.z <= max2.z && max1.z >= min2.z
  }
  
  /**
   * 计算两点间距离
   */
  private distance(a: PhysicsVector3, b: PhysicsVector3): number {
    const dx = a.x - b.x
    const dy = a.y - b.y
    const dz = a.z - b.z
    return Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
}

/**
 * 全局空间索引实例
 */
export const globalSpatialIndex = new GridSpatialIndex(10)
