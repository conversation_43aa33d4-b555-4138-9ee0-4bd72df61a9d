import * as React from 'react';
import type * as CSS from 'csstype';
import type { HashPriority } from '../StyleContext';
import type Cache from '../Cache';
import type { Theme } from '..';
import type Keyframes from '../Keyframes';
declare const SKIP_CHECK = "_skip_check_";
export declare type CSSProperties = Omit<CSS.PropertiesFallback<number | string>, 'animationName'> & {
    animationName?: CSS.PropertiesFallback<number | string>['animationName'] | Keyframes;
};
export declare type CSSPropertiesWithMultiValues = {
    [K in keyof CSSProperties]: CSSProperties[K] | Extract<CSSProperties[K], string>[] | {
        [SKIP_CHECK]: boolean;
        value: CSSProperties[K] | Extract<CSSProperties[K], string>[];
    };
};
export declare type CSSPseudos = {
    [K in CSS.Pseudos]?: CSSObject;
};
declare type ArrayCSSInterpolation = CSSInterpolation[];
export declare type InterpolationPrimitive = null | undefined | boolean | number | string | CSSObject;
export declare type CSSInterpolation = InterpolationPrimitive | ArrayCSSInterpolation | Keyframes;
export declare type CSSOthersObject = Record<string, CSSInterpolation>;
export interface CSSObject extends CSSPropertiesWithMultiValues, CSSPseudos, CSSOthersObject {
}
export declare function normalizeStyle(styleStr: string): string;
export declare let animationStatistics: Record<string, boolean>;
export interface ParseConfig {
    hashId?: string;
    hashPriority?: HashPriority;
    layer?: string;
    path?: string;
}
export interface ParseInfo {
    root?: boolean;
    injectHash?: boolean;
}
export declare const parseStyle: (interpolation: CSSInterpolation, config?: ParseConfig, { root, injectHash }?: ParseInfo) => string;
/**
 * Register a style to the global style sheet.
 */
export default function useStyleRegister(info: {
    theme: Theme<any, any>;
    token: any;
    path: string[];
    hashId?: string;
    layer?: string;
}, styleFn: () => CSSInterpolation): (node: React.ReactElement) => JSX.Element;
export declare function extractStyle(cache: Cache): string;
export {};
