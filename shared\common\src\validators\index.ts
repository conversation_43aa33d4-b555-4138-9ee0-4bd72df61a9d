/**
 * DL-Engine 验证器模块
 * 提供各种数据验证功能
 */

/**
 * 验证器函数类型
 */
export type Validator<T> = (value: unknown) => value is T

/**
 * 验证结果类型
 */
export interface ValidationResult {
  valid: boolean
  errors: string[]
}

/**
 * 字符串验证器
 */
export const StringValidators = {
  /**
   * 验证是否为字符串
   */
  isString: (value: unknown): value is string => {
    return typeof value === 'string'
  },

  /**
   * 验证字符串长度
   */
  hasLength: (min?: number, max?: number) => (value: unknown): value is string => {
    if (typeof value !== 'string') return false
    if (min !== undefined && value.length < min) return false
    if (max !== undefined && value.length > max) return false
    return true
  },

  /**
   * 验证邮箱格式
   */
  isEmail: (value: unknown): value is string => {
    if (typeof value !== 'string') return false
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(value)
  },

  /**
   * 验证URL格式
   */
  isUrl: (value: unknown): value is string => {
    if (typeof value !== 'string') return false
    try {
      new URL(value)
      return true
    } catch {
      return false
    }
  },

  /**
   * 验证UUID格式
   */
  isUUID: (value: unknown): value is string => {
    if (typeof value !== 'string') return false
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(value)
  }
}

/**
 * 数字验证器
 */
export const NumberValidators = {
  /**
   * 验证是否为数字
   */
  isNumber: (value: unknown): value is number => {
    return typeof value === 'number' && !isNaN(value)
  },

  /**
   * 验证是否为整数
   */
  isInteger: (value: unknown): value is number => {
    return typeof value === 'number' && Number.isInteger(value)
  },

  /**
   * 验证数字范围
   */
  inRange: (min?: number, max?: number) => (value: unknown): value is number => {
    if (typeof value !== 'number' || isNaN(value)) return false
    if (min !== undefined && value < min) return false
    if (max !== undefined && value > max) return false
    return true
  },

  /**
   * 验证是否为正数
   */
  isPositive: (value: unknown): value is number => {
    return typeof value === 'number' && value > 0
  },

  /**
   * 验证是否为非负数
   */
  isNonNegative: (value: unknown): value is number => {
    return typeof value === 'number' && value >= 0
  }
}

/**
 * 数组验证器
 */
export const ArrayValidators = {
  /**
   * 验证是否为数组
   */
  isArray: <T>(value: unknown): value is T[] => {
    return Array.isArray(value)
  },

  /**
   * 验证数组长度
   */
  hasLength: <T>(min?: number, max?: number) => (value: unknown): value is T[] => {
    if (!Array.isArray(value)) return false
    if (min !== undefined && value.length < min) return false
    if (max !== undefined && value.length > max) return false
    return true
  },

  /**
   * 验证数组元素
   */
  hasValidElements: <T>(elementValidator: Validator<T>) => (value: unknown): value is T[] => {
    if (!Array.isArray(value)) return false
    return value.every(elementValidator)
  },

  /**
   * 验证数组非空
   */
  isNotEmpty: <T>(value: unknown): value is T[] => {
    return Array.isArray(value) && value.length > 0
  }
}

/**
 * 对象验证器
 */
export const ObjectValidators = {
  /**
   * 验证是否为对象
   */
  isObject: (value: unknown): value is Record<string, unknown> => {
    return typeof value === 'object' && value !== null && !Array.isArray(value)
  },

  /**
   * 验证对象属性
   */
  hasProperties: <T extends Record<string, unknown>>(
    schema: { [K in keyof T]: Validator<T[K]> }
  ) => (value: unknown): value is T => {
    if (!ObjectValidators.isObject(value)) return false
    
    for (const key in schema) {
      if (!(key in value) || !schema[key](value[key])) {
        return false
      }
    }
    
    return true
  },

  /**
   * 验证对象可选属性
   */
  hasOptionalProperties: <T extends Record<string, unknown>>(
    schema: { [K in keyof T]?: Validator<T[K]> }
  ) => (value: unknown): value is Partial<T> => {
    if (!ObjectValidators.isObject(value)) return false
    
    for (const key in schema) {
      if (key in value && schema[key] && !schema[key]!(value[key])) {
        return false
      }
    }
    
    return true
  }
}

/**
 * 组合验证器
 */
export const CombinedValidators = {
  /**
   * 或验证器
   */
  or: <T>(...validators: Validator<T>[]) => (value: unknown): value is T => {
    return validators.some(validator => validator(value))
  },

  /**
   * 与验证器
   */
  and: <T>(...validators: Validator<T>[]) => (value: unknown): value is T => {
    return validators.every(validator => validator(value))
  },

  /**
   * 非验证器
   */
  not: <T>(validator: Validator<T>) => (value: unknown): boolean => {
    return !validator(value)
  },

  /**
   * 可选验证器
   */
  optional: <T>(validator: Validator<T>) => (value: unknown): value is T | undefined => {
    return value === undefined || validator(value)
  }
}

/**
 * 验证工具函数
 */
export const ValidationUtils = {
  /**
   * 验证并收集错误
   */
  validate: <T>(value: unknown, validator: Validator<T>, fieldName?: string): ValidationResult => {
    const valid = validator(value)
    const errors = valid ? [] : [`${fieldName || 'Value'} is invalid`]
    
    return { valid, errors }
  },

  /**
   * 验证对象的多个字段
   */
  validateObject: <T extends Record<string, unknown>>(
    obj: unknown,
    schema: { [K in keyof T]: { validator: Validator<T[K]>; required?: boolean } }
  ): ValidationResult => {
    const errors: string[] = []
    
    if (!ObjectValidators.isObject(obj)) {
      return { valid: false, errors: ['Value must be an object'] }
    }
    
    for (const key in schema) {
      const field = schema[key]
      const value = obj[key]
      
      if (field.required !== false && value === undefined) {
        errors.push(`Field '${key}' is required`)
        continue
      }
      
      if (value !== undefined && !field.validator(value)) {
        errors.push(`Field '${key}' is invalid`)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  },

  /**
   * 创建自定义验证器
   */
  custom: <T>(predicate: (value: unknown) => boolean): Validator<T> => {
    return (value: unknown): value is T => predicate(value)
  }
}

/**
 * 常用验证器实例
 */
export const CommonValidators = {
  /** 非空字符串 */
  nonEmptyString: StringValidators.hasLength(1),
  
  /** 正整数 */
  positiveInteger: CombinedValidators.and(
    NumberValidators.isInteger,
    NumberValidators.isPositive
  ),
  
  /** 非负整数 */
  nonNegativeInteger: CombinedValidators.and(
    NumberValidators.isInteger,
    NumberValidators.isNonNegative
  ),
  
  /** 百分比（0-100） */
  percentage: NumberValidators.inRange(0, 100),
  
  /** 非空数组 */
  nonEmptyArray: ArrayValidators.isNotEmpty,
  
  /** 布尔值 */
  boolean: (value: unknown): value is boolean => typeof value === 'boolean',
  
  /** 日期 */
  date: (value: unknown): value is Date => value instanceof Date && !isNaN(value.getTime()),
  
  /** 函数 */
  function: (value: unknown): value is Function => typeof value === 'function'
}

/**
 * 教育相关验证器
 */
export const EducationValidators = {
  /**
   * 验证学生年龄
   */
  studentAge: NumberValidators.inRange(3, 25),
  
  /**
   * 验证课程时长（分钟）
   */
  courseDuration: NumberValidators.inRange(1, 480), // 1分钟到8小时
  
  /**
   * 验证班级大小
   */
  classSize: NumberValidators.inRange(1, 100),
  
  /**
   * 验证成绩（0-100）
   */
  grade: NumberValidators.inRange(0, 100),
  
  /**
   * 验证学期
   */
  semester: CombinedValidators.or(
    ValidationUtils.custom<string>(value => 
      typeof value === 'string' && ['spring', 'summer', 'fall', 'winter'].includes(value.toLowerCase())
    )
  )
}
