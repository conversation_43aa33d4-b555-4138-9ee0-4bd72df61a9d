/**
 * DL-Engine 材质管理器
 * 管理材质创建、缓存、优化和动态切换
 */

import {
  Material,
  MeshBasicMaterial,
  MeshLambertMaterial,
  MeshPhongMaterial,
  MeshStandardMaterial,
  MeshPhysicalMaterial,
  ShaderMaterial,
  Texture,
  Color,
  Vector2,
  Vector3,
  Vector4,
  Wrapping,
  MagnificationTextureFilter,
  MinificationTextureFilter
} from 'three'

import { Entity } from '@dl-engine/engine-ecs'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * 材质类型
 */
export type MaterialType = 
  | 'basic'
  | 'lambert'
  | 'phong'
  | 'standard'
  | 'physical'
  | 'shader'
  | 'custom'

/**
 * 材质参数
 */
export interface MaterialParams {
  /** 基础颜色 */
  color?: Color | string | number
  
  /** 透明度 */
  opacity?: number
  
  /** 是否透明 */
  transparent?: boolean
  
  /** 金属度 */
  metalness?: number
  
  /** 粗糙度 */
  roughness?: number
  
  /** 法线强度 */
  normalScale?: Vector2
  
  /** 环境遮挡强度 */
  aoMapIntensity?: number
  
  /** 发光强度 */
  emissiveIntensity?: number
  
  /** 发光颜色 */
  emissive?: Color | string | number
  
  /** 环境贴图强度 */
  envMapIntensity?: number
  
  /** 折射率 */
  ior?: number
  
  /** 厚度 */
  thickness?: number
  
  /** 传输 */
  transmission?: number
  
  /** 清漆 */
  clearcoat?: number
  
  /** 清漆粗糙度 */
  clearcoatRoughness?: number
  
  /** 各向异性 */
  anisotropy?: number
  
  /** 各向异性旋转 */
  anisotropyRotation?: number
  
  /** 光泽 */
  sheen?: number
  
  /** 光泽颜色 */
  sheenColor?: Color | string | number
  
  /** 光泽粗糙度 */
  sheenRoughness?: number
  
  /** 虹彩 */
  iridescence?: number
  
  /** 虹彩IOR */
  iridescenceIOR?: number
  
  /** 虹彩厚度范围 */
  iridescenceThicknessRange?: [number, number]
}

/**
 * 纹理配置
 */
export interface TextureConfig {
  /** 纹理路径 */
  url: string

  /** 纹理重复 */
  repeat?: Vector2

  /** 纹理偏移 */
  offset?: Vector2

  /** 纹理旋转 */
  rotation?: number

  /** 纹理中心 */
  center?: Vector2

  /** 是否翻转Y */
  flipY?: boolean

  /** 包装模式 */
  wrapS?: Wrapping
  wrapT?: Wrapping

  /** 过滤模式 */
  magFilter?: MagnificationTextureFilter
  minFilter?: MinificationTextureFilter

  /** 各向异性过滤 */
  anisotropy?: number
}

/**
 * 材质定义
 */
export interface MaterialDefinition {
  /** 材质ID */
  id: string
  
  /** 材质名称 */
  name: string
  
  /** 材质类型 */
  type: MaterialType
  
  /** 材质参数 */
  params: MaterialParams
  
  /** 纹理映射 */
  textures: {
    map?: TextureConfig
    normalMap?: TextureConfig
    roughnessMap?: TextureConfig
    metalnessMap?: TextureConfig
    aoMap?: TextureConfig
    emissiveMap?: TextureConfig
    envMap?: TextureConfig
    lightMap?: TextureConfig
    bumpMap?: TextureConfig
    displacementMap?: TextureConfig
    alphaMap?: TextureConfig
    clearcoatMap?: TextureConfig
    clearcoatNormalMap?: TextureConfig
    clearcoatRoughnessMap?: TextureConfig
    sheenColorMap?: TextureConfig
    sheenRoughnessMap?: TextureConfig
    transmissionMap?: TextureConfig
    thicknessMap?: TextureConfig
    iridescenceMap?: TextureConfig
    iridescenceThicknessMap?: TextureConfig
    anisotropyMap?: TextureConfig
  }
  
  /** 着色器代码（仅用于shader类型） */
  shaderCode?: {
    vertexShader: string
    fragmentShader: string
    uniforms?: Record<string, any>
  }
  
  /** 材质标签 */
  tags: string[]
  
  /** 创建时间 */
  createdAt: number
  
  /** 更新时间 */
  updatedAt: number
}

/**
 * 材质实例
 */
export interface MaterialInstance {
  /** 实例ID */
  id: string
  
  /** 材质定义ID */
  definitionId: string
  
  /** Three.js材质对象 */
  material: Material
  
  /** 关联的实体 */
  entities: Set<Entity>
  
  /** 引用计数 */
  refCount: number
  
  /** 是否需要更新 */
  needsUpdate: boolean
  
  /** 最后使用时间 */
  lastUsed: number
}

/**
 * 材质管理器状态
 */
export interface MaterialManagerState {
  /** 材质定义 */
  definitions: Record<string, MaterialDefinition>

  /** 材质实例 */
  instances: Record<string, MaterialInstance>

  /** 纹理缓存 */
  textureCache: Record<string, Texture>

  /** 实体材质映射 */
  entityMaterials: Record<Entity, string[]>

  /** 配置 */
  config: {
    /** 是否启用材质缓存 */
    enableCaching: boolean

    /** 缓存大小限制 */
    maxCacheSize: number

    /** 自动清理间隔（毫秒） */
    cleanupInterval: number

    /** 未使用材质清理阈值（毫秒） */
    unusedThreshold: number
  }

  /** 统计信息 */
  stats: {
    totalDefinitions: number
    totalInstances: number
    cacheHitRate: number
    memoryUsage: number
  }
}

/**
 * 材质管理器状态定义
 */
export const MaterialManagerState = defineState({
  name: 'DLEngine.MaterialManager',
  initial: (): MaterialManagerState => ({
    definitions: {},
    instances: {},
    textureCache: {},
    entityMaterials: {},
    config: {
      enableCaching: true,
      maxCacheSize: 1000,
      cleanupInterval: 60000, // 1分钟
      unusedThreshold: 300000 // 5分钟
    },
    stats: {
      totalDefinitions: 0,
      totalInstances: 0,
      cacheHitRate: 0,
      memoryUsage: 0
    }
  })
})

/**
 * 材质管理器
 */
export class MaterialManager {
  private static instance: MaterialManager | null = null
  private cleanupTimer: number | null = null

  // 内部存储
  private definitions: Map<string, MaterialDefinition> = new Map()
  private instances: Map<string, MaterialInstance> = new Map()
  private textureCache: Map<string, Texture> = new Map()
  private entityMaterials: Map<Entity, Set<string>> = new Map()

  /**
   * 获取单例实例
   */
  static getInstance(): MaterialManager {
    if (!MaterialManager.instance) {
      MaterialManager.instance = new MaterialManager()
    }
    return MaterialManager.instance
  }
  
  constructor() {
    this.startCleanupTimer()
  }
  
  /**
   * 创建材质定义
   */
  createDefinition(definition: Omit<MaterialDefinition, 'id' | 'createdAt' | 'updatedAt'>): string {
    const id = this.generateDefinitionId()
    const fullDefinition: MaterialDefinition = {
      ...definition,
      id,
      createdAt: Date.now(),
      updatedAt: Date.now()
    }

    // 添加到内部存储
    this.definitions.set(id, fullDefinition)

    // 更新状态
    const state = getMutableState(MaterialManagerState)
    state.definitions[id].set(fullDefinition)
    state.stats.totalDefinitions.set(state.stats.totalDefinitions.value + 1)

    console.log(`Material definition created: ${id}`)
    return id
  }
  
  /**
   * 获取或创建材质实例
   */
  async getInstance(definitionId: string, entity?: Entity): Promise<MaterialInstance | null> {
    const definition = this.definitions.get(definitionId)
    if (!definition) {
      console.warn(`Material definition not found: ${definitionId}`)
      return null
    }
    
    // 检查是否已有实例
    const existingInstance = this.findExistingInstance(definitionId)
    if (existingInstance) {
      existingInstance.refCount++
      existingInstance.lastUsed = Date.now()
      
      if (entity) {
        existingInstance.entities.add(entity)
        this.addEntityMaterial(entity, existingInstance.id)
      }
      
      return existingInstance
    }
    
    // 创建新实例
    try {
      const material = await this.createMaterial(definition)
      const instance: MaterialInstance = {
        id: this.generateInstanceId(),
        definitionId,
        material,
        entities: entity ? new Set([entity]) : new Set(),
        refCount: 1,
        needsUpdate: false,
        lastUsed: Date.now()
      }
      
      // 添加到内部存储
      this.instances.set(instance.id, instance)

      // 更新状态
      const state = getMutableState(MaterialManagerState)
      state.instances[instance.id].set(instance)
      state.stats.totalInstances.set(state.stats.totalInstances.value + 1)

      if (entity) {
        this.addEntityMaterial(entity, instance.id)
      }
      
      console.log(`Material instance created: ${instance.id}`)
      return instance
      
    } catch (error) {
      console.error('Failed to create material instance:', error)
      return null
    }
  }
  
  /**
   * 创建Three.js材质
   */
  private async createMaterial(definition: MaterialDefinition): Promise<Material> {
    let material: Material
    
    // 加载纹理
    const textures = await this.loadTextures(definition.textures)
    
    // 根据类型创建材质
    switch (definition.type) {
      case 'basic':
        material = new MeshBasicMaterial()
        break
        
      case 'lambert':
        material = new MeshLambertMaterial()
        break
        
      case 'phong':
        material = new MeshPhongMaterial()
        break
        
      case 'standard':
        material = new MeshStandardMaterial()
        break
        
      case 'physical':
        material = new MeshPhysicalMaterial()
        break
        
      case 'shader':
        if (!definition.shaderCode) {
          throw new Error('Shader code required for shader material')
        }
        material = new ShaderMaterial({
          vertexShader: definition.shaderCode.vertexShader,
          fragmentShader: definition.shaderCode.fragmentShader,
          uniforms: definition.shaderCode.uniforms || {}
        })
        break
        
      default:
        throw new Error(`Unsupported material type: ${definition.type}`)
    }
    
    // 应用参数
    this.applyMaterialParams(material, definition.params)
    
    // 应用纹理
    this.applyTextures(material, textures)
    
    // 设置材质名称
    material.name = definition.name
    
    return material
  }
  
  /**
   * 应用材质参数
   */
  private applyMaterialParams(material: Material, params: MaterialParams): void {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && key in material) {
        try {
          // 特殊处理颜色类型
          if (key === 'color' || key === 'emissive' || key === 'sheenColor') {
            if (material[key as keyof Material] instanceof Color) {
              (material[key as keyof Material] as Color).set(value as any)
            }
          } else {
            (material as any)[key] = value
          }
        } catch (error) {
          console.warn(`Failed to set material parameter ${key}:`, error)
        }
      }
    })
  }
  
  /**
   * 加载纹理
   */
  private async loadTextures(textureConfigs: MaterialDefinition['textures']): Promise<Record<string, Texture>> {
    const textures: Record<string, Texture> = {}
    const state = getState(MaterialManagerState)
    
    for (const [key, config] of Object.entries(textureConfigs)) {
      if (!config) continue
      
      try {
        // 检查缓存
        let texture = this.textureCache.get(config.url)

        if (!texture) {
          // 加载新纹理
          texture = await this.loadTexture(config)

          // 添加到缓存
          if (state.config.enableCaching) {
            this.textureCache.set(config.url, texture)
            getMutableState(MaterialManagerState).textureCache[config.url].set(texture)
          }
        }
        
        textures[key] = texture
        
      } catch (error) {
        console.warn(`Failed to load texture ${key}:`, error)
      }
    }
    
    return textures
  }
  
  /**
   * 加载单个纹理
   */
  private async loadTexture(config: TextureConfig): Promise<Texture> {
    return new Promise((resolve, reject) => {
      const loader = new (window as any).THREE.TextureLoader()
      
      loader.load(
        config.url,
        (texture: Texture) => {
          // 应用纹理配置
          if (config.repeat) texture.repeat.copy(config.repeat)
          if (config.offset) texture.offset.copy(config.offset)
          if (config.rotation !== undefined) texture.rotation = config.rotation
          if (config.center) texture.center.copy(config.center)
          if (config.flipY !== undefined) texture.flipY = config.flipY
          if (config.wrapS !== undefined) texture.wrapS = config.wrapS
          if (config.wrapT !== undefined) texture.wrapT = config.wrapT
          if (config.magFilter !== undefined) texture.magFilter = config.magFilter
          if (config.minFilter !== undefined) texture.minFilter = config.minFilter
          if (config.anisotropy !== undefined) texture.anisotropy = config.anisotropy
          
          texture.needsUpdate = true
          resolve(texture)
        },
        undefined,
        (error: any) => {
          reject(new Error(`Failed to load texture: ${config.url}`))
        }
      )
    })
  }
  
  /**
   * 应用纹理到材质
   */
  private applyTextures(material: Material, textures: Record<string, Texture>): void {
    Object.entries(textures).forEach(([key, texture]) => {
      if (key in material) {
        (material as any)[key] = texture
      }
    })
    
    material.needsUpdate = true
  }
  
  /**
   * 释放材质实例
   */
  releaseInstance(instanceId: string, entity?: Entity): boolean {
    const instance = this.instances.get(instanceId)
    if (!instance) {
      return false
    }

    // 减少引用计数
    instance.refCount = Math.max(0, instance.refCount - 1)

    // 从实体集合中移除
    if (entity) {
      instance.entities.delete(entity)
      this.removeEntityMaterial(entity, instanceId)
    }
    
    // 如果引用计数为0，标记为可清理
    if (instance.refCount === 0) {
      instance.lastUsed = Date.now()
    }
    
    return true
  }
  
  /**
   * 更新材质参数
   */
  updateMaterialParams(instanceId: string, params: Partial<MaterialParams>): boolean {
    const instance = this.instances.get(instanceId)
    if (!instance) {
      return false
    }
    
    this.applyMaterialParams(instance.material, params)
    instance.needsUpdate = true
    
    return true
  }
  
  /**
   * 查找现有实例
   */
  private findExistingInstance(definitionId: string): MaterialInstance | null {
    // 使用内部存储查找
    const instances = Array.from(this.instances.values())
    for (const instance of instances) {
      if (instance.definitionId === definitionId) {
        return instance
      }
    }

    return null
  }
  
  /**
   * 添加实体材质映射
   */
  private addEntityMaterial(entity: Entity, instanceId: string): void {
    // 更新内部存储
    if (!this.entityMaterials.has(entity)) {
      this.entityMaterials.set(entity, new Set())
    }
    this.entityMaterials.get(entity)!.add(instanceId)

    // 更新状态
    const state = getMutableState(MaterialManagerState)
    const currentMaterials = state.entityMaterials[entity]?.value || []
    if (!currentMaterials.includes(instanceId)) {
      state.entityMaterials[entity].set([...currentMaterials, instanceId])
    }
  }
  
  /**
   * 移除实体材质映射
   */
  private removeEntityMaterial(entity: Entity, instanceId: string): void {
    // 更新内部存储
    const entityMaterials = this.entityMaterials.get(entity)
    if (entityMaterials) {
      entityMaterials.delete(instanceId)
      if (entityMaterials.size === 0) {
        this.entityMaterials.delete(entity)
      }
    }

    // 更新状态
    const state = getMutableState(MaterialManagerState)
    const currentMaterials = state.entityMaterials[entity]?.value || []
    const updatedMaterials = currentMaterials.filter(id => id !== instanceId)

    if (updatedMaterials.length === 0) {
      // 设置为空数组而不是删除
      state.entityMaterials[entity].set([])
    } else {
      state.entityMaterials[entity].set(updatedMaterials)
    }
  }
  
  /**
   * 开始清理定时器
   */
  private startCleanupTimer(): void {
    const config = getState(MaterialManagerState).config
    
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, config.cleanupInterval)
  }
  
  /**
   * 清理未使用的材质
   */
  private cleanup(): void {
    const state = getState(MaterialManagerState)
    const now = Date.now()
    const threshold = state.config.unusedThreshold
    
    // 清理未使用的材质实例
    const instances = Array.from(this.instances.entries())
    for (const [id, instance] of instances) {
      if (instance.refCount === 0 && (now - instance.lastUsed) > threshold) {
        // 销毁材质
        instance.material.dispose()

        // 从内部存储中移除
        this.instances.delete(id)

        // 从状态中移除
        const mutableState = getMutableState(MaterialManagerState)
        mutableState.instances[id].set(undefined as any)
        mutableState.stats.totalInstances.set(Math.max(0, mutableState.stats.totalInstances.value - 1))

        console.log(`Cleaned up unused material instance: ${id}`)
      }
    }
    
    // 清理未使用的纹理
    // TODO: 实现纹理引用计数和清理
  }
  
  /**
   * 生成定义ID
   */
  private generateDefinitionId(): string {
    return `mat_def_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 生成实例ID
   */
  private generateInstanceId(): string {
    return `mat_inst_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    
    // 清理所有材质实例
    const instances = Array.from(this.instances.values())
    for (const instance of instances) {
      instance.material.dispose()
    }
    this.instances.clear()

    // 清理所有纹理
    const textures = Array.from(this.textureCache.values())
    for (const texture of textures) {
      texture.dispose()
    }
    this.textureCache.clear()

    // 清理实体映射
    this.entityMaterials.clear()
    this.definitions.clear()
  }
}
