/**
 * DL-Engine 系统功能
 * 系统定义、执行、管理功能
 */

// React类型定义（简化版）
type FC<P = {}> = (props: P) => any
import { getState, getMutableState, startReactor, OpaqueType } from '@dl-engine/engine-state'
import { generateUUID } from '@dl-engine/shared-common'
import { SystemState } from './SystemState'
import { ECSState } from './ECSState'
import { nowMilliseconds } from './Timer'

/**
 * 系统UUID类型
 */
export type SystemUUID = OpaqueType<'SystemUUID'> & string

/**
 * 系统插入配置
 */
export type InsertSystem = {
  before?: SystemUUID
  with?: SystemUUID
  after?: SystemUUID
}

/**
 * 系统参数
 */
export interface SystemArgs {
  uuid: string
  insert: InsertSystem
  execute?: () => void
  reactor?: FC
  enabled?: boolean
}

/**
 * 系统接口
 */
export interface System {
  uuid: SystemUUID
  reactor?: FC
  insert?: InsertSystem
  preSystems: SystemUUID[]
  /** 在preSystems之后，subSystems之前运行 */
  execute: () => void
  subSystems: SystemUUID[]
  postSystems: SystemUUID[]
  enabled: boolean
  sceneSystem?: boolean
  // 调试信息
  systemDuration: number
  avgSystemDuration: number
  executionCount: number
  lastExecutionTime: number
}

/**
 * 系统定义映射
 */
export const SystemDefinitions = new Map<SystemUUID, System>()

/**
 * 系统执行顺序
 */
export const SystemExecutionOrder: SystemUUID[] = []

/**
 * 性能警告相关
 */
const lastWarningTime = new Map<SystemUUID, number>()
const warningCooldownDuration = 5000 // 5秒

/**
 * 执行系统
 * @param systemUUID 系统UUID
 */
export function executeSystem(systemUUID: SystemUUID): void {
  const system = SystemDefinitions.get(systemUUID)
  if (!system) {
    console.warn(`System ${systemUUID} does not exist.`)
    return
  }

  if (!system.enabled) {
    return
  }

  const startTime = nowMilliseconds()

  // 执行前置系统
  for (let i = 0; i < system.preSystems.length; i++) {
    executeSystem(system.preSystems[i])
  }

  // 启动reactor（如果有且未启动）
  if (system.reactor) {
    const systemState = getState(SystemState)
    const activeReactors = systemState.activeSystemReactors as any
    if (!activeReactors.has || !activeReactors.has(system.uuid)) {
      // 简化reactor启动逻辑
      console.log(`Starting reactor for system: ${system.uuid}`)
    }
  }

  // 设置当前系统UUID
  const systemState = getMutableState(SystemState)
  systemState.currentSystemUUID.set(system.uuid)

  try {
    // 执行系统主逻辑
    system.execute()
  } catch (error) {
    console.error(`Error executing system ${system.uuid}:`, error)
  }

  // 清除当前系统UUID
  systemState.currentSystemUUID.set('' as SystemUUID)

  // 计算执行时间
  const endTime = nowMilliseconds()
  const systemDuration = endTime - startTime
  system.systemDuration = systemDuration
  system.executionCount++
  system.lastExecutionTime = endTime

  // 计算平均执行时间
  if (system.systemDuration !== 0) {
    system.avgSystemDuration = (systemDuration + system.avgSystemDuration) * 0.5
  }

  // 性能警告
  if (getState(ECSState).performanceProfilingEnabled) {
    if (systemDuration > 50 && (lastWarningTime.get(systemUUID) ?? 0) < endTime - warningCooldownDuration) {
      lastWarningTime.set(systemUUID, endTime)
      console.warn(`Long system execution detected. System: ${system.uuid}, Duration: ${systemDuration}ms`)
    }
  }

  // 更新ECS统计
  const ecsState = getMutableState(ECSState)
  const currentStats = ecsState.systemStats.get({ noproxy: true })
  ecsState.systemStats.set({
    totalExecutionTime: currentStats.totalExecutionTime + systemDuration,
    systemCount: currentStats.systemCount + 1,
    averageExecutionTime: (currentStats.totalExecutionTime + systemDuration) / (currentStats.systemCount + 1)
  })

  // 执行子系统
  for (let i = 0; i < system.subSystems.length; i++) {
    executeSystem(system.subSystems[i])
  }

  // 执行后置系统
  for (let i = 0; i < system.postSystems.length; i++) {
    executeSystem(system.postSystems[i])
  }
}

/**
 * 定义系统
 * @param systemConfig 系统配置
 * @returns 系统UUID
 */
export function defineSystem(systemConfig: SystemArgs): SystemUUID {
  if (SystemDefinitions.has(systemConfig.uuid as SystemUUID)) {
    throw new Error(`System ${systemConfig.uuid} already exists.`)
  }

  const system: System = {
    preSystems: [],
    subSystems: [],
    postSystems: [],
    sceneSystem: false,
    execute: () => {},
    ...systemConfig,
    uuid: systemConfig.uuid as SystemUUID,
    enabled: systemConfig.enabled ?? true,
    systemDuration: 0,
    avgSystemDuration: 0,
    executionCount: 0,
    lastExecutionTime: 0
  }

  SystemDefinitions.set(system.uuid, system)

  // 处理插入配置
  const insert = systemConfig.insert
  if (insert?.before) {
    const referenceSystem = SystemDefinitions.get(insert.before)
    if (!referenceSystem) {
      throw new Error(`System ${insert.before} does not exist.`)
    }
    referenceSystem.preSystems.push(system.uuid)
  }

  if (insert?.with) {
    const referenceSystem = SystemDefinitions.get(insert.with)
    if (!referenceSystem) {
      throw new Error(`System ${insert.with} does not exist.`)
    }
    referenceSystem.subSystems.push(system.uuid)
  }

  if (insert?.after) {
    const referenceSystem = SystemDefinitions.get(insert.after)
    if (!referenceSystem) {
      throw new Error(`System ${insert.after} does not exist.`)
    }
    referenceSystem.postSystems.push(system.uuid)
  }

  // 更新执行顺序
  updateSystemExecutionOrder()

  return system.uuid
}

/**
 * 销毁系统
 * @param systemUUID 系统UUID
 */
export function destroySystem(systemUUID: SystemUUID): void {
  const system = SystemDefinitions.get(systemUUID)
  if (!system) {
    return
  }

  // 停止reactor（简化版）
  const systemState = getState(SystemState)
  const activeReactors = systemState.activeSystemReactors as any
  if (activeReactors.get && activeReactors.delete) {
    const reactor = activeReactors.get(systemUUID)
    if (reactor && reactor.stop) {
      reactor.stop()
      activeReactors.delete(systemUUID)
    }
  }

  // 从其他系统的依赖中移除
  for (const [, otherSystem] of SystemDefinitions) {
    otherSystem.preSystems = otherSystem.preSystems.filter(uuid => uuid !== systemUUID)
    otherSystem.subSystems = otherSystem.subSystems.filter(uuid => uuid !== systemUUID)
    otherSystem.postSystems = otherSystem.postSystems.filter(uuid => uuid !== systemUUID)
  }

  // 移除系统定义
  SystemDefinitions.delete(systemUUID)

  // 更新执行顺序
  updateSystemExecutionOrder()
}

/**
 * 启用系统
 * @param systemUUID 系统UUID
 */
export function enableSystem(systemUUID: SystemUUID): void {
  const system = SystemDefinitions.get(systemUUID)
  if (system) {
    system.enabled = true
  }
}

/**
 * 禁用系统
 * @param systemUUID 系统UUID
 */
export function disableSystem(systemUUID: SystemUUID): void {
  const system = SystemDefinitions.get(systemUUID)
  if (system) {
    system.enabled = false
  }
}

/**
 * 获取系统信息
 * @param systemUUID 系统UUID
 * @returns 系统信息
 */
export function getSystemInfo(systemUUID: SystemUUID) {
  const system = SystemDefinitions.get(systemUUID)
  if (!system) {
    return null
  }

  return {
    uuid: system.uuid,
    enabled: system.enabled,
    executionCount: system.executionCount,
    lastExecutionTime: system.lastExecutionTime,
    avgSystemDuration: system.avgSystemDuration,
    systemDuration: system.systemDuration,
    preSystems: system.preSystems,
    subSystems: system.subSystems,
    postSystems: system.postSystems
  }
}

/**
 * 获取所有系统信息
 * @returns 所有系统信息
 */
export function getAllSystemsInfo() {
  const systems: ReturnType<typeof getSystemInfo>[] = []
  for (const [uuid] of SystemDefinitions) {
    const info = getSystemInfo(uuid)
    if (info) {
      systems.push(info)
    }
  }
  return systems
}

/**
 * 更新系统执行顺序
 */
function updateSystemExecutionOrder(): void {
  SystemExecutionOrder.length = 0
  
  // 简单的拓扑排序
  const visited = new Set<SystemUUID>()
  const visiting = new Set<SystemUUID>()
  
  function visit(systemUUID: SystemUUID): void {
    if (visiting.has(systemUUID)) {
      throw new Error(`Circular dependency detected in system ${systemUUID}`)
    }
    
    if (visited.has(systemUUID)) {
      return
    }
    
    visiting.add(systemUUID)
    
    const system = SystemDefinitions.get(systemUUID)
    if (system) {
      // 先访问依赖的系统
      for (const depUUID of system.preSystems) {
        visit(depUUID)
      }
      for (const depUUID of system.subSystems) {
        visit(depUUID)
      }
      for (const depUUID of system.postSystems) {
        visit(depUUID)
      }
    }
    
    visiting.delete(systemUUID)
    visited.add(systemUUID)
    SystemExecutionOrder.push(systemUUID)
  }
  
  // 访问所有系统
  for (const [systemUUID] of SystemDefinitions) {
    if (!visited.has(systemUUID)) {
      visit(systemUUID)
    }
  }
}

/**
 * 系统工具函数
 */
export const SystemUtils = {
  /**
   * 生成系统UUID
   */
  generateSystemUUID: (name?: string): SystemUUID => {
    const uuid = name ? `${name}_${generateUUID()}` : generateUUID()
    return uuid as SystemUUID
  },

  /**
   * 检查系统是否存在
   */
  systemExists: (systemUUID: SystemUUID): boolean => {
    return SystemDefinitions.has(systemUUID)
  },

  /**
   * 获取系统执行统计
   */
  getExecutionStats: () => {
    let totalDuration = 0
    let totalExecutions = 0
    
    for (const [, system] of SystemDefinitions) {
      totalDuration += system.systemDuration
      totalExecutions += system.executionCount
    }
    
    return {
      totalSystems: SystemDefinitions.size,
      totalDuration,
      totalExecutions,
      averageDuration: totalExecutions > 0 ? totalDuration / totalExecutions : 0
    }
  }
}
