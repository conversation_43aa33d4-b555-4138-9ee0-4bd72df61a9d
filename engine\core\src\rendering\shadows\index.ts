/**
 * DL-Engine 阴影系统
 * 提供阴影渲染和管理功能
 */

import * as THREE from 'three'

/**
 * 阴影质量枚举
 */
export enum ShadowQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}

/**
 * 阴影类型枚举
 */
export enum ShadowType {
  BASIC = 'basic',
  PCF = 'pcf',
  PCF_SOFT = 'pcf_soft',
  VSM = 'vsm'
}

/**
 * 阴影配置接口
 */
export interface ShadowConfig {
  enabled: boolean
  type: ShadowType
  quality: ShadowQuality
  bias: number
  normalBias: number
  radius: number
  autoUpdate: boolean
}

/**
 * 阴影管理器
 */
export class ShadowManager {
  private renderer: THREE.WebGLRenderer | null = null
  private config: ShadowConfig

  constructor(renderer?: THREE.WebGLRenderer) {
    this.renderer = renderer || null
    this.config = {
      enabled: true,
      type: ShadowType.PCF_SOFT,
      quality: ShadowQuality.MEDIUM,
      bias: -0.0001,
      normalBias: 0.02,
      radius: 4,
      autoUpdate: true
    }
  }

  /**
   * 设置渲染器
   */
  setRenderer(renderer: THREE.WebGLRenderer): void {
    this.renderer = renderer
    this.updateRendererShadows()
  }

  /**
   * 更新渲染器阴影设置
   */
  private updateRendererShadows(): void {
    if (!this.renderer) return

    this.renderer.shadowMap.enabled = this.config.enabled
    this.renderer.shadowMap.autoUpdate = this.config.autoUpdate

    // 设置阴影类型
    switch (this.config.type) {
      case ShadowType.BASIC:
        this.renderer.shadowMap.type = THREE.BasicShadowMap
        break
      case ShadowType.PCF:
        this.renderer.shadowMap.type = THREE.PCFShadowMap
        break
      case ShadowType.PCF_SOFT:
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
        break
      case ShadowType.VSM:
        this.renderer.shadowMap.type = THREE.VSMShadowMap
        break
    }
  }

  /**
   * 配置阴影
   */
  configure(config: Partial<ShadowConfig>): void {
    this.config = { ...this.config, ...config }
    this.updateRendererShadows()
  }

  /**
   * 启用阴影
   */
  enable(): void {
    this.config.enabled = true
    this.updateRendererShadows()
  }

  /**
   * 禁用阴影
   */
  disable(): void {
    this.config.enabled = false
    this.updateRendererShadows()
  }

  /**
   * 设置阴影质量
   */
  setQuality(quality: ShadowQuality): void {
    this.config.quality = quality
    this.updateAllShadowMaps()
  }

  /**
   * 设置阴影类型
   */
  setType(type: ShadowType): void {
    this.config.type = type
    this.updateRendererShadows()
  }

  /**
   * 更新所有阴影贴图
   */
  private updateAllShadowMaps(): void {
    // 这个方法需要在有场景引用时实现
    // 遍历所有光源并更新其阴影贴图大小
  }

  /**
   * 为光源配置阴影
   */
  configureLightShadow(light: THREE.Light): void {
    if (!('castShadow' in light) || !light.shadow) return

    light.castShadow = this.config.enabled

    const qualitySettings = this.getQualitySettings(this.config.quality)
    
    // 设置阴影贴图大小
    light.shadow.mapSize.width = qualitySettings.mapSize
    light.shadow.mapSize.height = qualitySettings.mapSize

    // 设置阴影偏移
    light.shadow.bias = this.config.bias
    light.shadow.normalBias = this.config.normalBias
    light.shadow.radius = this.config.radius

    // 配置阴影相机
    this.configureShadowCamera(light)
  }

  /**
   * 配置阴影相机
   */
  private configureShadowCamera(light: THREE.Light): void {
    if (!light.shadow) return

    const camera = light.shadow.camera

    if (light instanceof THREE.DirectionalLight) {
      // 方向光使用正交相机
      if (camera instanceof THREE.OrthographicCamera) {
        const size = 10
        camera.left = -size
        camera.right = size
        camera.top = size
        camera.bottom = -size
        camera.near = 0.5
        camera.far = 50
      }
    } else if (light instanceof THREE.SpotLight) {
      // 聚光灯使用透视相机
      if (camera instanceof THREE.PerspectiveCamera) {
        camera.fov = light.angle * 180 / Math.PI * 2
        camera.near = 0.5
        camera.far = light.distance || 500
      }
    } else if (light instanceof THREE.PointLight) {
      // 点光源使用透视相机
      if (camera instanceof THREE.PerspectiveCamera) {
        camera.fov = 90
        camera.near = 0.5
        camera.far = light.distance || 500
      }
    }

    if ('updateProjectionMatrix' in camera) {
      (camera as any).updateProjectionMatrix()
    }
  }

  /**
   * 获取质量设置
   */
  private getQualitySettings(quality: ShadowQuality) {
    const settings = {
      [ShadowQuality.LOW]: { mapSize: 512 },
      [ShadowQuality.MEDIUM]: { mapSize: 1024 },
      [ShadowQuality.HIGH]: { mapSize: 2048 },
      [ShadowQuality.ULTRA]: { mapSize: 4096 }
    }
    return settings[quality]
  }

  /**
   * 获取当前配置
   */
  getConfig(): ShadowConfig {
    return { ...this.config }
  }
}

/**
 * 阴影工具函数
 */
export const ShadowUtils = {
  /**
   * 为对象启用阴影投射
   */
  enableCastShadow(object: THREE.Object3D, recursive: boolean = true): void {
    object.castShadow = true
    
    if (recursive) {
      object.traverse((child) => {
        child.castShadow = true
      })
    }
  },

  /**
   * 为对象启用阴影接收
   */
  enableReceiveShadow(object: THREE.Object3D, recursive: boolean = true): void {
    object.receiveShadow = true
    
    if (recursive) {
      object.traverse((child) => {
        child.receiveShadow = true
      })
    }
  },

  /**
   * 为对象禁用阴影
   */
  disableShadow(object: THREE.Object3D, recursive: boolean = true): void {
    object.castShadow = false
    object.receiveShadow = false
    
    if (recursive) {
      object.traverse((child) => {
        child.castShadow = false
        child.receiveShadow = false
      })
    }
  },

  /**
   * 优化阴影性能
   */
  optimizeShadowPerformance(light: THREE.Light): void {
    if (!light.shadow) return

    // 禁用自动更新，手动控制
    light.shadow.autoUpdate = false
    
    // 设置合理的阴影相机范围
    if (light instanceof THREE.DirectionalLight) {
      const camera = light.shadow.camera as THREE.OrthographicCamera
      // 根据场景大小调整
      const size = 20
      camera.left = -size
      camera.right = size
      camera.top = size
      camera.bottom = -size
      camera.updateProjectionMatrix()
    }
  },

  /**
   * 创建软阴影
   */
  createSoftShadow(light: THREE.Light, radius: number = 4): void {
    if (!light.shadow) return
    
    light.shadow.radius = radius
    light.shadow.blurSamples = 25
  },

  /**
   * 计算阴影边界
   */
  calculateShadowBounds(objects: THREE.Object3D[]): THREE.Box3 {
    const box = new THREE.Box3()
    
    objects.forEach(object => {
      const objectBox = new THREE.Box3().setFromObject(object)
      box.union(objectBox)
    })
    
    return box
  },

  /**
   * 自动调整阴影相机
   */
  autoAdjustShadowCamera(light: THREE.DirectionalLight, objects: THREE.Object3D[]): void {
    if (!light.shadow) return

    const bounds = this.calculateShadowBounds(objects)
    const camera = light.shadow.camera as THREE.OrthographicCamera
    
    const size = Math.max(
      bounds.max.x - bounds.min.x,
      bounds.max.z - bounds.min.z
    ) / 2
    
    camera.left = -size
    camera.right = size
    camera.top = size
    camera.bottom = -size
    camera.near = bounds.min.y - 10
    camera.far = bounds.max.y + 10
    
    camera.updateProjectionMatrix()
  }
}

/**
 * 预定义阴影配置
 */
export const PredefinedShadowConfigs = {
  /**
   * 高性能配置
   */
  performance: {
    enabled: true,
    type: ShadowType.BASIC,
    quality: ShadowQuality.LOW,
    bias: -0.001,
    normalBias: 0.05,
    radius: 1,
    autoUpdate: false
  } as ShadowConfig,

  /**
   * 平衡配置
   */
  balanced: {
    enabled: true,
    type: ShadowType.PCF,
    quality: ShadowQuality.MEDIUM,
    bias: -0.0005,
    normalBias: 0.02,
    radius: 2,
    autoUpdate: true
  } as ShadowConfig,

  /**
   * 高质量配置
   */
  quality: {
    enabled: true,
    type: ShadowType.PCF_SOFT,
    quality: ShadowQuality.HIGH,
    bias: -0.0001,
    normalBias: 0.01,
    radius: 4,
    autoUpdate: true
  } as ShadowConfig,

  /**
   * 超高质量配置
   */
  ultra: {
    enabled: true,
    type: ShadowType.VSM,
    quality: ShadowQuality.ULTRA,
    bias: -0.00005,
    normalBias: 0.005,
    radius: 8,
    autoUpdate: true
  } as ShadowConfig
}

/**
 * 全局阴影管理器实例
 */
export const globalShadowManager = new ShadowManager()
