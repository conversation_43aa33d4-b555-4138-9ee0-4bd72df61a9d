/**
 * DL-Engine 自然语言处理器
 * 提供文本分析、情感分析、关键词提取等NLP功能
 */

import { OllamaClient, OllamaChatRequest, OllamaChatMessage } from '../ollama/OllamaClient'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * 文本分析结果
 */
export interface TextAnalysisResult {
  /** 原始文本 */
  text: string
  
  /** 语言检测结果 */
  language: string
  
  /** 情感分析 */
  sentiment: {
    /** 情感极性 (-1到1) */
    polarity: number
    
    /** 情感标签 */
    label: 'positive' | 'negative' | 'neutral'
    
    /** 置信度 */
    confidence: number
  }
  
  /** 关键词 */
  keywords: Array<{
    word: string
    score: number
    frequency: number
  }>
  
  /** 主题分类 */
  topics: Array<{
    topic: string
    confidence: number
  }>
  
  /** 文本摘要 */
  summary: string
  
  /** 文本复杂度 */
  complexity: {
    /** 阅读难度等级 */
    readingLevel: number
    
    /** 词汇复杂度 */
    vocabularyComplexity: number
    
    /** 句子复杂度 */
    sentenceComplexity: number
  }
  
  /** 教育相关标签 */
  educationTags: string[]
}

/**
 * 文本处理配置
 */
export interface TextProcessorConfig {
  /** 默认语言 */
  defaultLanguage: string
  
  /** 是否启用情感分析 */
  enableSentimentAnalysis: boolean
  
  /** 是否启用关键词提取 */
  enableKeywordExtraction: boolean
  
  /** 是否启用主题分类 */
  enableTopicClassification: boolean
  
  /** 是否启用文本摘要 */
  enableSummarization: boolean
  
  /** 是否启用复杂度分析 */
  enableComplexityAnalysis: boolean
  
  /** 最大关键词数量 */
  maxKeywords: number
  
  /** 最大主题数量 */
  maxTopics: number
  
  /** 摘要最大长度 */
  maxSummaryLength: number
}

/**
 * 默认文本处理配置
 */
const defaultTextProcessorConfig: TextProcessorConfig = {
  defaultLanguage: 'zh-CN',
  enableSentimentAnalysis: true,
  enableKeywordExtraction: true,
  enableTopicClassification: true,
  enableSummarization: true,
  enableComplexityAnalysis: true,
  maxKeywords: 10,
  maxTopics: 5,
  maxSummaryLength: 200
}

/**
 * 文本处理状态
 */
export interface TextProcessorState {
  /** 配置 */
  config: TextProcessorConfig
  
  /** 处理历史 */
  history: Array<{
    id: string
    timestamp: number
    input: string
    result: TextAnalysisResult
  }>
  
  /** 统计信息 */
  stats: {
    totalProcessed: number
    averageProcessingTime: number
    languageDistribution: Record<string, number>
    sentimentDistribution: Record<string, number>
  }
}

/**
 * 文本处理状态定义
 */
export const TextProcessorState = defineState({
  name: 'DLEngine.TextProcessor',
  initial: (): TextProcessorState => ({
    config: defaultTextProcessorConfig,
    history: [],
    stats: {
      totalProcessed: 0,
      averageProcessingTime: 0,
      languageDistribution: {},
      sentimentDistribution: {}
    }
  })
})

/**
 * 文本处理器
 */
export class TextProcessor {
  private static instance: TextProcessor | null = null
  private ollamaClient: OllamaClient

  // 内部存储
  private history: Array<{
    id: string
    timestamp: number
    input: string
    result: TextAnalysisResult
  }> = []

  /**
   * 获取单例实例
   */
  static getInstance(): TextProcessor {
    if (!TextProcessor.instance) {
      TextProcessor.instance = new TextProcessor()
    }
    return TextProcessor.instance
  }

  constructor() {
    this.ollamaClient = OllamaClient.getInstance()
  }
  
  /**
   * 分析文本
   */
  async analyzeText(text: string, options?: Partial<TextProcessorConfig>): Promise<TextAnalysisResult> {
    const startTime = performance.now()
    const config = { ...getState(TextProcessorState).config, ...options }
    
    try {
      const result: TextAnalysisResult = {
        text,
        language: await this.detectLanguage(text),
        sentiment: await this.analyzeSentiment(text, config),
        keywords: await this.extractKeywords(text, config),
        topics: await this.classifyTopics(text, config),
        summary: await this.summarizeText(text, config),
        complexity: await this.analyzeComplexity(text, config),
        educationTags: await this.extractEducationTags(text)
      }
      
      // 记录处理历史
      const historyEntry = {
        id: this.generateId(),
        timestamp: Date.now(),
        input: text,
        result
      }

      // 添加到内部存储
      this.history.push(historyEntry)

      // 限制历史记录数量
      if (this.history.length > 1000) {
        this.history.shift()
      }

      // 更新状态
      const state = getMutableState(TextProcessorState)
      state.history.set([...this.history])

      // 更新统计信息
      this.updateStatistics(result, performance.now() - startTime)
      
      return result
      
    } catch (error) {
      console.error('Failed to analyze text:', error)
      throw error
    }
  }
  
  /**
   * 检测语言
   */
  private async detectLanguage(text: string): Promise<string> {
    // 简单的语言检测逻辑
    const chineseRegex = /[\u4e00-\u9fff]/
    const englishRegex = /[a-zA-Z]/
    
    const chineseCount = (text.match(chineseRegex) || []).length
    const englishCount = (text.match(englishRegex) || []).length
    
    if (chineseCount > englishCount) {
      return 'zh-CN'
    } else if (englishCount > 0) {
      return 'en-US'
    } else {
      return getState(TextProcessorState).config.defaultLanguage
    }
  }
  
  /**
   * 情感分析
   */
  private async analyzeSentiment(text: string, config: TextProcessorConfig): Promise<TextAnalysisResult['sentiment']> {
    if (!config.enableSentimentAnalysis) {
      return { polarity: 0, label: 'neutral', confidence: 0 }
    }
    
    try {
      const prompt = `请分析以下文本的情感倾向，返回JSON格式：
{
  "polarity": -1到1之间的数值，
  "label": "positive"、"negative"或"neutral"，
  "confidence": 0到1之间的置信度
}

文本：${text}`

      const response = await this.ollamaClient.generate({
        model: 'llama2',
        prompt,
        format: 'json'
      })
      
      const result = JSON.parse(response.response)
      return {
        polarity: Math.max(-1, Math.min(1, result.polarity || 0)),
        label: result.label || 'neutral',
        confidence: Math.max(0, Math.min(1, result.confidence || 0))
      }
      
    } catch (error) {
      console.warn('Sentiment analysis failed, using fallback:', error)
      return this.fallbackSentimentAnalysis(text)
    }
  }
  
  /**
   * 备用情感分析
   */
  private fallbackSentimentAnalysis(text: string): TextAnalysisResult['sentiment'] {
    const positiveWords = ['好', '棒', '优秀', '喜欢', '满意', 'good', 'great', 'excellent', 'love', 'like']
    const negativeWords = ['坏', '差', '糟糕', '讨厌', '不满', 'bad', 'terrible', 'awful', 'hate', 'dislike']
    
    let positiveCount = 0
    let negativeCount = 0
    
    const lowerText = text.toLowerCase()
    
    positiveWords.forEach(word => {
      const matches = lowerText.split(word).length - 1
      positiveCount += matches
    })
    
    negativeWords.forEach(word => {
      const matches = lowerText.split(word).length - 1
      negativeCount += matches
    })
    
    const total = positiveCount + negativeCount
    if (total === 0) {
      return { polarity: 0, label: 'neutral', confidence: 0.5 }
    }
    
    const polarity = (positiveCount - negativeCount) / total
    const label = polarity > 0.1 ? 'positive' : polarity < -0.1 ? 'negative' : 'neutral'
    const confidence = Math.abs(polarity)
    
    return { polarity, label, confidence }
  }
  
  /**
   * 提取关键词
   */
  private async extractKeywords(text: string, config: TextProcessorConfig): Promise<TextAnalysisResult['keywords']> {
    if (!config.enableKeywordExtraction) {
      return []
    }
    
    try {
      const prompt = `请从以下文本中提取${config.maxKeywords}个最重要的关键词，返回JSON格式：
[
  {
    "word": "关键词",
    "score": 0到1之间的重要性分数,
    "frequency": 在文本中出现的频率
  }
]

文本：${text}`

      const response = await this.ollamaClient.generate({
        model: 'llama2',
        prompt,
        format: 'json'
      })
      
      const keywords = JSON.parse(response.response)
      return Array.isArray(keywords) ? keywords.slice(0, config.maxKeywords) : []
      
    } catch (error) {
      console.warn('Keyword extraction failed, using fallback:', error)
      return this.fallbackKeywordExtraction(text, config)
    }
  }
  
  /**
   * 备用关键词提取
   */
  private fallbackKeywordExtraction(text: string, config: TextProcessorConfig): TextAnalysisResult['keywords'] {
    // 简单的关键词提取：基于词频
    const words = text.toLowerCase().match(/[\u4e00-\u9fff]+|[a-zA-Z]+/g) || []
    const wordCount: Record<string, number> = {}
    
    // 停用词列表
    const stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])
    
    words.forEach(word => {
      if (word.length > 1 && !stopWords.has(word)) {
        wordCount[word] = (wordCount[word] || 0) + 1
      }
    })
    
    const totalWords = words.length
    const keywords = Object.entries(wordCount)
      .map(([word, frequency]) => ({
        word,
        score: frequency / totalWords,
        frequency
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, config.maxKeywords)
    
    return keywords
  }
  
  /**
   * 主题分类
   */
  private async classifyTopics(text: string, config: TextProcessorConfig): Promise<TextAnalysisResult['topics']> {
    if (!config.enableTopicClassification) {
      return []
    }
    
    try {
      const prompt = `请对以下文本进行主题分类，返回最多${config.maxTopics}个主题，JSON格式：
[
  {
    "topic": "主题名称",
    "confidence": 0到1之间的置信度
  }
]

可能的主题包括：数学、科学、历史、语言、艺术、体育、技术、社会、自然、文化等。

文本：${text}`

      const response = await this.ollamaClient.generate({
        model: 'llama2',
        prompt,
        format: 'json'
      })
      
      const topics = JSON.parse(response.response)
      return Array.isArray(topics) ? topics.slice(0, config.maxTopics) : []
      
    } catch (error) {
      console.warn('Topic classification failed:', error)
      return []
    }
  }
  
  /**
   * 文本摘要
   */
  private async summarizeText(text: string, config: TextProcessorConfig): Promise<string> {
    if (!config.enableSummarization || text.length < 100) {
      return text.substring(0, config.maxSummaryLength)
    }
    
    try {
      const prompt = `请为以下文本生成一个简洁的摘要，长度不超过${config.maxSummaryLength}个字符：

${text}`

      const response = await this.ollamaClient.generate({
        model: 'llama2',
        prompt
      })
      
      return response.response.substring(0, config.maxSummaryLength)
      
    } catch (error) {
      console.warn('Text summarization failed:', error)
      return text.substring(0, config.maxSummaryLength)
    }
  }
  
  /**
   * 复杂度分析
   */
  private async analyzeComplexity(text: string, config: TextProcessorConfig): Promise<TextAnalysisResult['complexity']> {
    if (!config.enableComplexityAnalysis) {
      return { readingLevel: 5, vocabularyComplexity: 0.5, sentenceComplexity: 0.5 }
    }
    
    // 简单的复杂度分析
    const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0)
    const words = text.match(/[\u4e00-\u9fff]+|[a-zA-Z]+/g) || []
    
    const avgWordsPerSentence = words.length / sentences.length
    const avgCharsPerWord = words.reduce((sum, word) => sum + word.length, 0) / words.length
    
    const readingLevel = Math.min(12, Math.max(1, Math.round(avgWordsPerSentence / 3)))
    const vocabularyComplexity = Math.min(1, avgCharsPerWord / 10)
    const sentenceComplexity = Math.min(1, avgWordsPerSentence / 20)
    
    return {
      readingLevel,
      vocabularyComplexity,
      sentenceComplexity
    }
  }
  
  /**
   * 提取教育标签
   */
  private async extractEducationTags(text: string): Promise<string[]> {
    const educationKeywords = [
      '学习', '教育', '课程', '知识', '技能', '培训', '教学', '学生', '老师', '考试',
      'learning', 'education', 'course', 'knowledge', 'skill', 'training', 'teaching', 'student', 'teacher', 'exam'
    ]
    
    const tags: string[] = []
    const lowerText = text.toLowerCase()
    
    educationKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) {
        tags.push(keyword)
      }
    })
    
    return [...new Set(tags)] // 去重
  }
  
  /**
   * 更新统计信息
   */
  private updateStatistics(result: TextAnalysisResult, processingTime: number): void {
    const state = getMutableState(TextProcessorState)
    const stats = state.stats
    
    // 更新总数和平均处理时间
    const newTotal = stats.totalProcessed.value + 1
    const currentAvg = stats.averageProcessingTime.value
    const newAvg = (currentAvg * (newTotal - 1) + processingTime) / newTotal
    
    // 更新语言分布
    const langDist = { ...stats.languageDistribution.value }
    langDist[result.language] = (langDist[result.language] || 0) + 1
    
    // 更新情感分布
    const sentDist = { ...stats.sentimentDistribution.value }
    sentDist[result.sentiment.label] = (sentDist[result.sentiment.label] || 0) + 1
    
    // 更新状态
    state.stats.totalProcessed.set(newTotal)
    state.stats.averageProcessingTime.set(newAvg)
    state.stats.languageDistribution.set(langDist)
    state.stats.sentimentDistribution.set(sentDist)
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }
}
