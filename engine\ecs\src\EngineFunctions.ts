/**
 * DL-Engine 引擎执行函数
 * 系统执行和帧循环管理
 */

import { getState, getMutableState } from '@dl-engine/engine-state'
import { ECSState } from './ECSState'
import { EngineState } from './EngineState'
import { SystemState } from './SystemState'
import { executeSystem, SystemUUID } from './SystemFunctions'
import { nowMilliseconds } from './Timer'

/**
 * 系统组定义
 */
export const InputSystemGroup = 'InputSystemGroup' as SystemUUID
export const SimulationSystemGroup = 'SimulationSystemGroup' as SystemUUID
export const AnimationSystemGroup = 'AnimationSystemGroup' as SystemUUID
export const PresentationSystemGroup = 'PresentationSystemGroup' as SystemUUID

/**
 * 固定时间步长系统组
 */
export const FixedSystemGroups = new Set([SimulationSystemGroup])

/**
 * 执行系统
 * @param elapsedTime 当前帧时间（毫秒，相对于performance.timeOrigin）
 */
export const executeSystems = (elapsedTime: number): void => {
  const ecsState = getMutableState(ECSState)
  const engineState = getState(EngineState)
  
  // 检查引擎是否正在运行
  if (!engineState.isRunning) {
    return
  }
  
  // 更新帧时间
  ecsState.frameTime.set(performance.timeOrigin + elapsedTime)
  
  const start = nowMilliseconds()
  
  // 计算时间增量
  const elapsedSeconds = elapsedTime / 1000
  const maxDelta = ecsState.maxDeltaSeconds.value
  const deltaSeconds = Math.max(0.001, Math.min(maxDelta, elapsedSeconds - ecsState.elapsedSeconds.value))
  
  ecsState.deltaSeconds.set(deltaSeconds)
  ecsState.elapsedSeconds.set(elapsedSeconds)
  
  // 更新模拟时间
  ecsState.simulationTime.set(ecsState.simulationTime.value + deltaSeconds * 1000)
  
  // 更新帧率（手动计算）
  const currentTime = performance.now()
  ecsState.frameCount.set(ecsState.frameCount.value + 1)

  if (currentTime - ecsState.lastFPSTime.value >= 1000) {
    const fps = ecsState.frameCount.value / ((currentTime - ecsState.lastFPSTime.value) / 1000)
    ecsState.actualFPS.set(Math.round(fps))
    ecsState.frameCount.set(0)
    ecsState.lastFPSTime.set(currentTime)
  }
  
  try {
    // 执行系统组
    executeSystem(InputSystemGroup)
    executeFixedSystem(SimulationSystemGroup)
    executeSystem(AnimationSystemGroup)
    executeSystem(PresentationSystemGroup)
  } catch (error) {
    console.error('Error executing systems:', error)
    
    // 记录系统错误
    const systemState = getMutableState(SystemState)
    const currentSystem = systemState.currentSystemUUID.value
    if (currentSystem) {
      // 手动记录错误到错误统计中
      const currentErrorStats = systemState.errorStats.get({ noproxy: true })
      const newErrorsBySystem = new Map(currentErrorStats.errorsBySystem)
      newErrorsBySystem.set(currentSystem, (newErrorsBySystem.get(currentSystem) || 0) + 1)

      const newErrorStats = {
        totalErrors: currentErrorStats.totalErrors + 1,
        errorsBySystem: newErrorsBySystem,
        lastError: {
          system: currentSystem,
          error: error instanceof Error ? error.message : String(error),
          timestamp: Date.now()
        }
      }
      systemState.errorStats.set(newErrorStats)
    }
  }
  
  // 更新系统执行统计
  const executionTime = nowMilliseconds() - start
  const currentStats = ecsState.systemStats.get({ noproxy: true })
  const newStats = {
    totalExecutionTime: currentStats.totalExecutionTime + executionTime,
    systemCount: currentStats.systemCount + 1,
    averageExecutionTime: 0
  }
  newStats.averageExecutionTime = newStats.totalExecutionTime / newStats.systemCount
  ecsState.systemStats.set(newStats)

  // 更新内存统计（每60帧更新一次）
  if (ecsState.frameCount.value % 60 === 0) {
    // 检查是否支持performance.memory (Chrome特有)
    const performanceAny = performance as any
    if (performanceAny.memory) {
      const memory = performanceAny.memory
      ecsState.memoryStats.usedJSHeapSize.set(memory.usedJSHeapSize)
      ecsState.memoryStats.totalJSHeapSize.set(memory.totalJSHeapSize)
      ecsState.memoryStats.jsHeapSizeLimit.set(memory.jsHeapSizeLimit)
    }
  }
}

/**
 * 执行固定时间步长系统
 * @param systemUUID 系统UUID
 */
export const executeFixedSystem = (systemUUID: SystemUUID): void => {
  const ecsState = getMutableState(ECSState)
  const fixedDelta = ecsState.fixedDeltaSeconds.value
  
  // 累积时间
  ecsState.fixedTimeAccumulator.set(ecsState.fixedTimeAccumulator.value + ecsState.deltaSeconds.value)
  
  // 执行固定时间步长
  while (ecsState.fixedTimeAccumulator.value >= fixedDelta) {
    // 临时设置固定增量时间
    const originalDelta = ecsState.deltaSeconds.value
    ecsState.deltaSeconds.set(fixedDelta)
    
    // 执行系统
    executeSystem(systemUUID)
    
    // 恢复原始增量时间
    ecsState.deltaSeconds.set(originalDelta)
    
    // 减少累积器
    ecsState.fixedTimeAccumulator.set(ecsState.fixedTimeAccumulator.value - fixedDelta)
  }
}

/**
 * 启动引擎主循环
 */
export const startEngineLoop = (): void => {
  const engineState = getMutableState(EngineState)
  
  if (engineState.isRunning.value) {
    console.warn('Engine loop is already running')
    return
  }
  
  engineState.isRunning.set(true)
  
  let lastTime = 0
  
  const loop = (currentTime: number) => {
    if (!getState(EngineState).isRunning) {
      return
    }
    
    // 计算帧时间
    const deltaTime = currentTime - lastTime
    lastTime = currentTime
    
    // 执行系统
    executeSystems(currentTime)
    
    // 更新引擎运行时间
    const currentUptime = engineState.uptime.value
    engineState.uptime.set(currentUptime + deltaTime)
    
    // 请求下一帧
    requestAnimationFrame(loop)
  }
  
  // 启动循环
  requestAnimationFrame((time) => {
    lastTime = time
    requestAnimationFrame(loop)
  })
  
  console.log('Engine loop started')
}

/**
 * 停止引擎主循环
 */
export const stopEngineLoop = (): void => {
  const engineState = getMutableState(EngineState)
  engineState.isRunning.set(false)
  console.log('Engine loop stopped')
}

/**
 * 暂停引擎
 */
export const pauseEngine = (): void => {
  const ecsState = getMutableState(ECSState)
  ecsState.isPaused.set(true)
  console.log('Engine paused')
}

/**
 * 恢复引擎
 */
export const resumeEngine = (): void => {
  const ecsState = getMutableState(ECSState)
  ecsState.isPaused.set(false)
  console.log('Engine resumed')
}

/**
 * 单步执行（调试用）
 */
export const stepEngine = (): void => {
  const engineState = getState(EngineState)
  
  if (!engineState.isInitialized) {
    throw new Error('Engine not initialized')
  }
  
  const currentTime = performance.now()
  executeSystems(currentTime)
  
  console.log('Engine stepped')
}

/**
 * 重置引擎统计
 */
export const resetEngineStats = (): void => {
  const ecsState = getMutableState(ECSState)
  const systemState = getMutableState(SystemState)

  // 手动重置ECS统计
  ecsState.systemStats.set({
    totalExecutionTime: 0,
    systemCount: 0,
    averageExecutionTime: 0
  })
  ecsState.frameCount.set(0)
  ecsState.lastFPSTime.set(performance.now())

  // 手动重置系统统计
  systemState.executionStats.set({
    totalExecutions: 0,
    totalDuration: 0,
    averageDuration: 0,
    slowestSystem: { uuid: null, duration: 0 },
    fastestSystem: { uuid: null, duration: 0 }
  })
  systemState.errorStats.set({
    totalErrors: 0,
    errorsBySystem: new Map(),
    lastError: { system: null, error: null, timestamp: 0 }
  })

  console.log('Engine stats reset')
}

/**
 * 获取引擎性能信息
 */
export const getEnginePerformance = () => {
  const ecsState = getState(ECSState)
  const systemState = getState(SystemState)
  
  return {
    fps: ecsState.actualFPS,
    frameTime: ecsState.deltaSeconds * 1000,
    systemStats: ecsState.systemStats,
    memoryStats: ecsState.memoryStats,
    systemPerformance: systemState.executionStats,
    errorStats: systemState.errorStats
  }
}

/**
 * 设置引擎配置
 */
export const configureEngine = (config: {
  targetFPS?: number
  maxDeltaSeconds?: number
  performanceProfiling?: boolean
  debugMode?: boolean
  verboseLogging?: boolean
}) => {
  const ecsState = getMutableState(ECSState)
  const systemState = getMutableState(SystemState)
  const engineState = getMutableState(EngineState)
  
  if (config.targetFPS !== undefined) {
    ecsState.targetFPS.set(Math.max(1, Math.min(240, config.targetFPS)))
    ecsState.fixedDeltaSeconds.set(1 / config.targetFPS)
  }
  
  if (config.maxDeltaSeconds !== undefined) {
    ecsState.maxDeltaSeconds.set(config.maxDeltaSeconds)
  }
  
  if (config.performanceProfiling !== undefined) {
    ecsState.performanceProfilingEnabled.set(config.performanceProfiling)
    systemState.performanceProfilingEnabled.set(config.performanceProfiling)
  }
  
  if (config.debugMode !== undefined) {
    systemState.debugMode.set(config.debugMode)
    engineState.debugMode.set(config.debugMode)
  }
  
  if (config.verboseLogging !== undefined) {
    systemState.verboseLogging.set(config.verboseLogging)
    engineState.verboseLogging.set(config.verboseLogging)
  }
}

/**
 * 引擎工具函数
 */
export const EngineUtils = {
  /**
   * 检查引擎是否正在运行
   */
  isRunning: (): boolean => {
    return getState(EngineState).isRunning
  },
  
  /**
   * 检查引擎是否暂停
   */
  isPaused: (): boolean => {
    return getState(ECSState).isPaused
  },
  
  /**
   * 获取当前FPS
   */
  getCurrentFPS: (): number => {
    return getState(ECSState).actualFPS
  },
  
  /**
   * 获取帧时间
   */
  getFrameTime: (): number => {
    return getState(ECSState).deltaSeconds * 1000
  },
  
  /**
   * 获取运行时间
   */
  getUptime: (): number => {
    return getState(EngineState).uptime
  },
  
  /**
   * 格式化性能信息
   */
  formatPerformanceInfo: () => {
    const perf = getEnginePerformance()
    return {
      fps: `${perf.fps.toFixed(1)} FPS`,
      frameTime: `${perf.frameTime.toFixed(2)}ms`,
      memory: `${(perf.memoryStats.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
      systems: `${perf.systemStats.systemCount} systems`,
      avgSystemTime: `${perf.systemStats.averageExecutionTime.toFixed(2)}ms`
    }
  }
}
