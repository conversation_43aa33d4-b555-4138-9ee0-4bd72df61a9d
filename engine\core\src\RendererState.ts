/**
 * DL-Engine 渲染器状态管理
 * 管理Three.js渲染器和渲染相关状态
 */

import { defineState, State, getState } from '@dl-engine/engine-state'
import { 
  WebGLRenderer, 
  Scene, 
  Camera, 
  PerspectiveCamera,
  OrthographicCamera,
  WebGLRenderTarget,
  Vector2,
  Color,
  Fog,
  FogExp2
} from 'three'

/**
 * 渲染模式
 */
export enum RenderMode {
  /** 正常渲染 */
  NORMAL = 'normal',
  /** 线框模式 */
  WIREFRAME = 'wireframe',
  /** 深度模式 */
  DEPTH = 'depth',
  /** 法线模式 */
  NORMAL_VIEW = 'normal_view',
  /** 材质预览 */
  MATERIAL_PREVIEW = 'material_preview'
}

/**
 * 渲染质量设置
 */
export enum RenderQuality {
  /** 低质量 */
  LOW = 'low',
  /** 中等质量 */
  MEDIUM = 'medium',
  /** 高质量 */
  HIGH = 'high',
  /** 超高质量 */
  ULTRA = 'ultra'
}

/**
 * 抗锯齿类型
 */
export enum AntiAliasing {
  /** 无抗锯齿 */
  NONE = 'none',
  /** MSAA */
  MSAA = 'msaa',
  /** FXAA */
  FXAA = 'fxaa',
  /** SMAA */
  SMAA = 'smaa',
  /** TAA */
  TAA = 'taa'
}

/**
 * 渲染器状态接口
 */
export interface RendererStateType {
  /** 主渲染器 */
  renderer: WebGLRenderer | null
  
  /** 主场景 */
  scene: Scene | null
  
  /** 主相机 */
  camera: Camera | null
  
  /** 渲染目标 */
  renderTarget: WebGLRenderTarget | null
  
  /** 画布尺寸 */
  canvasSize: {
    width: number
    height: number
  }
  
  /** 设备像素比 */
  pixelRatio: number
  
  /** 渲染模式 */
  renderMode: RenderMode
  
  /** 渲染质量 */
  quality: RenderQuality
  
  /** 抗锯齿设置 */
  antiAliasing: AntiAliasing
  
  /** 是否启用阴影 */
  shadowsEnabled: boolean
  
  /** 阴影质量 */
  shadowMapSize: number
  
  /** 是否启用后处理 */
  postProcessingEnabled: boolean
  
  /** 背景颜色 */
  backgroundColor: Color
  
  /** 雾效设置 */
  fog: {
    enabled: boolean
    type: 'linear' | 'exponential'
    color: Color
    near: number
    far: number
    density: number
  }
  
  /** 渲染统计 */
  stats: {
    frameCount: number
    triangles: number
    drawCalls: number
    textureMemory: number
    geometryMemory: number
    lastFrameTime: number
    averageFrameTime: number
  }
  
  /** 性能设置 */
  performance: {
    maxLights: number
    maxShadowCasters: number
    frustumCulling: boolean
    occlusionCulling: boolean
    levelOfDetail: boolean
    instancedRendering: boolean
  }
  
  /** 调试设置 */
  debug: {
    showWireframe: boolean
    showBoundingBoxes: boolean
    showLightHelpers: boolean
    showCameraHelpers: boolean
    showStats: boolean
    logRenderCalls: boolean
  }
  
  /** VR/AR设置 */
  xr: {
    enabled: boolean
    mode: 'vr' | 'ar' | null
    session: any | null
    referenceSpace: any | null
  }
}

/**
 * 渲染器状态默认值
 */
const RendererStateDefaults: RendererStateType = {
  renderer: null,
  scene: null,
  camera: null,
  renderTarget: null,
  canvasSize: {
    width: 1920,
    height: 1080
  },
  pixelRatio: typeof window !== 'undefined' ? window.devicePixelRatio : 1,
  renderMode: RenderMode.NORMAL,
  quality: RenderQuality.HIGH,
  antiAliasing: AntiAliasing.MSAA,
  shadowsEnabled: true,
  shadowMapSize: 2048,
  postProcessingEnabled: true,
  backgroundColor: new Color(0x000000),
  fog: {
    enabled: false,
    type: 'linear',
    color: new Color(0xffffff),
    near: 1,
    far: 1000,
    density: 0.00025
  },
  stats: {
    frameCount: 0,
    triangles: 0,
    drawCalls: 0,
    textureMemory: 0,
    geometryMemory: 0,
    lastFrameTime: 0,
    averageFrameTime: 16.67
  },
  performance: {
    maxLights: 16,
    maxShadowCasters: 4,
    frustumCulling: true,
    occlusionCulling: false,
    levelOfDetail: true,
    instancedRendering: true
  },
  debug: {
    showWireframe: false,
    showBoundingBoxes: false,
    showLightHelpers: false,
    showCameraHelpers: false,
    showStats: false,
    logRenderCalls: false
  },
  xr: {
    enabled: false,
    mode: null,
    session: null,
    referenceSpace: null
  }
}

/**
 * 渲染器状态定义
 */
export const RendererState = defineState({
  name: 'DLEngine.Renderer',
  initial: () => RendererStateDefaults
})

/**
 * 渲染器状态工具函数
 */
export const RendererStateUtils = {
  /**
   * 获取渲染器信息
   */
  getRendererInfo: () => {
    const state = getState(RendererState)
    return {
      canvasSize: state.canvasSize,
      pixelRatio: state.pixelRatio,
      renderMode: state.renderMode,
      quality: state.quality,
      antiAliasing: state.antiAliasing,
      shadowsEnabled: state.shadowsEnabled,
      postProcessingEnabled: state.postProcessingEnabled
    }
  },
  
  /**
   * 获取性能统计
   */
  getPerformanceStats: () => {
    const state = getState(RendererState)
    return state.stats
  },
  
  /**
   * 检查是否支持WebGL2
   */
  supportsWebGL2: (): boolean => {
    if (typeof window === 'undefined') return false
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('webgl2')
    return context !== null
  },
  
  /**
   * 检查是否支持WebXR
   */
  supportsWebXR: (): boolean => {
    return typeof navigator !== 'undefined' && 'xr' in navigator
  },
  
  /**
   * 格式化内存使用
   */
  formatMemoryUsage: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`
  }
}
