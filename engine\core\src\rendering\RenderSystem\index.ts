/**
 * DL-Engine 渲染系统
 * 核心渲染系统实现
 */

import * as THREE from 'three'
import { defineSystem, SystemUUID } from '@dl-engine/engine-ecs'
import { getMutableState, getState } from '@dl-engine/engine-state'
import { RendererState } from '../../RendererState'

/**
 * 渲染系统配置
 */
export interface RenderSystemConfig {
  enableShadows: boolean
  enablePostProcessing: boolean
  antialias: boolean
  alpha: boolean
  preserveDrawingBuffer: boolean
  powerPreference: 'default' | 'high-performance' | 'low-power'
}

/**
 * 渲染系统类
 */
export class RenderSystem {
  private renderer: THREE.WebGLRenderer | null = null
  private scene: THREE.Scene | null = null
  private camera: THREE.Camera | null = null
  private config: RenderSystemConfig

  constructor(config?: Partial<RenderSystemConfig>) {
    this.config = {
      enableShadows: true,
      enablePostProcessing: false,
      antialias: true,
      alpha: false,
      preserveDrawingBuffer: false,
      powerPreference: 'high-performance',
      ...config
    }
  }

  /**
   * 初始化渲染系统
   */
  initialize(canvas?: HTMLCanvasElement): void {
    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: this.config.antialias,
      alpha: this.config.alpha,
      preserveDrawingBuffer: this.config.preserveDrawingBuffer,
      powerPreference: this.config.powerPreference
    })

    // 配置渲染器
    this.configureRenderer()

    // 创建默认场景和相机
    this.scene = new THREE.Scene()
    this.camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000)

    // 更新状态
    const rendererState = getMutableState(RendererState)
    rendererState.renderer.set(this.renderer)
    rendererState.scene.set(this.scene)
    rendererState.camera.set(this.camera)
  }

  /**
   * 配置渲染器
   */
  private configureRenderer(): void {
    if (!this.renderer) return

    // 设置像素比
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // 设置颜色空间
    this.renderer.outputColorSpace = THREE.SRGBColorSpace

    // 设置色调映射
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping
    this.renderer.toneMappingExposure = 1

    // 配置阴影
    if (this.config.enableShadows) {
      this.renderer.shadowMap.enabled = true
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap
    }

    // 物理正确的光照在新版本Three.js中已默认启用
  }

  /**
   * 渲染一帧
   */
  render(): void {
    if (!this.renderer || !this.scene || !this.camera) return

    const startTime = performance.now()

    // 渲染场景
    this.renderer.render(this.scene, this.camera)

    // 更新统计信息
    const renderTime = performance.now() - startTime
    this.updateStats(renderTime)
  }

  /**
   * 更新统计信息
   */
  private updateStats(renderTime: number): void {
    const rendererState = getMutableState(RendererState)
    
    rendererState.stats.lastFrameTime.set(renderTime)
    rendererState.stats.triangles.set(this.renderer?.info.render.triangles || 0)
    rendererState.stats.drawCalls.set(this.renderer?.info.render.calls || 0)
    rendererState.stats.geometryMemory.set(this.renderer?.info.memory.geometries || 0)
    rendererState.stats.textureMemory.set(this.renderer?.info.memory.textures || 0)
  }

  /**
   * 设置场景
   */
  setScene(scene: THREE.Scene): void {
    this.scene = scene
    getMutableState(RendererState).scene.set(scene)
  }

  /**
   * 设置相机
   */
  setCamera(camera: THREE.Camera): void {
    this.camera = camera
    getMutableState(RendererState).camera.set(camera)
  }

  /**
   * 设置渲染器大小
   */
  setSize(width: number, height: number): void {
    if (!this.renderer) return

    this.renderer.setSize(width, height)
    
    // 更新相机宽高比
    if (this.camera instanceof THREE.PerspectiveCamera) {
      this.camera.aspect = width / height
      this.camera.updateProjectionMatrix()
    }

    // 更新状态
    const rendererState = getMutableState(RendererState)
    rendererState.canvasSize.width.set(width)
    rendererState.canvasSize.height.set(height)
  }

  /**
   * 设置像素比
   */
  setPixelRatio(ratio: number): void {
    if (!this.renderer) return

    this.renderer.setPixelRatio(ratio)
    getMutableState(RendererState).pixelRatio.set(ratio)
  }

  /**
   * 获取渲染器
   */
  getRenderer(): THREE.WebGLRenderer | null {
    return this.renderer
  }

  /**
   * 获取场景
   */
  getScene(): THREE.Scene | null {
    return this.scene
  }

  /**
   * 获取相机
   */
  getCamera(): THREE.Camera | null {
    return this.camera
  }

  /**
   * 释放资源
   */
  dispose(): void {
    if (this.renderer) {
      this.renderer.dispose()
      this.renderer = null
    }

    if (this.scene) {
      // 清理场景中的所有对象
      this.scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          if (object.geometry) {
            object.geometry.dispose()
          }
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach(material => material.dispose())
            } else {
              object.material.dispose()
            }
          }
        }
      })
      this.scene = null
    }

    this.camera = null

    // 清理状态
    const rendererState = getMutableState(RendererState)
    rendererState.renderer.set(null)
    rendererState.scene.set(null)
    rendererState.camera.set(null)
  }
}

/**
 * 渲染系统定义
 */
export const RenderSystemDefinition = defineSystem({
  uuid: 'core.render' as SystemUUID,
  insert: { with: 'core.render' as SystemUUID },
  execute: () => {
    const rendererState = getState(RendererState)
    
    if (rendererState.renderer && rendererState.scene && rendererState.camera) {
      const startTime = performance.now()
      
      // 渲染场景
      rendererState.renderer.render(rendererState.scene, rendererState.camera)
      
      // 更新统计信息
      const renderTime = performance.now() - startTime
      const mutableState = getMutableState(RendererState)
      
      mutableState.stats.lastFrameTime.set(renderTime)
      mutableState.stats.triangles.set(rendererState.renderer.info.render.triangles)
      mutableState.stats.drawCalls.set(rendererState.renderer.info.render.calls)
      mutableState.stats.geometryMemory.set(rendererState.renderer.info.memory.geometries)
      mutableState.stats.textureMemory.set(rendererState.renderer.info.memory.textures)
    }
  }
})

/**
 * 渲染工具函数
 */
export const RenderUtils = {
  /**
   * 创建默认渲染系统
   */
  createDefaultRenderSystem(canvas?: HTMLCanvasElement): RenderSystem {
    const renderSystem = new RenderSystem()
    renderSystem.initialize(canvas)
    return renderSystem
  },

  /**
   * 获取渲染信息
   */
  getRenderInfo(): {
    triangles: number
    drawCalls: number
    geometries: number
    textures: number
    programs: number
  } {
    const rendererState = getState(RendererState)
    const renderer = rendererState.renderer
    
    if (!renderer) {
      return {
        triangles: 0,
        drawCalls: 0,
        geometries: 0,
        textures: 0,
        programs: 0
      }
    }

    return {
      triangles: renderer.info.render.triangles,
      drawCalls: renderer.info.render.calls,
      geometries: renderer.info.memory.geometries,
      textures: renderer.info.memory.textures,
      programs: renderer.info.programs?.length || 0
    }
  },

  /**
   * 截图
   */
  takeScreenshot(format: 'image/png' | 'image/jpeg' = 'image/png', quality: number = 1.0): string | null {
    const rendererState = getState(RendererState)
    const renderer = rendererState.renderer
    
    if (!renderer) return null

    return renderer.domElement.toDataURL(format, quality)
  },

  /**
   * 设置背景颜色
   */
  setBackgroundColor(color: number | string): void {
    const rendererState = getState(RendererState)
    const scene = rendererState.scene
    
    if (scene) {
      scene.background = new THREE.Color(color)
    }
  },

  /**
   * 设置雾效
   */
  setFog(color: number | string, near: number, far: number): void {
    const rendererState = getState(RendererState)
    const scene = rendererState.scene
    
    if (scene) {
      scene.fog = new THREE.Fog(color, near, far)
    }
  },

  /**
   * 清除雾效
   */
  clearFog(): void {
    const rendererState = getState(RendererState)
    const scene = rendererState.scene
    
    if (scene) {
      scene.fog = null
    }
  }
}

/**
 * 全局渲染系统实例
 */
export const globalRenderSystem = new RenderSystem()
