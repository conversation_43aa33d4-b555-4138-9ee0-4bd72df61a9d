/**
 * DL-Engine 存储管理函数
 * 管理全局状态存储和配置
 */

import { v4 as uuidv4 } from 'uuid'
import {
  DLEngineStore,
  PeerID,
  UserID,
  Topic,
  ReactorRoot
} from './types'

/**
 * UUID转换为32位无符号整数
 */
function uuidToUint32(uuid: string): number {
  // 简单的UUID到数字的转换
  let hash = 0
  for (let i = 0; i < uuid.length; i++) {
    const char = uuid.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

/**
 * 创建DL-Engine存储
 * @param options 存储选项
 * @returns DL-Engine存储实例
 */
export function createDLEngineStore(options?: {
  getDispatchTime?: () => number
  defaultDispatchDelay?: () => number
  getCurrentReactorRoot?: () => ReactorRoot | undefined
  getAgentID?: () => UserID
}): DLEngineStore {
  const peerID = uuidv4() as PeerID
  const peerIndex = uuidToUint32(peerID)
  
  const store: DLEngineStore = {
    defaultTopic: 'default' as Topic,
    forwardingTopics: new Set<Topic>(),
    getDispatchTime: options?.getDispatchTime ?? (() => Date.now()),
    defaultDispatchDelay: options?.defaultDispatchDelay ?? (() => 0),
    getCurrentReactorRoot: options?.getCurrentReactorRoot ?? (() => undefined),
    getAgentID: options?.getAgentID ?? (() => 'default' as UserID),
    peerID,
    peerIndex,
    stateMap: {},
    stateReactors: {},
    actions: {
      queues: new Map(),
      cached: [],
      incoming: [],
      history: [],
      knownUUIDs: new Set(),
      outgoing: {}
    },
    logger: (name: string) => ({
      info: (message: string, ...args: any[]) => console.log(`[${name}] ${message}`, ...args),
      warn: (message: string, ...args: any[]) => console.warn(`[${name}] ${message}`, ...args),
      error: (message: string, ...args: any[]) => console.error(`[${name}] ${message}`, ...args),
      debug: (message: string, ...args: any[]) => console.debug(`[${name}] ${message}`, ...args)
    })
  }
  
  return store
}

/**
 * 全局DL-Engine存储实例
 */
export const DLEngineFlux = {
  store: null as DLEngineStore | null
}

/**
 * 初始化DL-Engine存储
 * @param options 存储选项
 */
export function initializeDLEngineStore(options?: Parameters<typeof createDLEngineStore>[0]): void {
  if (DLEngineFlux.store) {
    console.warn('DL-Engine store already initialized')
    return
  }
  
  DLEngineFlux.store = createDLEngineStore(options)
  console.log('DL-Engine store initialized successfully')
}

/**
 * 获取DL-Engine存储
 * @returns DL-Engine存储实例
 */
export function getDLEngineStore(): DLEngineStore {
  if (!DLEngineFlux.store) {
    throw new Error('DL-Engine store not initialized. Call initializeDLEngineStore() first.')
  }
  return DLEngineFlux.store
}

/**
 * 销毁DL-Engine存储
 */
export function destroyDLEngineStore(): void {
  if (!DLEngineFlux.store) {
    return
  }
  
  // 停止所有反应器
  for (const reactor of Object.values(DLEngineFlux.store.stateReactors)) {
    if (reactor && typeof reactor.stop === 'function') {
      reactor.stop()
    }
  }
  
  // 清理动作队列
  DLEngineFlux.store.actions.queues.clear()
  DLEngineFlux.store.actions.cached.length = 0
  DLEngineFlux.store.actions.incoming.length = 0
  DLEngineFlux.store.actions.history.length = 0
  DLEngineFlux.store.actions.knownUUIDs.clear()
  
  // 清理状态
  DLEngineFlux.store.stateMap = {}
  DLEngineFlux.store.stateReactors = {}
  
  DLEngineFlux.store = null
  console.log('DL-Engine store destroyed')
}

/**
 * 重置DL-Engine存储
 * @param options 新的存储选项
 */
export function resetDLEngineStore(options?: Parameters<typeof createDLEngineStore>[0]): void {
  destroyDLEngineStore()
  initializeDLEngineStore(options)
}

/**
 * 存储统计信息
 */
export interface StoreStats {
  /** 状态数量 */
  stateCount: number
  
  /** 反应器数量 */
  reactorCount: number
  
  /** 动作队列数量 */
  queueCount: number
  
  /** 缓存动作数量 */
  cachedActionCount: number
  
  /** 传入动作数量 */
  incomingActionCount: number
  
  /** 历史动作数量 */
  historyActionCount: number
  
  /** 已知UUID数量 */
  knownUUIDCount: number
  
  /** 传出主题数量 */
  outgoingTopicCount: number
  
  /** 内存使用估算（字节） */
  estimatedMemoryUsage: number
}

/**
 * 获取存储统计信息
 * @returns 存储统计信息
 */
export function getStoreStats(): StoreStats {
  const store = getDLEngineStore()
  
  // 估算内存使用
  let estimatedMemoryUsage = 0
  try {
    const stateData = JSON.stringify(store.stateMap)
    const actionData = JSON.stringify({
      cached: store.actions.cached,
      incoming: store.actions.incoming,
      history: store.actions.history.slice(-100), // 只计算最近100个历史动作
      outgoing: store.actions.outgoing
    })
    // 使用字符串长度估算内存使用（每个字符约2字节）
    estimatedMemoryUsage = (stateData.length + actionData.length) * 2
  } catch (error) {
    console.warn('Failed to estimate memory usage:', error)
  }
  
  return {
    stateCount: Object.keys(store.stateMap).length,
    reactorCount: Object.keys(store.stateReactors).length,
    queueCount: store.actions.queues.size,
    cachedActionCount: store.actions.cached.length,
    incomingActionCount: store.actions.incoming.length,
    historyActionCount: store.actions.history.length,
    knownUUIDCount: store.actions.knownUUIDs.size,
    outgoingTopicCount: Object.keys(store.actions.outgoing).length,
    estimatedMemoryUsage
  }
}

/**
 * 清理存储
 * @param options 清理选项
 */
export function cleanupStore(options: {
  /** 清理历史动作（保留最近N个） */
  keepRecentActions?: number
  
  /** 清理已知UUID（保留最近N个） */
  keepRecentUUIDs?: number
  
  /** 清理传出动作历史 */
  clearOutgoingHistory?: boolean
  
  /** 强制垃圾回收 */
  forceGC?: boolean
} = {}): void {
  const store = getDLEngineStore()
  
  // 清理历史动作
  if (options.keepRecentActions !== undefined) {
    const keepCount = Math.max(0, options.keepRecentActions)
    if (store.actions.history.length > keepCount) {
      store.actions.history.splice(0, store.actions.history.length - keepCount)
    }
  }
  
  // 清理已知UUID
  if (options.keepRecentUUIDs !== undefined) {
    const keepCount = Math.max(0, options.keepRecentUUIDs)
    if (store.actions.knownUUIDs.size > keepCount) {
      const uuids = Array.from(store.actions.knownUUIDs)
      const toKeep = uuids.slice(-keepCount)
      store.actions.knownUUIDs.clear()
      toKeep.forEach(uuid => store.actions.knownUUIDs.add(uuid))
    }
  }
  
  // 清理传出动作历史
  if (options.clearOutgoingHistory) {
    Object.entries(store.actions.outgoing).forEach(([, outgoingTopic]) => {
      const topic = outgoingTopic as {
        queue: any[]
        history: any[]
        forwardedUUIDs: Set<string>
      }
      topic.history.length = 0
      topic.forwardedUUIDs.clear()
    })
  }
  
  // 强制垃圾回收（如果支持）
  if (options.forceGC && typeof globalThis.gc === 'function') {
    globalThis.gc()
  }
  
  console.log('Store cleanup completed')
}

/**
 * 存储配置
 */
export interface StoreConfig {
  /** 最大历史动作数量 */
  maxHistoryActions: number
  
  /** 最大已知UUID数量 */
  maxKnownUUIDs: number
  
  /** 自动清理间隔（毫秒） */
  autoCleanupInterval: number
  
  /** 是否启用调试日志 */
  enableDebugLogging: boolean
  
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean
}

/**
 * 默认存储配置
 */
export const defaultStoreConfig: StoreConfig = {
  maxHistoryActions: 1000,
  maxKnownUUIDs: 10000,
  autoCleanupInterval: 60000, // 1分钟
  enableDebugLogging: false,
  enablePerformanceMonitoring: true
}

/**
 * 当前存储配置
 */
let currentStoreConfig: StoreConfig = { ...defaultStoreConfig }

/**
 * 配置存储
 * @param config 存储配置
 */
export function configureStore(config: Partial<StoreConfig>): void {
  currentStoreConfig = { ...currentStoreConfig, ...config }
  
  // 应用配置
  if (config.enableDebugLogging !== undefined) {
    // 可以在这里设置日志级别
  }
  
  console.log('Store configured:', currentStoreConfig)
}

/**
 * 获取存储配置
 * @returns 当前存储配置
 */
export function getStoreConfig(): StoreConfig {
  return { ...currentStoreConfig }
}

/**
 * 自动清理定时器
 */
let autoCleanupTimer: NodeJS.Timeout | null = null

/**
 * 启动自动清理
 */
export function startAutoCleanup(): void {
  if (autoCleanupTimer) {
    clearInterval(autoCleanupTimer)
  }
  
  autoCleanupTimer = setInterval(() => {
    cleanupStore({
      keepRecentActions: currentStoreConfig.maxHistoryActions,
      keepRecentUUIDs: currentStoreConfig.maxKnownUUIDs,
      clearOutgoingHistory: false
    })
  }, currentStoreConfig.autoCleanupInterval)
  
  console.log('Auto cleanup started')
}

/**
 * 停止自动清理
 */
export function stopAutoCleanup(): void {
  if (autoCleanupTimer) {
    clearInterval(autoCleanupTimer)
    autoCleanupTimer = null
    console.log('Auto cleanup stopped')
  }
}
