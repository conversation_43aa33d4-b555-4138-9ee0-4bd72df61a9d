/**
 * DL-Engine ECS系统
 * 基于bitECS的高性能实体组件系统
 * 支持网络同步和教育场景特定功能
 */

// 确保依赖模块被导入
import '@dl-engine/engine-state'

// 导出bitECS核心功能
export { Not, getAllEntities, getEntityComponents } from 'bitecs'

// 导出组件功能
export * from './src/ComponentFunctions'

// 导出引擎核心
export * from './src/Engine'
export * from './src/EngineFunctions'
export * from './src/EngineState'

// 导出实体相关
export * from './src/Entity'
export * from './src/EntityTree'

// 导出系统相关
export * from './src/SystemFunctions'
export {
  AnimationSystemGroup as ECSAnimationSystemGroup,
  InputSystemGroup as ECSInputSystemGroup,
  PresentationSystemGroup as ECSPresentationSystemGroup,
  SimulationSystemGroup as ECSSimulationSystemGroup
} from './src/SystemGroups'
export * from './src/SystemState'

// 导出查询功能
export * from './src/QueryFunctions'

// 导出ECS状态
export * from './src/ECSState'

// 导出时间相关
export * from './src/Timer'

// 导出教育组件
export * from './src/education'

// TODO: 以下模块将在后续批次中实现
// export * from './src/network/DataReader'
// export * from './src/network/DataWriter'
// export * from './src/network/EntityNetworkState'
// export * from './src/network/IncomingActionSystem'
// export * from './src/network/IncomingNetworkSystem'
// export * from './src/network/NetworkObjectComponent'
// export * from './src/network/NetworkSerializationState'
// export * from './src/network/OutgoingActionSystem'
// export * from './src/network/OutgoingNetworkSystem'
// export * from './src/network/RingBuffer'
// export * from './src/Layers'
// export * from './src/UUIDComponent'
// export * from './src/schemas/JSONSchemas'
// export * from './src/schemas/JSONSchemaTypes'
// export * from './src/schemas/JSONSchemaUtils'
