/**
 * DL-Engine Ollama客户端
 * 管理与Ollama服务的连接和模型调用
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { defineState, getMutableState, getState } from '@dl-engine/engine-state'

/**
 * Ollama模型信息
 */
export interface OllamaModel {
  /** 模型名称 */
  name: string
  
  /** 模型大小 */
  size: number
  
  /** 修改时间 */
  modified_at: string
  
  /** 模型详情 */
  details?: {
    format: string
    family: string
    families: string[]
    parameter_size: string
    quantization_level: string
  }
}

/**
 * Ollama生成请求
 */
export interface OllamaGenerateRequest {
  /** 模型名称 */
  model: string
  
  /** 提示文本 */
  prompt: string
  
  /** 系统提示 */
  system?: string
  
  /** 模板 */
  template?: string
  
  /** 上下文 */
  context?: number[]
  
  /** 流式响应 */
  stream?: boolean
  
  /** 原始响应 */
  raw?: boolean
  
  /** 格式 */
  format?: 'json'
  
  /** 选项 */
  options?: {
    /** 温度 */
    temperature?: number
    
    /** top_p */
    top_p?: number
    
    /** top_k */
    top_k?: number
    
    /** 重复惩罚 */
    repeat_penalty?: number
    
    /** 种子 */
    seed?: number
    
    /** 最大令牌数 */
    num_predict?: number
    
    /** 停止词 */
    stop?: string[]
  }
}

/**
 * Ollama生成响应
 */
export interface OllamaGenerateResponse {
  /** 模型名称 */
  model: string
  
  /** 创建时间 */
  created_at: string
  
  /** 响应文本 */
  response: string
  
  /** 是否完成 */
  done: boolean
  
  /** 上下文 */
  context?: number[]
  
  /** 总持续时间 */
  total_duration?: number
  
  /** 加载持续时间 */
  load_duration?: number
  
  /** 提示评估计数 */
  prompt_eval_count?: number
  
  /** 提示评估持续时间 */
  prompt_eval_duration?: number
  
  /** 评估计数 */
  eval_count?: number
  
  /** 评估持续时间 */
  eval_duration?: number
}

/**
 * Ollama聊天消息
 */
export interface OllamaChatMessage {
  /** 角色 */
  role: 'system' | 'user' | 'assistant'
  
  /** 内容 */
  content: string
  
  /** 图片（base64编码） */
  images?: string[]
}

/**
 * Ollama聊天请求
 */
export interface OllamaChatRequest {
  /** 模型名称 */
  model: string
  
  /** 消息列表 */
  messages: OllamaChatMessage[]
  
  /** 流式响应 */
  stream?: boolean
  
  /** 格式 */
  format?: 'json'
  
  /** 选项 */
  options?: OllamaGenerateRequest['options']
}

/**
 * Ollama聊天响应
 */
export interface OllamaChatResponse {
  /** 模型名称 */
  model: string
  
  /** 创建时间 */
  created_at: string
  
  /** 消息 */
  message: OllamaChatMessage
  
  /** 是否完成 */
  done: boolean
  
  /** 总持续时间 */
  total_duration?: number
  
  /** 加载持续时间 */
  load_duration?: number
  
  /** 提示评估计数 */
  prompt_eval_count?: number
  
  /** 提示评估持续时间 */
  prompt_eval_duration?: number
  
  /** 评估计数 */
  eval_count?: number
  
  /** 评估持续时间 */
  eval_duration?: number
}

/**
 * Ollama嵌入请求
 */
export interface OllamaEmbedRequest {
  /** 模型名称 */
  model: string
  
  /** 提示文本 */
  prompt: string
  
  /** 选项 */
  options?: {
    /** 温度 */
    temperature?: number
  }
}

/**
 * Ollama嵌入响应
 */
export interface OllamaEmbedResponse {
  /** 嵌入向量 */
  embedding: number[]
}

/**
 * Ollama配置
 */
export interface OllamaConfig {
  /** 服务器URL */
  baseURL: string
  
  /** 请求超时时间 */
  timeout: number
  
  /** 默认模型 */
  defaultModel: string
  
  /** 最大重试次数 */
  maxRetries: number
  
  /** 重试延迟 */
  retryDelay: number
}

/**
 * 默认Ollama配置
 */
const defaultOllamaConfig: OllamaConfig = {
  baseURL: 'http://localhost:11434',
  timeout: 30000,
  defaultModel: 'llama2',
  maxRetries: 3,
  retryDelay: 1000
}

/**
 * Ollama状态
 */
export interface OllamaState {
  /** 配置 */
  config: OllamaConfig

  /** 是否已连接 */
  connected: boolean

  /** 可用模型列表 */
  models: OllamaModel[]

  /** 当前加载的模型 */
  loadedModels: string[]

  /** 错误信息 */
  error: string | null
  
  /** 统计信息 */
  stats: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    totalTokens: number
    averageResponseTime: number
  }
}

/**
 * Ollama状态定义
 */
export const OllamaState = defineState({
  name: 'DLEngine.Ollama',
  initial: (): OllamaState => ({
    config: defaultOllamaConfig,
    connected: false,
    models: [],
    loadedModels: [],
    error: null,
    stats: {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokens: 0,
      averageResponseTime: 0
    }
  })
})

/**
 * Ollama客户端
 */
export class OllamaClient {
  private static instance: OllamaClient | null = null
  private axiosInstance: AxiosInstance
  private defaultModel: string = 'llama2'

  // 内部存储
  private connected: boolean = false
  private models: OllamaModel[] = []
  private loadedModels: Set<string> = new Set()
  private error: string | null = null

  /**
   * 获取单例实例
   */
  static getInstance(): OllamaClient {
    if (!OllamaClient.instance) {
      OllamaClient.instance = new OllamaClient()
    }
    return OllamaClient.instance
  }
  
  constructor() {
    const config = getState(OllamaState).config
    
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    // 设置请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const state = getMutableState(OllamaState)
        state.stats.totalRequests.set(state.stats.totalRequests.value + 1)
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 设置响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        const state = getMutableState(OllamaState)
        state.stats.successfulRequests.set(state.stats.successfulRequests.value + 1)
        return response
      },
      (error) => {
        const state = getMutableState(OllamaState)
        state.stats.failedRequests.set(state.stats.failedRequests.value + 1)
        state.error.set(error.message)

        // 更新内部存储
        this.error = error.message

        return Promise.reject(error)
      }
    )
  }
  
  /**
   * 检查连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.axiosInstance.get('/api/tags')

      // 更新内部存储
      this.connected = true
      this.error = null

      // 更新状态
      const state = getMutableState(OllamaState)
      state.connected.set(true)
      state.error.set(null)

      return true
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Connection failed'

      // 更新内部存储
      this.connected = false
      this.error = errorMessage

      // 更新状态
      const state = getMutableState(OllamaState)
      state.connected.set(false)
      state.error.set(errorMessage)

      return false
    }
  }
  
  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<OllamaModel[]> {
    try {
      const response: AxiosResponse<{ models: OllamaModel[] }> = await this.axiosInstance.get('/api/tags')
      const models = response.data.models

      // 更新内部存储
      this.models = models

      // 更新状态
      getMutableState(OllamaState).models.set(models)

      return models

    } catch (error) {
      console.error('Failed to get models:', error)
      throw error
    }
  }
  
  /**
   * 生成文本
   */
  async generate(request: OllamaGenerateRequest): Promise<OllamaGenerateResponse> {
    const startTime = performance.now()
    
    try {
      const response: AxiosResponse<OllamaGenerateResponse> = await this.axiosInstance.post('/api/generate', request)
      
      // 更新统计信息
      const endTime = performance.now()
      const responseTime = endTime - startTime
      const state = getMutableState(OllamaState)

      const currentAvg = state.stats.averageResponseTime.value
      const totalRequests = state.stats.totalRequests.value
      const newAvg = (currentAvg * (totalRequests - 1) + responseTime) / totalRequests

      // 更新统计信息
      state.stats.averageResponseTime.set(newAvg)
      state.stats.totalTokens.set(state.stats.totalTokens.value + (response.data.eval_count || 0))
      
      return response.data
      
    } catch (error) {
      console.error('Failed to generate text:', error)
      throw error
    }
  }
  
  /**
   * 聊天对话
   */
  async chat(request: OllamaChatRequest): Promise<OllamaChatResponse> {
    const startTime = performance.now()
    
    try {
      const response: AxiosResponse<OllamaChatResponse> = await this.axiosInstance.post('/api/chat', request)
      
      // 更新统计信息
      const endTime = performance.now()
      const responseTime = endTime - startTime
      const state = getMutableState(OllamaState)

      const currentAvg = state.stats.averageResponseTime.value
      const totalRequests = state.stats.totalRequests.value
      const newAvg = (currentAvg * (totalRequests - 1) + responseTime) / totalRequests

      // 更新统计信息
      state.stats.averageResponseTime.set(newAvg)
      state.stats.totalTokens.set(state.stats.totalTokens.value + (response.data.eval_count || 0))
      
      return response.data
      
    } catch (error) {
      console.error('Failed to chat:', error)
      throw error
    }
  }
  
  /**
   * 生成嵌入向量
   */
  async embeddings(request: OllamaEmbedRequest): Promise<OllamaEmbedResponse> {
    try {
      const response: AxiosResponse<OllamaEmbedResponse> = await this.axiosInstance.post('/api/embeddings', request)
      return response.data
      
    } catch (error) {
      console.error('Failed to generate embeddings:', error)
      throw error
    }
  }
  
  /**
   * 拉取模型
   */
  async pullModel(model: string, onProgress?: (progress: number) => void): Promise<void> {
    try {
      const response = await this.axiosInstance.post('/api/pull', { name: model }, {
        responseType: 'stream'
      })
      
      // TODO: 处理流式响应和进度回调
      // 这里需要处理流式数据来获取下载进度

      // 更新内部存储
      this.loadedModels.add(model)

      // 更新状态
      const state = getMutableState(OllamaState)
      const currentModels = state.loadedModels.value
      if (!currentModels.includes(model)) {
        state.loadedModels.set([...currentModels, model])
      }
      
    } catch (error) {
      console.error('Failed to pull model:', error)
      throw error
    }
  }
  
  /**
   * 删除模型
   */
  async deleteModel(model: string): Promise<void> {
    try {
      await this.axiosInstance.delete('/api/delete', { data: { name: model } })

      // 更新内部存储
      this.loadedModels.delete(model)

      // 更新状态
      const state = getMutableState(OllamaState)
      const currentModels = state.loadedModels.value
      state.loadedModels.set(currentModels.filter(m => m !== model))

    } catch (error) {
      console.error('Failed to delete model:', error)
      throw error
    }
  }
  
  /**
   * 更新配置
   */
  updateConfig(config: Partial<OllamaConfig>): void {
    // 更新状态
    const state = getMutableState(OllamaState)
    const currentConfig = state.config.value
    const newConfig = { ...currentConfig, ...config }
    state.config.set(newConfig)

    // 更新axios实例配置
    if (config.baseURL) {
      this.axiosInstance.defaults.baseURL = config.baseURL
    }

    if (config.timeout) {
      this.axiosInstance.defaults.timeout = config.timeout
    }
  }
  
  /**
   * 获取统计信息
   */
  getStats(): OllamaState['stats'] {
    return getState(OllamaState).stats
  }
}

/**
 * 教育AI助手
 */
export class EducationAIAssistant {
  private static instance: EducationAIAssistant | null = null
  private ollamaClient: OllamaClient
  private defaultModel: string = 'llama3.2:3b'

  /**
   * 获取单例实例
   */
  static getInstance(): EducationAIAssistant {
    if (!EducationAIAssistant.instance) {
      EducationAIAssistant.instance = new EducationAIAssistant()
    }
    return EducationAIAssistant.instance
  }

  constructor() {
    this.ollamaClient = OllamaClient.getInstance()
  }

  /**
   * 生成学习内容解释
   */
  async explainConcept(
    concept: string,
    level: 'beginner' | 'intermediate' | 'advanced' = 'intermediate',
    language: string = 'zh-CN'
  ): Promise<string> {
    const systemPrompt = this.getEducationSystemPrompt(language, level)

    const prompt = `请解释以下概念：${concept}

要求：
1. 使用${level === 'beginner' ? '简单易懂' : level === 'intermediate' ? '适中难度' : '深入专业'}的语言
2. 提供具体的例子
3. 如果适用，包含实际应用场景
4. 保持解释简洁但全面`

    try {
      const response = await this.ollamaClient.generate({
        model: this.defaultModel,
        prompt,
        system: systemPrompt,
        options: {
          temperature: 0.7,
          num_predict: 500
        }
      })

      return response.response
    } catch (error) {
      console.error('Failed to explain concept:', error)
      throw new Error('无法生成概念解释')
    }
  }

  /**
   * 生成练习题
   */
  async generateQuestions(
    topic: string,
    questionType: 'multiple_choice' | 'true_false' | 'short_answer' | 'essay',
    count: number = 5,
    difficulty: 'easy' | 'medium' | 'hard' = 'medium'
  ): Promise<any[]> {
    const systemPrompt = `你是一个专业的教育内容生成器。请根据给定的主题生成高质量的练习题。`

    const prompt = `请为主题"${topic}"生成${count}道${this.getQuestionTypeText(questionType)}题目。

要求：
1. 难度等级：${difficulty}
2. 题目应该测试对概念的理解，而不仅仅是记忆
3. 每道题都应该有明确的正确答案
4. 如果是选择题，请提供4个选项，其中只有1个正确
5. 请以JSON格式返回，包含题目、选项（如适用）、正确答案和解释

JSON格式示例：
{
  "questions": [
    {
      "question": "题目内容",
      "options": ["A选项", "B选项", "C选项", "D选项"], // 仅选择题需要
      "correct_answer": "正确答案",
      "explanation": "答案解释"
    }
  ]
}`

    try {
      const response = await this.ollamaClient.generate({
        model: this.defaultModel,
        prompt,
        system: systemPrompt,
        options: {
          temperature: 0.8,
          num_predict: 1000
        },
        format: 'json'
      })

      const result = JSON.parse(response.response)
      return result.questions || []
    } catch (error) {
      console.error('Failed to generate questions:', error)
      throw new Error('无法生成练习题')
    }
  }

  /**
   * 评估学生答案
   */
  async evaluateAnswer(
    question: string,
    studentAnswer: string,
    correctAnswer: string,
    provideFeedback: boolean = true
  ): Promise<{
    isCorrect: boolean
    score: number
    feedback?: string
    suggestions?: string[]
  }> {
    const systemPrompt = `你是一个专业的教育评估专家。请客观、公正地评估学生的答案。`

    const prompt = `请评估以下学生答案：

题目：${question}
标准答案：${correctAnswer}
学生答案：${studentAnswer}

请提供：
1. 答案是否正确（true/false）
2. 分数（0-100）
3. ${provideFeedback ? '详细的反馈意见' : ''}
4. ${provideFeedback ? '改进建议' : ''}

请以JSON格式返回：
{
  "isCorrect": boolean,
  "score": number,
  "feedback": "反馈内容",
  "suggestions": ["建议1", "建议2"]
}`

    try {
      const response = await this.ollamaClient.generate({
        model: this.defaultModel,
        prompt,
        system: systemPrompt,
        options: {
          temperature: 0.3,
          num_predict: 400
        },
        format: 'json'
      })

      return JSON.parse(response.response)
    } catch (error) {
      console.error('Failed to evaluate answer:', error)
      throw new Error('无法评估答案')
    }
  }

  /**
   * 生成个性化学习建议
   */
  async generateLearningRecommendations(
    studentProfile: {
      level: string
      strengths: string[]
      weaknesses: string[]
      interests: string[]
      learningStyle: string
    },
    currentTopic: string
  ): Promise<{
    recommendations: string[]
    nextTopics: string[]
    studyPlan: string
  }> {
    const systemPrompt = `你是一个专业的个性化学习顾问。请根据学生的学习档案提供个性化的学习建议。`

    const prompt = `请为以下学生档案生成个性化学习建议：

学生水平：${studentProfile.level}
优势领域：${studentProfile.strengths.join(', ')}
薄弱环节：${studentProfile.weaknesses.join(', ')}
兴趣爱好：${studentProfile.interests.join(', ')}
学习风格：${studentProfile.learningStyle}
当前学习主题：${currentTopic}

请提供：
1. 具体的学习建议（3-5条）
2. 推荐的下一步学习主题（3-5个）
3. 个性化的学习计划

请以JSON格式返回：
{
  "recommendations": ["建议1", "建议2", "建议3"],
  "nextTopics": ["主题1", "主题2", "主题3"],
  "studyPlan": "详细的学习计划"
}`

    try {
      const response = await this.ollamaClient.generate({
        model: this.defaultModel,
        prompt,
        system: systemPrompt,
        options: {
          temperature: 0.7,
          num_predict: 600
        },
        format: 'json'
      })

      return JSON.parse(response.response)
    } catch (error) {
      console.error('Failed to generate recommendations:', error)
      throw new Error('无法生成学习建议')
    }
  }

  /**
   * 智能问答
   */
  async answerQuestion(
    question: string,
    context?: string,
    language: string = 'zh-CN'
  ): Promise<string> {
    const systemPrompt = this.getEducationSystemPrompt(language, 'intermediate')

    const prompt = context
      ? `基于以下上下文回答问题：

上下文：${context}

问题：${question}

请提供准确、有帮助的答案。`
      : `请回答以下问题：${question}`

    try {
      const response = await this.ollamaClient.generate({
        model: this.defaultModel,
        prompt,
        system: systemPrompt,
        options: {
          temperature: 0.6,
          num_predict: 400
        }
      })

      return response.response
    } catch (error) {
      console.error('Failed to answer question:', error)
      throw new Error('无法回答问题')
    }
  }

  /**
   * 获取教育系统提示
   */
  private getEducationSystemPrompt(language: string, level: string): string {
    const levelText = {
      'beginner': '初学者',
      'intermediate': '中等水平',
      'advanced': '高级'
    }[level] || '中等水平'

    return `你是一个专业的教育AI助手，专门帮助${levelText}的学习者。请遵循以下原则：

1. 使用${language === 'zh-CN' ? '中文' : '英文'}回答
2. 保持耐心和鼓励的语调
3. 提供准确、有用的信息
4. 根据学习者水平调整解释的复杂度
5. 鼓励主动思考和探索
6. 如果不确定答案，请诚实说明
7. 提供相关的学习资源建议`
  }

  /**
   * 获取题目类型文本
   */
  private getQuestionTypeText(type: string): string {
    const typeMap: Record<string, string> = {
      'multiple_choice': '选择',
      'true_false': '判断',
      'short_answer': '简答',
      'essay': '论述'
    }
    return typeMap[type] || '选择'
  }

  /**
   * 设置默认模型
   */
  setDefaultModel(model: string): void {
    this.defaultModel = model
  }

  /**
   * 获取默认模型
   */
  getDefaultModel(): string {
    return this.defaultModel
  }
}
