"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _typeof3 = require("@babel/runtime/helpers/typeof");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.animationStatistics = void 0;
exports.default = useStyleRegister;
exports.extractStyle = extractStyle;
exports.normalizeStyle = normalizeStyle;
exports.parseStyle = void 0;
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectSpread3 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _typeof2 = _interopRequireDefault(require("@babel/runtime/helpers/typeof"));
var React = _interopRequireWildcard(require("react"));
var _dynamicCSS = require("rc-util/lib/Dom/dynamicCSS");
var _canUseDom = _interopRequireDefault(require("rc-util/lib/Dom/canUseDom"));
var _hash = _interopRequireDefault(require("@emotion/hash"));
var _unitless = _interopRequireDefault(require("@emotion/unitless"));
var _stylis = require("stylis");
var _useGlobalCache3 = _interopRequireDefault(require("./useGlobalCache"));
var _StyleContext = _interopRequireWildcard(require("../StyleContext"));
var _util = require("../util");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || _typeof3(obj) !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
// @ts-ignore

var isClientSide = (0, _canUseDom.default)();
var SKIP_CHECK = '_skip_check_';
// ============================================================================
// ==                                 Parser                                 ==
// ============================================================================
// Preprocessor style content to browser support one
function normalizeStyle(styleStr) {
  var serialized = (0, _stylis.serialize)((0, _stylis.compile)(styleStr), _stylis.stringify);
  return serialized.replace(/\{%%%\:[^;];}/g, ';');
}
function isCompoundCSSProperty(value) {
  return (0, _typeof2.default)(value) === 'object' && value && SKIP_CHECK in value;
}
var animationStatistics = {};
// 注入 hash 值
exports.animationStatistics = animationStatistics;
function injectSelectorHash(key, hashId, hashPriority) {
  if (!hashId) {
    return key;
  }
  var hashClassName = ".".concat(hashId);
  var hashSelector = hashPriority === 'low' ? ":where(".concat(hashClassName, ")") : hashClassName;
  // 注入 hashId
  var keys = key.split(',').map(function (k) {
    var _firstPath$match;
    var fullPath = k.trim().split(/\s+/);
    // 如果 Selector 第一个是 HTML Element，那我们就插到它的后面。反之，就插到最前面。
    var firstPath = fullPath[0] || '';
    var htmlElement = ((_firstPath$match = firstPath.match(/^\w+/)) === null || _firstPath$match === void 0 ? void 0 : _firstPath$match[0]) || '';
    firstPath = "".concat(htmlElement).concat(hashSelector).concat(firstPath.slice(htmlElement.length));
    return [firstPath].concat((0, _toConsumableArray2.default)(fullPath.slice(1))).join(' ');
  });
  return keys.join(',');
}
// Parse CSSObject to style content
var parseStyle = function parseStyle(interpolation) {
  var config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {
      root: true
    },
    root = _ref.root,
    injectHash = _ref.injectHash;
  var hashId = config.hashId,
    layer = config.layer,
    path = config.path,
    hashPriority = config.hashPriority;
  var styleStr = '';
  function parseKeyframes(keyframes) {
    if (animationStatistics[keyframes.getName(hashId)]) {
      return '';
    }
    animationStatistics[keyframes.getName(hashId)] = true;
    return "@keyframes ".concat(keyframes.getName(hashId)).concat(parseStyle(keyframes.style, config, {
      root: false
    }));
  }
  function flattenList(list) {
    var fullList = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
    list.forEach(function (item) {
      if (Array.isArray(item)) {
        flattenList(item, fullList);
      } else if (item) {
        fullList.push(item);
      }
    });
    return fullList;
  }
  var flattenStyleList = flattenList(Array.isArray(interpolation) ? interpolation : [interpolation]);
  flattenStyleList.forEach(function (originStyle) {
    // Only root level can use raw string
    var style = typeof originStyle === 'string' && !root ? {} : originStyle;
    if (typeof style === 'string') {
      styleStr += "".concat(style, "\n");
    } else if (style._keyframe) {
      // Keyframe
      styleStr += parseKeyframes(style);
    } else {
      // Normal CSSObject
      Object.keys(style).forEach(function (key) {
        var value = style[key];
        if ((0, _typeof2.default)(value) === 'object' && value && (key !== 'animationName' || !value._keyframe) && !isCompoundCSSProperty(value)) {
          var subInjectHash = false;
          // 当成嵌套对象来处理
          var mergedKey = key.trim();
          // Whether treat child as root. In most case it is false.
          var nextRoot = false;
          // 拆分多个选择器
          if ((root || injectHash) && hashId) {
            if (mergedKey.startsWith('@')) {
              // 略过媒体查询，交给子节点继续插入 hashId
              subInjectHash = true;
            } else {
              // 注入 hashId
              mergedKey = injectSelectorHash(key, hashId, hashPriority);
            }
          } else if (root && !hashId && (mergedKey === '&' || mergedKey === '')) {
            // In case of `{ '&': { a: { color: 'red' } } }` or `{ '': { a: { color: 'red' } } }` without hashId,
            // we will get `&{a:{color:red;}}` or `{a:{color:red;}}` string for stylis to compile.
            // But it does not conform to stylis syntax,
            // and finally we will get `{color:red;}` as css, which is wrong.
            // So we need to remove key in root, and treat child `{ a: { color: 'red' } }` as root.
            mergedKey = '';
            nextRoot = true;
          }
          styleStr += "".concat(mergedKey).concat(parseStyle(value, (0, _objectSpread3.default)((0, _objectSpread3.default)({}, config), {}, {
            path: "".concat(path, " -> ").concat(mergedKey)
          }), {
            root: nextRoot,
            injectHash: subInjectHash
          }));
        } else {
          var _value$value;
          var actualValue = (_value$value = value === null || value === void 0 ? void 0 : value.value) !== null && _value$value !== void 0 ? _value$value : value;
          if (process.env.NODE_ENV !== 'production' && ((0, _typeof2.default)(value) !== 'object' || !(value === null || value === void 0 ? void 0 : value[SKIP_CHECK]))) {
            (0, _util.styleValidate)(key, actualValue, {
              path: path,
              hashId: hashId
            });
          }
          // 如果是样式则直接插入
          var styleName = key.replace(/[A-Z]/g, function (match) {
            return "-".concat(match.toLowerCase());
          });
          // Auto suffix with px
          var formatValue = actualValue;
          if (!_unitless.default[key] && typeof formatValue === 'number' && formatValue !== 0) {
            formatValue = "".concat(formatValue, "px");
          }
          // handle animationName & Keyframe value
          if (key === 'animationName' && (value === null || value === void 0 ? void 0 : value._keyframe)) {
            styleStr += parseKeyframes(value);
            formatValue = value.getName(hashId);
          }
          styleStr += "".concat(styleName, ":").concat(formatValue, ";");
        }
      });
    }
  });
  if (!root) {
    styleStr = "{".concat(styleStr, "}");
  } else if (layer && (0, _util.supportLayer)()) {
    var layerCells = layer.split(',');
    var layerName = layerCells[layerCells.length - 1].trim();
    styleStr = "@layer ".concat(layerName, " {").concat(styleStr, "}");
    // Order of layer if needed
    if (layerCells.length > 1) {
      // zombieJ: stylis do not support layer order, so we need to handle it manually.
      styleStr = "@layer ".concat(layer, "{%%%:%}").concat(styleStr);
    }
  }
  return styleStr;
};
// ============================================================================
// ==                                Register                                ==
// ============================================================================
exports.parseStyle = parseStyle;
function uniqueHash(path, styleStr) {
  return (0, _hash.default)("".concat(path.join('%')).concat(styleStr));
}
function Empty() {
  return null;
}
/**
 * Register a style to the global style sheet.
 */
function useStyleRegister(info, styleFn) {
  var token = info.token,
    path = info.path,
    hashId = info.hashId,
    layer = info.layer;
  var _React$useContext = React.useContext(_StyleContext.default),
    autoClear = _React$useContext.autoClear,
    mock = _React$useContext.mock,
    defaultCache = _React$useContext.defaultCache,
    hashPriority = _React$useContext.hashPriority;
  var tokenKey = token._tokenKey;
  var fullPath = [tokenKey].concat((0, _toConsumableArray2.default)(path));
  // Check if need insert style
  var isMergedClientSide = isClientSide;
  if (process.env.NODE_ENV !== 'production' && mock !== undefined) {
    isMergedClientSide = mock === 'client';
  }
  var _useGlobalCache = (0, _useGlobalCache3.default)('style', fullPath,
    // Create cache if needed
    function () {
      var styleObj = styleFn();
      var styleStr = normalizeStyle(parseStyle(styleObj, {
        hashId: hashId,
        hashPriority: hashPriority,
        layer: layer,
        path: path.join('-')
      }));
      var styleId = uniqueHash(fullPath, styleStr);
      // Clear animation statistics
      exports.animationStatistics = animationStatistics = {};
      if (isMergedClientSide) {
        var style = (0, _dynamicCSS.updateCSS)(styleStr, styleId, {
          mark: _StyleContext.ATTR_MARK,
          prepend: 'queue'
        });
        style[_StyleContext.CSS_IN_JS_INSTANCE] = _StyleContext.CSS_IN_JS_INSTANCE_ID;
        // Used for `useCacheToken` to remove on batch when token removed
        style.setAttribute(_StyleContext.ATTR_TOKEN, tokenKey);
        // Dev usage to find which cache path made this easily
        if (process.env.NODE_ENV !== 'production') {
          style.setAttribute(_StyleContext.ATTR_DEV_CACHE_PATH, fullPath.join('|'));
        }
      }
      return [styleStr, tokenKey, styleId];
    },
    // Remove cache if no need
    function (_ref2, fromHMR) {
      var _ref3 = (0, _slicedToArray2.default)(_ref2, 3),
        styleId = _ref3[2];
      if ((fromHMR || autoClear) && isClientSide) {
        (0, _dynamicCSS.removeCSS)(styleId, {
          mark: _StyleContext.ATTR_MARK
        });
      }
    }),
    _useGlobalCache2 = (0, _slicedToArray2.default)(_useGlobalCache, 3),
    cachedStyleStr = _useGlobalCache2[0],
    cachedTokenKey = _useGlobalCache2[1],
    cachedStyleId = _useGlobalCache2[2];
  return function (node) {
    var styleNode;
    if (isMergedClientSide || !defaultCache) {
      styleNode = /*#__PURE__*/React.createElement(Empty, null);
    } else {
      var _objectSpread2;
      styleNode = /*#__PURE__*/React.createElement("style", (0, _objectSpread3.default)((0, _objectSpread3.default)({}, (_objectSpread2 = {}, (0, _defineProperty2.default)(_objectSpread2, _StyleContext.ATTR_TOKEN, cachedTokenKey), (0, _defineProperty2.default)(_objectSpread2, _StyleContext.ATTR_MARK, cachedStyleId), _objectSpread2)), {}, {
        dangerouslySetInnerHTML: {
          __html: cachedStyleStr
        }
      }));
    }
    return /*#__PURE__*/React.createElement(React.Fragment, null, styleNode, node);
  };
}
// ============================================================================
// ==                                  SSR                                   ==
// ============================================================================
function extractStyle(cache) {
  // prefix with `style` is used for `useStyleRegister` to cache style context
  var styleKeys = Array.from(cache.cache.keys()).filter(function (key) {
    return key.startsWith('style%');
  });
  // const tokenStyles: Record<string, string[]> = {};
  var styleText = '';
  styleKeys.forEach(function (key) {
    var _cache$cache$get$ = (0, _slicedToArray2.default)(cache.cache.get(key)[1], 3),
      styleStr = _cache$cache$get$[0],
      tokenKey = _cache$cache$get$[1],
      styleId = _cache$cache$get$[2];
    styleText += "<style ".concat(_StyleContext.ATTR_TOKEN, "=\"").concat(tokenKey, "\" ").concat(_StyleContext.ATTR_MARK, "=\"").concat(styleId, "\">").concat(styleStr, "</style>");
  });
  return styleText;
}