# DL-Engine 推荐系统

DL-Engine 推荐系统是一个完整的智能推荐解决方案，专为教育学习场景设计。它提供多种推荐算法，包括基于内容的推荐、协同过滤、混合推荐等，能够为学习者提供个性化的学习内容推荐。

## 功能特性

### 🎯 多种推荐算法
- **基于内容的推荐**: 根据内容特征和用户偏好进行推荐
- **协同过滤**: 基于用户行为相似性进行推荐
- **混合推荐**: 结合多种算法的优势
- **基于知识的推荐**: 利用领域知识和规则
- **热门推荐**: 基于全局热门度

### 📊 智能分析
- 用户学习档案分析
- 内容相似度计算
- 用户行为模式识别
- 推荐效果评估

### ⚡ 高性能
- 智能缓存机制
- 异步处理
- 批量操作支持
- 实时推荐更新

### 🔧 易于集成
- 统一的服务接口
- 灵活的配置选项
- 完整的类型定义
- 详细的使用示例

## 快速开始

### 安装和导入

```typescript
import { 
  RecommendationService,
  ContentRecommender,
  CollaborativeFiltering,
  RecommendationSystemFactory
} from '@dl-engine/ai/recommendations'
```

### 基本使用

```typescript
// 1. 创建推荐服务
const recommendationService = new RecommendationService({
  enableContentRecommendation: true,
  enableCollaborativeFiltering: true,
  defaultLimit: 10,
  cacheTimeout: 300
})

// 2. 添加学习内容
const content = {
  id: 'content_001',
  title: 'JavaScript 基础教程',
  description: '学习 JavaScript 编程语言的基础知识',
  type: 'video',
  difficulty: 2,
  topics: ['javascript', 'programming'],
  skills: ['javascript'],
  estimatedDuration: 120,
  prerequisites: [],
  learningObjectives: ['掌握 JavaScript 基本语法'],
  qualityScore: 4.5,
  popularity: 85,
  createdAt: new Date(),
  updatedAt: new Date()
}

recommendationService.addContent(content)

// 3. 创建用户学习档案
const userProfile = {
  userId: 'user_001',
  skillLevels: { 'javascript': 2, 'programming': 3 },
  preferences: {
    contentTypes: ['video', 'interactive'],
    difficultyRange: [2, 4],
    sessionDuration: 120,
    learningStyle: 'visual'
  },
  learningHistory: {
    completedContent: [],
    inProgressContent: [],
    bookmarkedContent: [],
    skippedContent: []
  },
  learningGoals: ['学习前端开发'],
  lastActiveAt: new Date()
}

recommendationService.updateUserProfile(userProfile)

// 4. 获取推荐
const recommendations = await recommendationService.getRecommendations({
  userId: 'user_001',
  type: 'content',
  limit: 5,
  context: {
    learningGoals: ['学习前端开发'],
    difficultyPreference: 'adaptive'
  }
})

console.log('推荐结果:', recommendations.recommendations)
```

### 记录用户行为

```typescript
// 记录用户行为以改进推荐
const behavior = {
  userId: 'user_001',
  itemId: 'content_001',
  actionType: 'complete',
  value: 5,
  timestamp: new Date(),
  context: {
    deviceType: 'desktop',
    sessionId: 'session_001'
  }
}

await recommendationService.recordUserBehavior(behavior)
```

## 核心组件

### RecommendationService
主要的推荐服务类，提供统一的推荐接口。

**主要方法:**
- `getRecommendations()`: 获取推荐
- `recordUserBehavior()`: 记录用户行为
- `addContent()`: 添加内容
- `updateUserProfile()`: 更新用户档案
- `explainRecommendation()`: 获取推荐解释

### ContentRecommender
基于内容的推荐器，专门处理学习内容推荐。

**特性:**
- 内容相似度计算
- 用户偏好匹配
- 难度适配
- 前置要求检查

### CollaborativeFiltering
协同过滤推荐器，基于用户行为相似性。

**支持的相似度算法:**
- 余弦相似度
- 皮尔逊相关系数
- 杰卡德相似度
- 欧几里得距离

**推荐类型:**
- 基于用户的协同过滤
- 基于项目的协同过滤
- 混合协同过滤

## 配置选项

### RecommendationServiceConfig

```typescript
interface RecommendationServiceConfig {
  enableContentRecommendation: boolean  // 启用内容推荐
  enableCollaborativeFiltering: boolean // 启用协同过滤
  defaultLimit: number                  // 默认推荐数量
  cacheTimeout: number                  // 缓存超时时间（秒）
  enableLogging: boolean                // 启用日志记录
}
```

### RecommendationConfig

```typescript
interface RecommendationConfig {
  limit: number                                    // 推荐数量限制
  enabledAlgorithms: RecommendationAlgorithm[]     // 启用的算法
  algorithmWeights: Record<string, number>         // 算法权重
  minConfidence: number                            // 最小置信度阈值
  diversityFactor: number                          // 多样性因子
  includeExplanation: boolean                      // 是否包含解释
  cacheTimeout: number                             // 缓存时间
}
```

## 推荐算法详解

### 1. 基于内容的推荐
根据内容的特征（主题、技能、难度等）和用户的偏好进行匹配推荐。

**优势:**
- 不需要大量用户数据
- 推荐结果可解释性强
- 能够推荐新内容

**适用场景:**
- 新用户推荐
- 内容冷启动
- 个性化学习路径

### 2. 协同过滤
基于"相似用户喜欢相似内容"的假设进行推荐。

**优势:**
- 能够发现用户潜在兴趣
- 推荐质量随数据增长而提升
- 支持跨领域推荐

**适用场景:**
- 有足够用户行为数据
- 探索性推荐
- 社交化学习

### 3. 混合推荐
结合多种算法的优势，提供更准确和多样化的推荐。

**策略:**
- 加权组合
- 切换策略
- 分层推荐

## 性能优化

### 缓存策略
- 推荐结果缓存
- 相似度计算缓存
- 用户档案缓存

### 批量处理
- 批量用户行为记录
- 批量相似度计算
- 批量推荐生成

### 异步处理
- 非阻塞推荐生成
- 后台模型更新
- 异步日志记录

## 监控和分析

### 推荐效果指标
- 点击率 (CTR)
- 转化率
- 用户满意度
- 多样性指标

### 系统性能指标
- 响应时间
- 缓存命中率
- 内存使用量
- 并发处理能力

## 最佳实践

### 1. 数据质量
- 确保内容元数据完整性
- 定期清理无效用户行为
- 维护用户档案的时效性

### 2. 算法选择
- 根据数据量选择合适算法
- 新用户使用基于内容的推荐
- 活跃用户使用协同过滤

### 3. 参数调优
- 根据业务场景调整权重
- 定期评估推荐效果
- A/B测试验证改进

### 4. 用户体验
- 提供推荐解释
- 支持用户反馈
- 保持推荐多样性

## 扩展开发

### 自定义推荐算法

```typescript
class CustomRecommendationEngine extends BaseRecommendationEngine {
  async generateRecommendations(context: RecommendationContext): Promise<RecommendationResult> {
    // 实现自定义推荐逻辑
    return {
      items: [],
      total: 0,
      algorithmUsage: {},
      generatedAt: new Date(),
      processingTime: 0,
      fromCache: false
    }
  }
  
  async updateFeedback(userId: Entity, itemId: string, feedback: string): Promise<void> {
    // 实现反馈处理逻辑
  }
  
  async explainRecommendation(userId: Entity, itemId: string): Promise<string> {
    // 实现推荐解释逻辑
    return '自定义推荐解释'
  }
}
```

### 集成外部数据源

```typescript
// 集成外部推荐API
class ExternalRecommendationProvider {
  async getRecommendations(userId: string, limit: number) {
    // 调用外部API
    const response = await fetch(`/api/external/recommendations/${userId}?limit=${limit}`)
    return response.json()
  }
}
```

## 故障排除

### 常见问题

1. **推荐结果为空**
   - 检查用户档案是否存在
   - 确认内容数据是否已添加
   - 验证算法配置是否正确

2. **推荐质量差**
   - 增加用户行为数据
   - 调整算法权重
   - 检查内容元数据质量

3. **性能问题**
   - 启用缓存机制
   - 优化相似度计算
   - 使用批量处理

### 调试工具

```typescript
// 启用详细日志
const service = new RecommendationService({
  enableLogging: true
})

// 获取统计信息
const stats = service.getStatistics()
console.log('推荐系统统计:', stats)

// 获取推荐解释
const explanation = await service.explainRecommendation(userId, itemId)
console.log('推荐解释:', explanation)
```

## 许可证

本推荐系统遵循 DL-Engine 项目的许可证协议。
