import _classCallCheck from "@babel/runtime/helpers/esm/classCallCheck";
import _createClass from "@babel/runtime/helpers/esm/createClass";
var Entity = /*#__PURE__*/function () {
  function Entity() {
    _classCallCheck(this, Entity);
    this.cache = new Map();
  }
  _createClass(Entity, [{
    key: "get",
    value: function get(keys) {
      return this.cache.get(keys.join('%')) || null;
    }
  }, {
    key: "update",
    value: function update(keys, valueFn) {
      var path = keys.join('%');
      var prevValue = this.cache.get(path);
      var nextValue = valueFn(prevValue);
      if (nextValue === null) {
        this.cache.delete(path);
      } else {
        this.cache.set(path, nextValue);
      }
    }
  }]);
  return Entity;
}();
export default Entity;