/**
 * DL-Engine 教育组件模块
 * 导出所有教育场景专用组件
 */

// 学习进度组件
export {
  LearningProgressComponent,
  LearningProgressUtils
} from './LearningProgressComponent'
export type {
  LearningProgressData,
  LearningObjective as ProgressLearningObjective
} from './LearningProgressComponent'

// 互动组件
export * from './InteractiveComponent'

// 评估组件
export * from './AssessmentComponent'

// 知识点组件
export {
  KnowledgePointComponent,
  KnowledgePointUtils
} from './KnowledgePointComponent'
export type {
  KnowledgePointData,
  LearningObjective as KnowledgeLearningObjective
} from './KnowledgePointComponent'

// 学习路径组件
export {
  LearningPathComponent,
  LearningPathUtils
} from './LearningPathComponent'
export type {
  LearningPathData,
  AdaptationStrategy as PathAdaptationStrategy
} from './LearningPathComponent'

// 协作学习组件
export * from './CollaborativeLearningComponent'

// 学习分析组件
export * from './LearningAnalyticsComponent'

// 虚拟实验室组件
export * from './VirtualLabComponent'

// 游戏化学习组件
export {
  GamificationComponent,
  GamificationUtils
} from './GamificationComponent'
export type {
  GamificationData,
  Achievement as GamificationAchievement
} from './GamificationComponent'

// 自适应学习组件
export {
  AdaptiveLearningComponent,
  AdaptiveLearningUtils
} from './AdaptiveLearningComponent'
export type {
  AdaptiveLearningData,
  AdaptationStrategy as AdaptiveLearningStrategy
} from './AdaptiveLearningComponent'

// 学习资源组件
export * from './LearningResourceComponent'

// 教育系统管理器
export * from './EducationSystemManager'

// 教育组件工具函数
export const EducationComponents = {
  LearningProgress: 'LearningProgress',
  Interactive: 'Interactive',
  Assessment: 'Assessment',
  KnowledgePoint: 'KnowledgePoint',
  LearningPath: 'LearningPath',
  CollaborativeLearning: 'CollaborativeLearning',
  LearningAnalytics: 'LearningAnalytics',
  VirtualLab: 'VirtualLab',
  Gamification: 'Gamification',
  AdaptiveLearning: 'AdaptiveLearning',
  LearningResource: 'LearningResource'
} as const

/**
 * 教育组件类型联合
 */
export type EducationComponentType = typeof EducationComponents[keyof typeof EducationComponents]

/**
 * 教育模块初始化
 */
export function initializeEducationComponents(): void {
  console.log('🎓 Education components initialized')
  console.log('Available components:', Object.values(EducationComponents))
}

/**
 * 教育组件统计
 */
export function getEducationComponentStats() {
  return {
    totalComponents: Object.keys(EducationComponents).length,
    components: Object.values(EducationComponents)
  }
}
