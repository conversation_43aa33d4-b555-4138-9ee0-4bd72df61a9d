/**
 * DL-Engine 系统状态管理
 * 管理系统执行状态和reactor
 */

import { defineState, ReactorRoot, State, NO_PROXY } from '@dl-engine/engine-state'
import { SystemUUID } from './SystemFunctions'

/**
 * 系统状态接口
 */
export interface SystemStateType {
  /** 当前执行的系统UUID */
  currentSystemUUID: SystemUUID
  
  /** 活跃的系统reactors */
  activeSystemReactors: Map<SystemUUID, ReactorRoot>
  
  /** 系统执行队列 */
  systemQueue: SystemUUID[]
  
  /** 是否启用性能分析 */
  performanceProfilingEnabled: boolean
  
  /** 系统执行统计 */
  executionStats: {
    totalExecutions: number
    totalDuration: number
    averageDuration: number
    slowestSystem: {
      uuid: SystemUUID | null
      duration: number
    }
    fastestSystem: {
      uuid: SystemUUID | null
      duration: number
    }
  }
  
  /** 系统错误统计 */
  errorStats: {
    totalErrors: number
    errorsBySystem: Map<SystemUUID, number>
    lastError: {
      system: SystemUUID | null
      error: string | null
      timestamp: number
    }
  }
  
  /** 系统依赖图 */
  dependencyGraph: {
    nodes: SystemUUID[]
    edges: Array<{ from: SystemUUID; to: SystemUUID; type: 'before' | 'after' | 'with' }>
  }
  
  /** 系统分组 */
  systemGroups: {
    input: SystemUUID[]
    simulation: SystemUUID[]
    animation: SystemUUID[]
    presentation: SystemUUID[]
    custom: Map<string, SystemUUID[]>
  }
  
  /** 系统暂停状态 */
  pausedSystems: Set<SystemUUID>
  
  /** 调试模式 */
  debugMode: boolean
  
  /** 详细日志 */
  verboseLogging: boolean
}

/**
 * 系统状态默认值
 */
const SystemStateDefaults: SystemStateType = {
  currentSystemUUID: '' as SystemUUID,
  activeSystemReactors: new Map(),
  systemQueue: [],
  performanceProfilingEnabled: false,
  executionStats: {
    totalExecutions: 0,
    totalDuration: 0,
    averageDuration: 0,
    slowestSystem: {
      uuid: null,
      duration: 0
    },
    fastestSystem: {
      uuid: null,
      duration: Infinity
    }
  },
  errorStats: {
    totalErrors: 0,
    errorsBySystem: new Map(),
    lastError: {
      system: null,
      error: null,
      timestamp: 0
    }
  },
  dependencyGraph: {
    nodes: [],
    edges: []
  },
  systemGroups: {
    input: [],
    simulation: [],
    animation: [],
    presentation: [],
    custom: new Map()
  },
  pausedSystems: new Set(),
  debugMode: false,
  verboseLogging: false
}

/**
 * 系统状态定义
 */
export const SystemState = defineState({
  name: 'DLEngine.System',
  initial: () => SystemStateDefaults
})

// 系统状态操作函数
export const SystemStateActions = {
    /**
     * 设置当前系统UUID
     */
    setCurrentSystemUUID: (state, systemUUID: SystemUUID) => {
      state.currentSystemUUID.set(systemUUID)
    },
    
    /**
     * 添加活跃reactor
     */
    addActiveReactor: (state, systemUUID: SystemUUID, reactor: ReactorRoot) => {
      state.activeSystemReactors.get(NO_PROXY).set(systemUUID, reactor)
    },
    
    /**
     * 移除活跃reactor
     */
    removeActiveReactor: (state, systemUUID: SystemUUID) => {
      const reactors = state.activeSystemReactors.get(NO_PROXY)
      const reactor = reactors.get(systemUUID)
      if (reactor) {
        reactor.stop()
        reactors.delete(systemUUID)
      }
    },
    
    /**
     * 设置性能分析状态
     */
    setPerformanceProfiling: (state, enabled: boolean) => {
      state.performanceProfilingEnabled.set(enabled)
    },
    
    /**
     * 更新执行统计
     */
    updateExecutionStats: (state, systemUUID: SystemUUID, duration: number) => {
      const stats = state.executionStats
      stats.totalExecutions.set(stats.totalExecutions.value + 1)
      stats.totalDuration.set(stats.totalDuration.value + duration)
      stats.averageDuration.set(stats.totalDuration.value / stats.totalExecutions.value)
      
      // 更新最慢系统
      if (duration > stats.slowestSystem.duration.value) {
        stats.slowestSystem.uuid.set(systemUUID)
        stats.slowestSystem.duration.set(duration)
      }
      
      // 更新最快系统
      if (duration < stats.fastestSystem.duration.value) {
        stats.fastestSystem.uuid.set(systemUUID)
        stats.fastestSystem.duration.set(duration)
      }
    },
    
    /**
     * 记录系统错误
     */
    recordSystemError: (state, systemUUID: SystemUUID, error: string) => {
      const errorStats = state.errorStats
      errorStats.totalErrors.set(errorStats.totalErrors.value + 1)
      
      const systemErrors = errorStats.errorsBySystem.get(NO_PROXY)
      const currentCount = systemErrors.get(systemUUID) || 0
      systemErrors.set(systemUUID, currentCount + 1)
      
      errorStats.lastError.system.set(systemUUID)
      errorStats.lastError.error.set(error)
      errorStats.lastError.timestamp.set(Date.now())
    },
    
    /**
     * 更新依赖图
     */
    updateDependencyGraph: (state, nodes: SystemUUID[], edges: SystemStateType['dependencyGraph']['edges']) => {
      state.dependencyGraph.nodes.set(nodes)
      state.dependencyGraph.edges.set(edges)
    },
    
    /**
     * 添加系统到分组
     */
    addSystemToGroup: (state, group: keyof SystemStateType['systemGroups'] | string, systemUUID: SystemUUID) => {
      if (group === 'custom') {
        throw new Error('Use addSystemToCustomGroup for custom groups')
      }
      
      if (group in state.systemGroups.value && group !== 'custom') {
        const groupArray = state.systemGroups[group as keyof Omit<SystemStateType['systemGroups'], 'custom'>]
        if (!groupArray.value.includes(systemUUID)) {
          groupArray.set([...groupArray.value, systemUUID])
        }
      }
    },
    
    /**
     * 添加系统到自定义分组
     */
    addSystemToCustomGroup: (state, groupName: string, systemUUID: SystemUUID) => {
      const customGroups = state.systemGroups.custom.get(NO_PROXY)
      const group = customGroups.get(groupName) || []
      if (!group.includes(systemUUID)) {
        customGroups.set(groupName, [...group, systemUUID])
      }
    },
    
    /**
     * 从分组移除系统
     */
    removeSystemFromGroup: (state, group: keyof SystemStateType['systemGroups'] | string, systemUUID: SystemUUID) => {
      if (group === 'custom') {
        throw new Error('Use removeSystemFromCustomGroup for custom groups')
      }
      
      if (group in state.systemGroups.value && group !== 'custom') {
        const groupArray = state.systemGroups[group as keyof Omit<SystemStateType['systemGroups'], 'custom'>]
        groupArray.set(groupArray.value.filter((uuid: SystemUUID) => uuid !== systemUUID))
      }
    },
    
    /**
     * 从自定义分组移除系统
     */
    removeSystemFromCustomGroup: (state: State<SystemStateType>, groupName: string, systemUUID: SystemUUID) => {
      const customGroups = state.systemGroups.custom.get(NO_PROXY)
      const group = customGroups.get(groupName)
      if (group) {
        customGroups.set(groupName, group.filter((uuid: SystemUUID) => uuid !== systemUUID))
      }
    },
    
    /**
     * 暂停系统
     */
    pauseSystem: (state: State<SystemStateType>, systemUUID: SystemUUID) => {
      state.pausedSystems.get(NO_PROXY).add(systemUUID)
    },
    
    /**
     * 恢复系统
     */
    resumeSystem: (state: State<SystemStateType>, systemUUID: SystemUUID) => {
      state.pausedSystems.get(NO_PROXY).delete(systemUUID)
    },
    
    /**
     * 设置调试模式
     */
    setDebugMode: (state: State<SystemStateType>, enabled: boolean) => {
      state.debugMode.set(enabled)
    },
    
    /**
     * 设置详细日志
     */
    setVerboseLogging: (state: State<SystemStateType>, enabled: boolean) => {
      state.verboseLogging.set(enabled)
    },
    
    /**
     * 重置统计
     */
    resetStats: (state: State<SystemStateType>) => {
      state.executionStats.set({
        totalExecutions: 0,
        totalDuration: 0,
        averageDuration: 0,
        slowestSystem: {
          uuid: null,
          duration: 0
        },
        fastestSystem: {
          uuid: null,
          duration: Infinity
        }
      })
      
      state.errorStats.set({
        totalErrors: 0,
        errorsBySystem: new Map(),
        lastError: {
          system: null,
          error: null,
          timestamp: 0
        }
      })
    }
  }

// 需要导入NO_PROXY
import { NO_PROXY } from '@dl-engine/engine-state'

/**
 * 系统状态工具函数
 */
export const SystemStateUtils = {
  /**
   * 获取当前执行的系统
   */
  getCurrentSystem: (): SystemUUID => {
    return SystemState.currentSystemUUID.value
  },
  
  /**
   * 检查系统是否暂停
   */
  isSystemPaused: (systemUUID: SystemUUID): boolean => {
    return SystemState.pausedSystems.value.has(systemUUID)
  },
  
  /**
   * 获取系统分组
   */
  getSystemGroup: (systemUUID: SystemUUID): string | null => {
    const groups = SystemState.systemGroups.value
    
    if (groups.input.includes(systemUUID)) return 'input'
    if (groups.simulation.includes(systemUUID)) return 'simulation'
    if (groups.animation.includes(systemUUID)) return 'animation'
    if (groups.presentation.includes(systemUUID)) return 'presentation'
    
    for (const [groupName, systems] of groups.custom) {
      if (systems.includes(systemUUID)) return groupName
    }
    
    return null
  },
  
  /**
   * 获取系统错误数量
   */
  getSystemErrorCount: (systemUUID: SystemUUID): number => {
    return SystemState.errorStats.errorsBySystem.value.get(systemUUID) || 0
  },
  
  /**
   * 获取性能报告
   */
  getPerformanceReport: () => {
    const stats = SystemState.executionStats.value
    const errorStats = SystemState.errorStats.value
    
    return {
      totalExecutions: stats.totalExecutions,
      totalDuration: stats.totalDuration,
      averageDuration: stats.averageDuration,
      slowestSystem: stats.slowestSystem,
      fastestSystem: stats.fastestSystem,
      totalErrors: errorStats.totalErrors,
      errorRate: stats.totalExecutions > 0 ? errorStats.totalErrors / stats.totalExecutions : 0,
      lastError: errorStats.lastError
    }
  }
}
