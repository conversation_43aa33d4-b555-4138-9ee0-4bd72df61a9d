/**
 * DL-Engine 资产缓存系统
 * 管理资产的内存和持久化缓存
 */

import { Asset, AssetType, AssetStatus, AssetEventType } from './AssetTypes'
import { AssetStateUtils } from './AssetState'

/**
 * 缓存策略
 */
export enum CacheStrategy {
  /** 最近最少使用 */
  LRU = 'lru',
  /** 先进先出 */
  FIFO = 'fifo',
  /** 最近最常使用 */
  LFU = 'lfu',
  /** 基于大小的优先级 */
  SIZE_BASED = 'size_based'
}

/**
 * 缓存配置
 */
export interface CacheConfig {
  /** 最大缓存大小（字节） */
  maxSize: number
  
  /** 缓存策略 */
  strategy: CacheStrategy
  
  /** 默认TTL（毫秒） */
  defaultTTL: number
  
  /** 是否启用持久化缓存 */
  enablePersistence: boolean
  
  /** 持久化存储名称 */
  persistenceKey: string
  
  /** 自动清理间隔（毫秒） */
  cleanupInterval: number
}

/**
 * 缓存项
 */
interface CacheItem {
  asset: Asset
  accessCount: number
  lastAccessed: number
  size: number
  ttl: number
}

/**
 * 资产缓存管理器
 */
export class AssetCache {
  private cache = new Map<string, CacheItem>()
  private config: CacheConfig
  private currentSize = 0
  private cleanupTimer?: NodeJS.Timeout

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 512 * 1024 * 1024, // 512MB
      strategy: CacheStrategy.LRU,
      defaultTTL: 30 * 60 * 1000, // 30分钟
      enablePersistence: true,
      persistenceKey: 'dl-engine-asset-cache',
      cleanupInterval: 60 * 1000, // 1分钟
      ...config
    }

    this.startCleanupTimer()
    this.loadFromPersistence()
  }

  /**
   * 添加资产到缓存
   */
  set(asset: Asset, ttl?: number): boolean {
    const itemTTL = ttl || asset.cacheTTL || this.config.defaultTTL
    const size = this.calculateAssetSize(asset)

    // 检查是否有足够空间
    if (!this.makeSpace(size)) {
      console.warn(`无法缓存资产 ${asset.id}: 空间不足`)
      return false
    }

    const item: CacheItem = {
      asset: { ...asset },
      accessCount: 1,
      lastAccessed: Date.now(),
      size,
      ttl: itemTTL
    }

    // 移除旧项（如果存在）
    if (this.cache.has(asset.id)) {
      this.remove(asset.id)
    }

    this.cache.set(asset.id, item)
    this.currentSize += size

    // 更新状态
    AssetStateUtils.addToCache(asset.id)
    AssetStateUtils.updateAsset(asset.id, { status: AssetStatus.CACHED })
    AssetStateUtils.addEvent({
      type: AssetEventType.CACHED,
      assetId: asset.id,
      asset,
      timestamp: Date.now()
    })

    this.saveToPersistence()
    return true
  }

  /**
   * 从缓存获取资产
   */
  get(assetId: string): Asset | null {
    const item = this.cache.get(assetId)
    if (!item) {
      return null
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.remove(assetId)
      return null
    }

    // 更新访问信息
    item.accessCount++
    item.lastAccessed = Date.now()

    // 更新资产的最后访问时间
    AssetStateUtils.updateAsset(assetId, {
      lastAccessedAt: new Date(),
      refCount: item.asset.refCount + 1
    })

    return { ...item.asset }
  }

  /**
   * 检查资产是否在缓存中
   */
  has(assetId: string): boolean {
    const item = this.cache.get(assetId)
    if (!item) {
      return false
    }

    if (this.isExpired(item)) {
      this.remove(assetId)
      return false
    }

    return true
  }

  /**
   * 从缓存移除资产
   */
  remove(assetId: string): boolean {
    const item = this.cache.get(assetId)
    if (!item) {
      return false
    }

    this.cache.delete(assetId)
    this.currentSize -= item.size

    // 更新状态
    AssetStateUtils.removeFromCache(assetId)
    AssetStateUtils.addEvent({
      type: AssetEventType.UNCACHED,
      assetId,
      timestamp: Date.now()
    })

    this.saveToPersistence()
    return true
  }

  /**
   * 清空缓存
   */
  clear(): void {
    const assetIds = Array.from(this.cache.keys())
    
    this.cache.clear()
    this.currentSize = 0

    // 更新状态
    for (const assetId of assetIds) {
      AssetStateUtils.removeFromCache(assetId)
    }

    this.saveToPersistence()
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      currentSize: this.currentSize,
      maxSize: this.config.maxSize,
      utilization: this.currentSize / this.config.maxSize,
      hitRate: this.calculateHitRate(),
      items: Array.from(this.cache.entries()).map(([id, item]) => ({
        id,
        size: item.size,
        accessCount: item.accessCount,
        lastAccessed: item.lastAccessed,
        ttl: item.ttl,
        type: item.asset.type
      }))
    }
  }

  /**
   * 强制清理过期项
   */
  cleanup(): number {
    const now = Date.now()
    const expiredIds: string[] = []

    for (const [id, item] of this.cache) {
      if (this.isExpired(item)) {
        expiredIds.push(id)
      }
    }

    for (const id of expiredIds) {
      this.remove(id)
    }

    return expiredIds.length
  }

  /**
   * 销毁缓存
   */
  dispose(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }
    this.clear()
  }

  // 私有方法

  /**
   * 计算资产大小
   */
  private calculateAssetSize(asset: Asset): number {
    if (asset.size > 0) {
      return asset.size
    }

    // 估算大小
    switch (asset.type) {
      case AssetType.TEXTURE:
      case AssetType.IMAGE:
        return 1024 * 1024 // 1MB 估算
      case AssetType.MODEL:
        return 5 * 1024 * 1024 // 5MB 估算
      case AssetType.AUDIO:
        return 2 * 1024 * 1024 // 2MB 估算
      case AssetType.VIDEO:
        return 10 * 1024 * 1024 // 10MB 估算
      default:
        return 100 * 1024 // 100KB 估算
    }
  }

  /**
   * 检查项是否过期
   */
  private isExpired(item: CacheItem): boolean {
    return Date.now() - item.lastAccessed > item.ttl
  }

  /**
   * 为新项腾出空间
   */
  private makeSpace(requiredSize: number): boolean {
    if (this.currentSize + requiredSize <= this.config.maxSize) {
      return true
    }

    // 首先清理过期项
    this.cleanup()

    if (this.currentSize + requiredSize <= this.config.maxSize) {
      return true
    }

    // 根据策略移除项
    return this.evictItems(requiredSize)
  }

  /**
   * 根据策略驱逐项
   */
  private evictItems(requiredSize: number): boolean {
    const items = Array.from(this.cache.entries())
    let freedSize = 0

    switch (this.config.strategy) {
      case CacheStrategy.LRU:
        items.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
        break
      case CacheStrategy.LFU:
        items.sort(([, a], [, b]) => a.accessCount - b.accessCount)
        break
      case CacheStrategy.SIZE_BASED:
        items.sort(([, a], [, b]) => b.size - a.size)
        break
      case CacheStrategy.FIFO:
      default:
        // 保持原有顺序
        break
    }

    for (const [id, item] of items) {
      if (freedSize >= requiredSize) {
        break
      }

      this.remove(id)
      freedSize += item.size
    }

    return freedSize >= requiredSize
  }

  /**
   * 计算命中率
   */
  private calculateHitRate(): number {
    if (this.cache.size === 0) {
      return 0
    }

    const totalAccess = Array.from(this.cache.values())
      .reduce((sum, item) => sum + item.accessCount, 0)
    
    return totalAccess > 0 ? this.cache.size / totalAccess : 0
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 保存到持久化存储
   */
  private saveToPersistence(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return
    }

    try {
      const data = {
        items: Array.from(this.cache.entries()).map(([id, item]) => ({
          id,
          asset: item.asset,
          accessCount: item.accessCount,
          lastAccessed: item.lastAccessed,
          size: item.size,
          ttl: item.ttl
        })),
        currentSize: this.currentSize,
        timestamp: Date.now()
      }

      localStorage.setItem(this.config.persistenceKey, JSON.stringify(data))
    } catch (error) {
      console.warn('无法保存缓存到持久化存储:', error)
    }
  }

  /**
   * 从持久化存储加载
   */
  private loadFromPersistence(): void {
    if (!this.config.enablePersistence || typeof localStorage === 'undefined') {
      return
    }

    try {
      const data = localStorage.getItem(this.config.persistenceKey)
      if (!data) {
        return
      }

      const parsed = JSON.parse(data)
      const now = Date.now()

      for (const itemData of parsed.items) {
        // 检查是否过期
        if (now - itemData.lastAccessed > itemData.ttl) {
          continue
        }

        const item: CacheItem = {
          asset: itemData.asset,
          accessCount: itemData.accessCount,
          lastAccessed: itemData.lastAccessed,
          size: itemData.size,
          ttl: itemData.ttl
        }

        this.cache.set(itemData.id, item)
        this.currentSize += item.size

        // 更新状态
        AssetStateUtils.addToCache(itemData.id)
      }

      console.log(`从持久化存储加载了 ${this.cache.size} 个缓存项`)
    } catch (error) {
      console.warn('无法从持久化存储加载缓存:', error)
    }
  }
}
