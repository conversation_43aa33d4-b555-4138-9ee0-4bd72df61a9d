{"fileNames": [], "fileInfos": [], "root": [], "options": {"allowImportingTsExtensions": false, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "jsx": 4, "module": 99, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 9}, "version": "5.6.3"}