/**
 * DL-Engine 推荐系统测试
 */

import { RecommendationEngine, RecommendationContext } from '../RecommendationEngine'
import { ContentRecommender, ContentMetadata, UserLearningProfile } from '../ContentRecommender'
import { CollaborativeFiltering, UserBehavior } from '../CollaborativeFiltering'
import { RecommendationService } from '../RecommendationService'

describe('推荐系统测试', () => {
  let recommendationEngine: RecommendationEngine
  let contentRecommender: ContentRecommender
  let collaborativeFiltering: CollaborativeFiltering
  let recommendationService: RecommendationService
  
  beforeEach(() => {
    recommendationEngine = new RecommendationEngine()
    contentRecommender = new ContentRecommender()
    collaborativeFiltering = new CollaborativeFiltering()
    recommendationService = new RecommendationService()
  })
  
  describe('推荐引擎基础功能', () => {
    test('应该能够创建推荐引擎实例', () => {
      expect(recommendationEngine).toBeInstanceOf(RecommendationEngine)
      expect(recommendationEngine.getConfig()).toBeDefined()
    })
    
    test('应该能够更新配置', () => {
      const newConfig = { limit: 20, minConfidence: 0.5 }
      recommendationEngine.updateConfig(newConfig)
      
      const config = recommendationEngine.getConfig()
      expect(config.limit).toBe(20)
      expect(config.minConfidence).toBe(0.5)
    })
    
    test('应该能够生成推荐', async () => {
      const context: RecommendationContext = {
        userId: 'test_user' as any,
        learningGoals: ['学习编程'],
        difficultyPreference: 'medium'
      }
      
      const result = await recommendationEngine.generateRecommendations(context)
      
      expect(result).toBeDefined()
      expect(result.items).toBeInstanceOf(Array)
      expect(result.total).toBeGreaterThanOrEqual(0)
      expect(result.generatedAt).toBeInstanceOf(Date)
      expect(result.processingTime).toBeGreaterThan(0)
    })
  })
  
  describe('内容推荐器功能', () => {
    let sampleContent: ContentMetadata
    let sampleUserProfile: UserLearningProfile
    
    beforeEach(() => {
      sampleContent = {
        id: 'test_content_001',
        title: '测试内容',
        description: '这是一个测试内容',
        type: 'video',
        difficulty: 3,
        topics: ['javascript', 'programming'],
        skills: ['javascript'],
        estimatedDuration: 120,
        prerequisites: [],
        learningObjectives: ['学习 JavaScript'],
        qualityScore: 4.5,
        popularity: 80,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      
      sampleUserProfile = {
        userId: 'test_user' as any,
        skillLevels: { 'javascript': 2, 'programming': 3 },
        preferences: {
          contentTypes: ['video', 'interactive'],
          difficultyRange: [2, 4],
          sessionDuration: 120,
          learningStyle: 'visual'
        },
        learningHistory: {
          completedContent: [],
          inProgressContent: [],
          bookmarkedContent: [],
          skippedContent: []
        },
        learningGoals: ['学习编程'],
        lastActiveAt: new Date()
      }
    })
    
    test('应该能够添加内容', () => {
      expect(() => {
        contentRecommender.addContent(sampleContent)
      }).not.toThrow()
    })
    
    test('应该能够更新用户档案', () => {
      expect(() => {
        contentRecommender.updateUserProfile(sampleUserProfile)
      }).not.toThrow()
    })
    
    test('应该能够生成内容推荐', async () => {
      contentRecommender.addContent(sampleContent)
      contentRecommender.updateUserProfile(sampleUserProfile)
      
      const context: RecommendationContext = {
        userId: 'test_user' as any,
        learningGoals: ['学习编程']
      }
      
      const result = await contentRecommender.generateRecommendations(context)
      
      expect(result).toBeDefined()
      expect(result.items).toBeInstanceOf(Array)
      expect(result.processingTime).toBeGreaterThan(0)
    })
    
    test('应该能够更新用户反馈', async () => {
      contentRecommender.updateUserProfile(sampleUserProfile)
      
      await expect(
        contentRecommender.updateFeedback('test_user' as any, 'test_content_001', 'like')
      ).resolves.not.toThrow()
    })
    
    test('应该能够解释推荐', async () => {
      contentRecommender.addContent(sampleContent)
      contentRecommender.updateUserProfile(sampleUserProfile)
      
      const explanation = await contentRecommender.explainRecommendation(
        'test_user' as any, 
        'test_content_001'
      )
      
      expect(explanation).toBeDefined()
      expect(typeof explanation).toBe('string')
      expect(explanation.length).toBeGreaterThan(0)
    })
  })
  
  describe('协同过滤功能', () => {
    let sampleBehaviors: UserBehavior[]
    
    beforeEach(() => {
      sampleBehaviors = [
        {
          userId: 'user_001' as any,
          itemId: 'item_001',
          actionType: 'view',
          value: 3,
          timestamp: new Date(),
          context: { deviceType: 'desktop' }
        },
        {
          userId: 'user_001' as any,
          itemId: 'item_002',
          actionType: 'like',
          value: 4,
          timestamp: new Date(),
          context: { deviceType: 'mobile' }
        },
        {
          userId: 'user_002' as any,
          itemId: 'item_001',
          actionType: 'complete',
          value: 5,
          timestamp: new Date(),
          context: { deviceType: 'tablet' }
        }
      ]
    })
    
    test('应该能够添加用户行为', () => {
      expect(() => {
        collaborativeFiltering.addUserBehavior(sampleBehaviors[0])
      }).not.toThrow()
    })
    
    test('应该能够批量添加用户行为', () => {
      expect(() => {
        collaborativeFiltering.addUserBehaviors(sampleBehaviors)
      }).not.toThrow()
    })
    
    test('应该能够生成基于用户的推荐', async () => {
      collaborativeFiltering.addUserBehaviors(sampleBehaviors)
      
      const recommendations = await collaborativeFiltering.getUserBasedRecommendations(
        'user_001' as any,
        5
      )
      
      expect(recommendations).toBeInstanceOf(Array)
      expect(recommendations.length).toBeGreaterThanOrEqual(0)
    })
    
    test('应该能够生成基于项目的推荐', async () => {
      collaborativeFiltering.addUserBehaviors(sampleBehaviors)
      
      const recommendations = await collaborativeFiltering.getItemBasedRecommendations(
        'user_001' as any,
        5
      )
      
      expect(recommendations).toBeInstanceOf(Array)
      expect(recommendations.length).toBeGreaterThanOrEqual(0)
    })
    
    test('应该能够生成混合推荐', async () => {
      collaborativeFiltering.addUserBehaviors(sampleBehaviors)
      
      const recommendations = await collaborativeFiltering.getHybridRecommendations(
        'user_001' as any,
        5
      )
      
      expect(recommendations).toBeInstanceOf(Array)
      expect(recommendations.length).toBeGreaterThanOrEqual(0)
    })
    
    test('应该能够获取统计信息', () => {
      collaborativeFiltering.addUserBehaviors(sampleBehaviors)
      
      const stats = collaborativeFiltering.getStatistics()
      
      expect(stats).toBeDefined()
      expect(stats.totalUsers).toBeGreaterThan(0)
      expect(stats.totalItems).toBeGreaterThan(0)
      expect(stats.totalBehaviors).toBeGreaterThan(0)
      expect(stats.sparsity).toBeGreaterThanOrEqual(0)
      expect(stats.sparsity).toBeLessThanOrEqual(1)
    })
  })
  
  describe('推荐服务功能', () => {
    test('应该能够创建推荐服务实例', () => {
      expect(recommendationService).toBeInstanceOf(RecommendationService)
    })
    
    test('应该能够获取推荐', async () => {
      const request = {
        userId: 'test_user' as any,
        type: 'content' as const,
        limit: 5
      }
      
      const response = await recommendationService.getRecommendations(request)
      
      expect(response).toBeDefined()
      expect(response.recommendations).toBeInstanceOf(Array)
      expect(response.total).toBeGreaterThanOrEqual(0)
      expect(response.requestId).toBeDefined()
      expect(response.processingTime).toBeGreaterThan(0)
      expect(response.generatedAt).toBeInstanceOf(Date)
    })
    
    test('应该能够记录用户行为', async () => {
      const behavior: UserBehavior = {
        userId: 'test_user' as any,
        itemId: 'test_item',
        actionType: 'view',
        value: 3,
        timestamp: new Date()
      }
      
      await expect(
        recommendationService.recordUserBehavior(behavior)
      ).resolves.not.toThrow()
    })
    
    test('应该能够获取推荐解释', async () => {
      const explanation = await recommendationService.explainRecommendation(
        'test_user' as any,
        'test_item'
      )
      
      expect(explanation).toBeDefined()
      expect(typeof explanation).toBe('string')
    })
    
    test('应该能够获取统计信息', () => {
      const stats = recommendationService.getStatistics()
      
      expect(stats).toBeDefined()
      expect(stats.cacheSize).toBeGreaterThanOrEqual(0)
      expect(stats.totalRequests).toBeGreaterThanOrEqual(0)
    })
    
    test('应该能够清除缓存', () => {
      expect(() => {
        recommendationService.clearCache()
      }).not.toThrow()
    })
    
    test('应该能够清除用户缓存', () => {
      expect(() => {
        recommendationService.clearUserCache('test_user' as any)
      }).not.toThrow()
    })
  })
  
  describe('缓存功能', () => {
    test('推荐结果应该被缓存', async () => {
      const request = {
        userId: 'test_user' as any,
        type: 'content' as const,
        limit: 5
      }
      
      // 第一次请求
      const response1 = await recommendationService.getRecommendations(request)
      expect(response1.fromCache).toBe(false)
      
      // 第二次请求应该来自缓存
      const response2 = await recommendationService.getRecommendations(request)
      expect(response2.fromCache).toBe(true)
      expect(response2.requestId).not.toBe(response1.requestId) // 请求ID应该不同
    })
    
    test('用户行为更新应该清除相关缓存', async () => {
      const request = {
        userId: 'test_user' as any,
        type: 'content' as const,
        limit: 5
      }
      
      // 第一次请求
      await recommendationService.getRecommendations(request)
      
      // 记录用户行为
      const behavior: UserBehavior = {
        userId: 'test_user' as any,
        itemId: 'test_item',
        actionType: 'like',
        value: 4,
        timestamp: new Date()
      }
      await recommendationService.recordUserBehavior(behavior)
      
      // 再次请求应该重新生成
      const response = await recommendationService.getRecommendations(request)
      expect(response.fromCache).toBe(false)
    })
  })
})
