/**
 * DL-Engine 八叉树实现
 * 高效的3D空间分区数据结构
 */

import { Entity } from '@dl-engine/engine-ecs'
import { PhysicsVector3 } from '../types/PhysicsTypes'
import { SpatialIndexItem } from './SpatialIndex'

/**
 * 八叉树节点
 */
export interface OctreeNode {
  /** 节点边界 */
  bounds: {
    center: PhysicsVector3
    halfSize: PhysicsVector3
  }
  
  /** 节点中的项目 */
  items: SpatialIndexItem[]
  
  /** 子节点（8个） */
  children?: OctreeNode[]
  
  /** 是否为叶子节点 */
  isLeaf: boolean
  
  /** 节点深度 */
  depth: number
}

/**
 * 八叉树配置
 */
export interface OctreeConfig {
  /** 最大深度 */
  maxDepth: number
  
  /** 每个节点最大项目数 */
  maxItemsPerNode: number
  
  /** 最小节点尺寸 */
  minNodeSize: number
}

/**
 * 八叉树实现
 */
export class Octree {
  private root: OctreeNode
  private config: OctreeConfig
  
  constructor(center: PhysicsVector3, halfSize: PhysicsVector3, config?: Partial<OctreeConfig>) {
    this.config = {
      maxDepth: 8,
      maxItemsPerNode: 10,
      minNodeSize: 1,
      ...config
    }
    
    this.root = {
      bounds: { center, halfSize },
      items: [],
      isLeaf: true,
      depth: 0
    }
  }
  
  /**
   * 插入项目
   */
  insert(item: SpatialIndexItem): boolean {
    return this.insertIntoNode(this.root, item)
  }
  
  /**
   * 移除项目
   */
  remove(entity: Entity): boolean {
    return this.removeFromNode(this.root, entity)
  }
  
  /**
   * 查询范围内的项目
   */
  queryRange(center: PhysicsVector3, halfSize: PhysicsVector3): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    this.queryRangeInNode(this.root, center, halfSize, results)
    return results
  }
  
  /**
   * 查询球形范围内的项目
   */
  querySphere(center: PhysicsVector3, radius: number): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    this.querySphereInNode(this.root, center, radius, results)
    return results
  }
  
  /**
   * 查询射线相交的项目
   */
  queryRay(origin: PhysicsVector3, direction: PhysicsVector3, maxDistance: number): SpatialIndexItem[] {
    const results: SpatialIndexItem[] = []
    this.queryRayInNode(this.root, origin, direction, maxDistance, results)
    return results
  }
  
  /**
   * 清空八叉树
   */
  clear(): void {
    this.root = {
      bounds: this.root.bounds,
      items: [],
      isLeaf: true,
      depth: 0
    }
  }
  
  /**
   * 获取统计信息
   */
  getStats(): { nodeCount: number; itemCount: number; maxDepth: number } {
    const stats = { nodeCount: 0, itemCount: 0, maxDepth: 0 }
    this.collectStats(this.root, stats)
    return stats
  }
  
  /**
   * 在节点中插入项目
   */
  private insertIntoNode(node: OctreeNode, item: SpatialIndexItem): boolean {
    // 检查项目是否在节点边界内
    if (!this.pointInBounds(item.position, node.bounds)) {
      return false
    }
    
    if (node.isLeaf) {
      node.items.push(item)
      
      // 检查是否需要分割节点
      if (node.items.length > this.config.maxItemsPerNode && 
          node.depth < this.config.maxDepth &&
          this.canSplitNode(node)) {
        this.splitNode(node)
      }
      
      return true
    } else {
      // 递归插入到子节点
      if (node.children) {
        for (const child of node.children) {
          if (this.insertIntoNode(child, item)) {
            return true
          }
        }
      }
    }
    
    return false
  }
  
  /**
   * 从节点中移除项目
   */
  private removeFromNode(node: OctreeNode, entity: Entity): boolean {
    if (node.isLeaf) {
      const index = node.items.findIndex(item => item.entity === entity)
      if (index !== -1) {
        node.items.splice(index, 1)
        return true
      }
    } else if (node.children) {
      for (const child of node.children) {
        if (this.removeFromNode(child, entity)) {
          return true
        }
      }
    }
    
    return false
  }
  
  /**
   * 在节点中查询范围
   */
  private queryRangeInNode(
    node: OctreeNode, 
    center: PhysicsVector3, 
    halfSize: PhysicsVector3, 
    results: SpatialIndexItem[]
  ): void {
    // 检查查询范围是否与节点边界相交
    if (!this.boundsIntersect(node.bounds, { center, halfSize })) {
      return
    }
    
    if (node.isLeaf) {
      for (const item of node.items) {
        if (this.pointInRange(item.position, center, halfSize)) {
          results.push(item)
        }
      }
    } else if (node.children) {
      for (const child of node.children) {
        this.queryRangeInNode(child, center, halfSize, results)
      }
    }
  }
  
  /**
   * 在节点中查询球形范围
   */
  private querySphereInNode(
    node: OctreeNode, 
    center: PhysicsVector3, 
    radius: number, 
    results: SpatialIndexItem[]
  ): void {
    // 检查球形是否与节点边界相交
    if (!this.sphereIntersectsBounds(center, radius, node.bounds)) {
      return
    }
    
    if (node.isLeaf) {
      for (const item of node.items) {
        const distance = this.distance(center, item.position)
        if (distance <= radius) {
          results.push(item)
        }
      }
    } else if (node.children) {
      for (const child of node.children) {
        this.querySphereInNode(child, center, radius, results)
      }
    }
  }
  
  /**
   * 在节点中查询射线
   */
  private queryRayInNode(
    node: OctreeNode, 
    origin: PhysicsVector3, 
    direction: PhysicsVector3, 
    maxDistance: number, 
    results: SpatialIndexItem[]
  ): void {
    // 检查射线是否与节点边界相交
    if (!this.rayIntersectsBounds(origin, direction, maxDistance, node.bounds)) {
      return
    }
    
    if (node.isLeaf) {
      // 简单的点检测（实际应用中可能需要更复杂的碰撞检测）
      for (const item of node.items) {
        results.push(item)
      }
    } else if (node.children) {
      for (const child of node.children) {
        this.queryRayInNode(child, origin, direction, maxDistance, results)
      }
    }
  }
  
  /**
   * 分割节点
   */
  private splitNode(node: OctreeNode): void {
    const bounds = node.bounds
    const quarterSize = {
      x: bounds.halfSize.x / 2,
      y: bounds.halfSize.y / 2,
      z: bounds.halfSize.z / 2
    }
    
    // 创建8个子节点
    node.children = []
    
    for (let x = -1; x <= 1; x += 2) {
      for (let y = -1; y <= 1; y += 2) {
        for (let z = -1; z <= 1; z += 2) {
          const childCenter = {
            x: bounds.center.x + x * quarterSize.x,
            y: bounds.center.y + y * quarterSize.y,
            z: bounds.center.z + z * quarterSize.z
          }
          
          node.children.push({
            bounds: {
              center: childCenter,
              halfSize: quarterSize
            },
            items: [],
            isLeaf: true,
            depth: node.depth + 1
          })
        }
      }
    }
    
    // 重新分配项目到子节点
    const items = node.items
    node.items = []
    node.isLeaf = false
    
    for (const item of items) {
      let inserted = false
      for (const child of node.children) {
        if (this.insertIntoNode(child, item)) {
          inserted = true
          break
        }
      }
      
      // 如果无法插入到任何子节点，保留在当前节点
      if (!inserted) {
        node.items.push(item)
      }
    }
  }
  
  /**
   * 检查是否可以分割节点
   */
  private canSplitNode(node: OctreeNode): boolean {
    return Math.min(node.bounds.halfSize.x, node.bounds.halfSize.y, node.bounds.halfSize.z) > this.config.minNodeSize
  }
  
  /**
   * 检查点是否在边界内
   */
  private pointInBounds(point: PhysicsVector3, bounds: { center: PhysicsVector3; halfSize: PhysicsVector3 }): boolean {
    return Math.abs(point.x - bounds.center.x) <= bounds.halfSize.x &&
           Math.abs(point.y - bounds.center.y) <= bounds.halfSize.y &&
           Math.abs(point.z - bounds.center.z) <= bounds.halfSize.z
  }
  
  /**
   * 检查点是否在查询范围内
   */
  private pointInRange(point: PhysicsVector3, center: PhysicsVector3, halfSize: PhysicsVector3): boolean {
    return Math.abs(point.x - center.x) <= halfSize.x &&
           Math.abs(point.y - center.y) <= halfSize.y &&
           Math.abs(point.z - center.z) <= halfSize.z
  }
  
  /**
   * 检查两个边界是否相交
   */
  private boundsIntersect(
    bounds1: { center: PhysicsVector3; halfSize: PhysicsVector3 },
    bounds2: { center: PhysicsVector3; halfSize: PhysicsVector3 }
  ): boolean {
    return Math.abs(bounds1.center.x - bounds2.center.x) <= (bounds1.halfSize.x + bounds2.halfSize.x) &&
           Math.abs(bounds1.center.y - bounds2.center.y) <= (bounds1.halfSize.y + bounds2.halfSize.y) &&
           Math.abs(bounds1.center.z - bounds2.center.z) <= (bounds1.halfSize.z + bounds2.halfSize.z)
  }
  
  /**
   * 检查球形是否与边界相交
   */
  private sphereIntersectsBounds(
    center: PhysicsVector3, 
    radius: number, 
    bounds: { center: PhysicsVector3; halfSize: PhysicsVector3 }
  ): boolean {
    const dx = Math.max(0, Math.abs(center.x - bounds.center.x) - bounds.halfSize.x)
    const dy = Math.max(0, Math.abs(center.y - bounds.center.y) - bounds.halfSize.y)
    const dz = Math.max(0, Math.abs(center.z - bounds.center.z) - bounds.halfSize.z)
    
    return dx * dx + dy * dy + dz * dz <= radius * radius
  }
  
  /**
   * 检查射线是否与边界相交
   */
  private rayIntersectsBounds(
    origin: PhysicsVector3, 
    direction: PhysicsVector3, 
    maxDistance: number, 
    bounds: { center: PhysicsVector3; halfSize: PhysicsVector3 }
  ): boolean {
    // 简化的射线-AABB相交检测
    const min = {
      x: bounds.center.x - bounds.halfSize.x,
      y: bounds.center.y - bounds.halfSize.y,
      z: bounds.center.z - bounds.halfSize.z
    }
    const max = {
      x: bounds.center.x + bounds.halfSize.x,
      y: bounds.center.y + bounds.halfSize.y,
      z: bounds.center.z + bounds.halfSize.z
    }
    
    // 使用slab方法进行射线-AABB相交检测
    let tmin = 0
    let tmax = maxDistance
    
    for (const axis of ['x', 'y', 'z'] as const) {
      if (Math.abs(direction[axis]) < 1e-6) {
        // 射线平行于slab
        if (origin[axis] < min[axis] || origin[axis] > max[axis]) {
          return false
        }
      } else {
        const t1 = (min[axis] - origin[axis]) / direction[axis]
        const t2 = (max[axis] - origin[axis]) / direction[axis]
        
        const tNear = Math.min(t1, t2)
        const tFar = Math.max(t1, t2)
        
        tmin = Math.max(tmin, tNear)
        tmax = Math.min(tmax, tFar)
        
        if (tmin > tmax) {
          return false
        }
      }
    }
    
    return tmin <= maxDistance
  }
  
  /**
   * 计算两点间距离
   */
  private distance(a: PhysicsVector3, b: PhysicsVector3): number {
    const dx = a.x - b.x
    const dy = a.y - b.y
    const dz = a.z - b.z
    return Math.sqrt(dx * dx + dy * dy + dz * dz)
  }
  
  /**
   * 收集统计信息
   */
  private collectStats(node: OctreeNode, stats: { nodeCount: number; itemCount: number; maxDepth: number }): void {
    stats.nodeCount++
    stats.itemCount += node.items.length
    stats.maxDepth = Math.max(stats.maxDepth, node.depth)
    
    if (node.children) {
      for (const child of node.children) {
        this.collectStats(child, stats)
      }
    }
  }
}
