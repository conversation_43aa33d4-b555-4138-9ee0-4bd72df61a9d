/**
 * DL-Engine 推荐服务
 * 提供统一的推荐服务接口
 */

import { Entity } from '@etherealengine/ecs'
import { 
  RecommendationEngine, 
  RecommendationItem, 
  RecommendationContext, 
  RecommendationResult 
} from './RecommendationEngine'
import { ContentRecommender, ContentMetadata, UserLearningProfile } from './ContentRecommender'
import { CollaborativeFiltering, UserBehavior } from './CollaborativeFiltering'

/**
 * 推荐服务配置
 */
export interface RecommendationServiceConfig {
  /** 是否启用内容推荐 */
  enableContentRecommendation: boolean
  
  /** 是否启用协同过滤 */
  enableCollaborativeFiltering: boolean
  
  /** 默认推荐数量 */
  defaultLimit: number
  
  /** 推荐结果缓存时间（秒） */
  cacheTimeout: number
  
  /** 是否记录推荐日志 */
  enableLogging: boolean
}

/**
 * 推荐请求
 */
export interface RecommendationRequest {
  /** 用户ID */
  userId: Entity
  
  /** 推荐类型 */
  type: 'content' | 'course' | 'lesson' | 'exercise' | 'path' | 'mixed'
  
  /** 推荐数量 */
  limit?: number
  
  /** 推荐上下文 */
  context?: Partial<RecommendationContext>
  
  /** 排除项目 */
  excludeItems?: string[]
  
  /** 额外参数 */
  params?: Record<string, any>
}

/**
 * 推荐响应
 */
export interface RecommendationResponse {
  /** 推荐项目 */
  recommendations: RecommendationItem[]
  
  /** 总数 */
  total: number
  
  /** 请求ID */
  requestId: string
  
  /** 处理时间 */
  processingTime: number
  
  /** 算法使用情况 */
  algorithmUsage: Record<string, number>
  
  /** 是否来自缓存 */
  fromCache: boolean
  
  /** 生成时间 */
  generatedAt: Date
}

/**
 * 推荐服务主类
 */
export class RecommendationService {
  private config: RecommendationServiceConfig
  private recommendationEngine: RecommendationEngine
  private contentRecommender: ContentRecommender
  private collaborativeFiltering: CollaborativeFiltering
  private requestCache = new Map<string, { response: RecommendationResponse; timestamp: number }>()
  
  constructor(config: Partial<RecommendationServiceConfig> = {}) {
    this.config = {
      enableContentRecommendation: true,
      enableCollaborativeFiltering: true,
      defaultLimit: 10,
      cacheTimeout: 300, // 5分钟
      enableLogging: true,
      ...config
    }
    
    this.recommendationEngine = new RecommendationEngine()
    this.contentRecommender = new ContentRecommender()
    this.collaborativeFiltering = new CollaborativeFiltering()
  }
  
  /**
   * 获取推荐
   */
  async getRecommendations(request: RecommendationRequest): Promise<RecommendationResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()
    
    // 检查缓存
    const cacheKey = this.generateCacheKey(request)
    const cached = this.getCachedResponse(cacheKey)
    if (cached) {
      return {
        ...cached,
        requestId,
        fromCache: true,
        processingTime: Date.now() - startTime
      }
    }
    
    try {
      // 构建推荐上下文
      const context: RecommendationContext = {
        userId: request.userId,
        excludeItems: request.excludeItems || [],
        ...request.context
      }
      
      let recommendations: RecommendationItem[] = []
      const algorithmUsage: Record<string, number> = {}
      
      // 根据推荐类型选择推荐器
      switch (request.type) {
        case 'content':
          if (this.config.enableContentRecommendation) {
            const result = await this.contentRecommender.generateRecommendations(context)
            recommendations = result.items
            Object.assign(algorithmUsage, result.algorithmUsage)
          }
          break
          
        case 'mixed':
          // 混合推荐
          recommendations = await this.generateMixedRecommendations(context, request.limit)
          break
          
        default:
          // 使用默认推荐引擎
          const result = await this.recommendationEngine.generateRecommendations(context)
          recommendations = result.items
          Object.assign(algorithmUsage, result.algorithmUsage)
      }
      
      // 应用过滤和限制
      const filteredRecommendations = this.filterRecommendations(
        recommendations, 
        request.excludeItems || []
      )
      
      const finalRecommendations = filteredRecommendations.slice(
        0, 
        request.limit || this.config.defaultLimit
      )
      
      const response: RecommendationResponse = {
        recommendations: finalRecommendations,
        total: finalRecommendations.length,
        requestId,
        processingTime: Date.now() - startTime,
        algorithmUsage,
        fromCache: false,
        generatedAt: new Date()
      }
      
      // 缓存结果
      this.cacheResponse(cacheKey, response)
      
      // 记录日志
      if (this.config.enableLogging) {
        this.logRecommendation(request, response)
      }
      
      return response
      
    } catch (error) {
      console.error('推荐生成失败:', error)
      throw new Error(`推荐生成失败: ${error.message}`)
    }
  }
  
  /**
   * 记录用户行为
   */
  async recordUserBehavior(behavior: UserBehavior): Promise<void> {
    if (this.config.enableCollaborativeFiltering) {
      this.collaborativeFiltering.addUserBehavior(behavior)
    }
    
    // 更新内容推荐器的用户反馈
    if (this.config.enableContentRecommendation) {
      await this.contentRecommender.updateFeedback(
        behavior.userId,
        behavior.itemId,
        behavior.actionType as any
      )
    }
    
    // 清除相关缓存
    this.clearUserCache(behavior.userId)
  }
  
  /**
   * 批量记录用户行为
   */
  async recordUserBehaviors(behaviors: UserBehavior[]): Promise<void> {
    for (const behavior of behaviors) {
      await this.recordUserBehavior(behavior)
    }
  }
  
  /**
   * 添加内容到推荐系统
   */
  addContent(content: ContentMetadata): void {
    if (this.config.enableContentRecommendation) {
      this.contentRecommender.addContent(content)
    }
  }
  
  /**
   * 更新用户学习档案
   */
  updateUserProfile(profile: UserLearningProfile): void {
    if (this.config.enableContentRecommendation) {
      this.contentRecommender.updateUserProfile(profile)
    }
  }
  
  /**
   * 获取推荐解释
   */
  async explainRecommendation(
    userId: Entity, 
    itemId: string, 
    algorithm?: string
  ): Promise<string> {
    try {
      if (algorithm === 'content_based' && this.config.enableContentRecommendation) {
        return await this.contentRecommender.explainRecommendation(userId, itemId)
      }
      
      return await this.recommendationEngine.explainRecommendation(userId, itemId)
    } catch (error) {
      return '推荐解释暂不可用'
    }
  }
  
  /**
   * 获取推荐统计信息
   */
  getStatistics(): {
    contentRecommender?: any
    collaborativeFiltering?: any
    cacheSize: number
    totalRequests: number
  } {
    const stats: any = {
      cacheSize: this.requestCache.size,
      totalRequests: 0 // TODO: 实现请求计数
    }
    
    if (this.config.enableCollaborativeFiltering) {
      stats.collaborativeFiltering = this.collaborativeFiltering.getStatistics()
    }
    
    return stats
  }
  
  /**
   * 清除缓存
   */
  clearCache(): void {
    this.requestCache.clear()
  }
  
  /**
   * 清除用户相关缓存
   */
  clearUserCache(userId: Entity): void {
    const userPrefix = `user_${userId.toString()}`
    for (const key of this.requestCache.keys()) {
      if (key.includes(userPrefix)) {
        this.requestCache.delete(key)
      }
    }
  }
  
  // 私有方法
  
  /**
   * 生成混合推荐
   */
  private async generateMixedRecommendations(
    context: RecommendationContext,
    limit?: number
  ): Promise<RecommendationItem[]> {
    const recommendations: RecommendationItem[] = []
    const targetLimit = limit || this.config.defaultLimit
    
    // 内容推荐 (40%)
    if (this.config.enableContentRecommendation) {
      try {
        const contentResult = await this.contentRecommender.generateRecommendations({
          ...context,
          additionalParams: { limit: Math.ceil(targetLimit * 0.4) }
        })
        recommendations.push(...contentResult.items)
      } catch (error) {
        console.warn('内容推荐失败:', error)
      }
    }
    
    // 协同过滤推荐 (40%)
    if (this.config.enableCollaborativeFiltering) {
      try {
        const collaborativeRecs = await this.collaborativeFiltering.getHybridRecommendations(
          context.userId,
          Math.ceil(targetLimit * 0.4)
        )
        
        for (const rec of collaborativeRecs) {
          recommendations.push({
            id: rec.itemId,
            type: 'content',
            title: rec.itemId, // TODO: 从内容数据库获取标题
            score: rec.score,
            reason: rec.reason,
            algorithm: 'collaborative_filtering',
            confidence: rec.score,
            generatedAt: new Date()
          })
        }
      } catch (error) {
        console.warn('协同过滤推荐失败:', error)
      }
    }
    
    // 热门推荐 (20%)
    // TODO: 实现热门推荐逻辑
    
    return recommendations
  }
  
  /**
   * 过滤推荐结果
   */
  private filterRecommendations(
    recommendations: RecommendationItem[],
    excludeItems: string[]
  ): RecommendationItem[] {
    return recommendations.filter(rec => !excludeItems.includes(rec.id))
  }
  
  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  /**
   * 生成缓存键
   */
  private generateCacheKey(request: RecommendationRequest): string {
    const key = {
      userId: request.userId.toString(),
      type: request.type,
      limit: request.limit,
      context: request.context,
      excludeItems: request.excludeItems?.sort()
    }
    return `cache_${JSON.stringify(key)}`
  }
  
  /**
   * 获取缓存响应
   */
  private getCachedResponse(cacheKey: string): RecommendationResponse | null {
    const cached = this.requestCache.get(cacheKey)
    if (!cached) return null
    
    const now = Date.now()
    if (now - cached.timestamp > this.config.cacheTimeout * 1000) {
      this.requestCache.delete(cacheKey)
      return null
    }
    
    return cached.response
  }
  
  /**
   * 缓存响应
   */
  private cacheResponse(cacheKey: string, response: RecommendationResponse): void {
    this.requestCache.set(cacheKey, {
      response,
      timestamp: Date.now()
    })
  }
  
  /**
   * 记录推荐日志
   */
  private logRecommendation(
    request: RecommendationRequest,
    response: RecommendationResponse
  ): void {
    console.log(`[推荐服务] 用户 ${request.userId} 请求 ${request.type} 推荐`, {
      requestId: response.requestId,
      recommendationCount: response.total,
      processingTime: response.processingTime,
      algorithmUsage: response.algorithmUsage,
      fromCache: response.fromCache
    })
  }
}
