/**
 * DL-Engine ECS状态管理
 * 管理ECS系统的全局状态
 */

import { defineState, State } from '@dl-engine/engine-state'
import { Timer } from './Timer'

/**
 * ECS系统状态接口
 */
export interface ECSStateType {
  /** 当前帧时间（毫秒） */
  frameTime: number
  
  /** 已运行时间（秒） */
  elapsedSeconds: number
  
  /** 帧间隔时间（秒） */
  deltaSeconds: number
  
  /** 最大帧间隔时间（秒） */
  maxDeltaSeconds: number
  
  /** 模拟时间（毫秒） */
  simulationTime: number
  
  /** 固定时间步长（秒） */
  fixedDeltaSeconds: number
  
  /** 固定时间累积器 */
  fixedTimeAccumulator: number
  
  /** 目标帧率 */
  targetFPS: number
  
  /** 实际帧率 */
  actualFPS: number
  
  /** 帧计数器 */
  frameCount: number
  
  /** 上次FPS计算时间 */
  lastFPSTime: number
  
  /** 定时器实例 */
  timer: Timer | null
  
  /** 是否暂停 */
  isPaused: boolean
  
  /** 是否启用性能分析 */
  performanceProfilingEnabled: boolean
  
  /** 系统执行统计 */
  systemStats: {
    totalExecutionTime: number
    systemCount: number
    averageExecutionTime: number
  }
  
  /** 内存使用统计 */
  memoryStats: {
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  }
}

/**
 * ECS状态默认值
 */
const ECSStateDefaults: ECSStateType = {
  frameTime: 0,
  elapsedSeconds: 0,
  deltaSeconds: 0,
  maxDeltaSeconds: 1 / 30, // 最大30fps
  simulationTime: 0,
  fixedDeltaSeconds: 1 / 60, // 60fps固定时间步长
  fixedTimeAccumulator: 0,
  targetFPS: 60,
  actualFPS: 0,
  frameCount: 0,
  lastFPSTime: 0,
  timer: null,
  isPaused: false,
  performanceProfilingEnabled: false,
  systemStats: {
    totalExecutionTime: 0,
    systemCount: 0,
    averageExecutionTime: 0
  },
  memoryStats: {
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0
  }
}

/**
 * ECS状态定义
 */
export const ECSState = defineState({
  name: 'DLEngine.ECS',
  initial: () => ECSStateDefaults
})

/**
 * ECS状态工具函数
 * 注意：这些函数需要通过getMutableState(ECSState)来访问状态
 */
export const ECSStateUtils = {
  /**
   * 格式化内存大小
   */
  formatMemorySize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  }
}
